package com.knet.payment.strategy;

import com.knet.common.enums.PaymentChannel;
import com.knet.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:30
 * @description: 支付策略工厂
 */
@Slf4j
@Component
public class PaymentStrategyFactory {

    @Resource
    private List<PaymentStrategy> paymentStrategies;

    private final Map<PaymentChannel, PaymentStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * 初始化策略映射
     */
    @PostConstruct
    public void initStrategies() {
        log.info("初始化支付策略工厂，共发现 {} 个支付策略", paymentStrategies.size());
        for (PaymentStrategy strategy : paymentStrategies) {
            PaymentChannel channel = strategy.getSupportedChannel();
            strategyMap.put(channel, strategy);
            log.info("注册支付策略: {} -> {}", channel.getName(), strategy.getClass().getSimpleName());
        }
        log.info("支付策略工厂初始化完成，支持的支付渠道: {}", strategyMap.keySet());
    }

    /**
     * 获取支付策略
     *
     * @param channel 支付渠道
     * @return 支付策略
     */
    public PaymentStrategy getStrategy(PaymentChannel channel) {
        PaymentStrategy strategy = strategyMap.get(channel);
        if (strategy == null) {
            throw new ServiceException("不支持的支付渠道: " + channel.getName());
        }
        return strategy;
    }

    /**
     * 检查是否支持指定的支付渠道
     *
     * @param channel 支付渠道
     * @return 是否支持
     */
    public boolean isSupported(PaymentChannel channel) {
        return strategyMap.containsKey(channel);
    }

    /**
     * 获取所有支持的支付渠道
     *
     * @return 支持的支付渠道列表
     */
    public List<PaymentChannel> getSupportedChannels() {
        return List.copyOf(strategyMap.keySet());
    }
}
