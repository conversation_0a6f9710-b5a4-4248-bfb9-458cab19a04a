package com.knet.payment.strategy.impl;

import com.knet.common.enums.PaymentChannel;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.entity.SysPaymentFlow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 支付宝支付策略
 */
@Slf4j
@Component
class AlipayPaymentStrategy extends AbstractThirdPartyPaymentStrategy {

    @Override
    public PaymentChannel getSupportedChannel() {
        return PaymentChannel.ALIPAY;
    }

    @Override
    protected String callThirdPartyPaymentApi(SysPaymentFlow paymentFlow, CreatePaymentRequest request) {
        log.info("调用支付宝支付接口: paymentId={}, amount={}",
                paymentFlow.getPaymentId(), paymentFlow.getAmount());
        return generateDefaultPaymentUrl(paymentFlow, "alipay");
    }
}

/**
 * 微信支付策略
 */
@Slf4j
@Component
class WechatPayPaymentStrategy extends AbstractThirdPartyPaymentStrategy {

    @Override
    public PaymentChannel getSupportedChannel() {
        return PaymentChannel.WECHAT_PAY;
    }

    @Override
    protected String callThirdPartyPaymentApi(SysPaymentFlow paymentFlow, CreatePaymentRequest request) {
        log.info("调用微信支付接口: paymentId={}, amount={}",
                paymentFlow.getPaymentId(), paymentFlow.getAmount());
        return generateDefaultPaymentUrl(paymentFlow, "wechatpay");
    }
}
