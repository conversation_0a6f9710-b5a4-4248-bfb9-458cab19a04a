package com.knet.payment.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.context.UserContext;
import com.knet.common.dto.message.UserOperationRecordMessage;
import com.knet.common.enums.WalletRecordType;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.NumberUtils;
import com.knet.payment.mapper.SysUserWalletMapper;
import com.knet.payment.model.dto.req.UserBalanceQueryRequest;
import com.knet.payment.model.dto.req.UserDeductRequest;
import com.knet.payment.model.dto.req.UserRechargeRequest;
import com.knet.payment.model.dto.resp.UserBalanceResponse;
import com.knet.payment.model.dto.resp.UserDeductResponse;
import com.knet.payment.model.dto.resp.UserRechargeResponse;
import com.knet.payment.model.entity.SysUserWallet;
import com.knet.payment.mq.producer.PaymentMessageProducer;
import com.knet.payment.service.ISysUserWalletService;
import com.knet.payment.service.ISysWalletRecordService;
import com.knet.payment.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_wallet(用户钱包表)】的数据库操作Service实现
 * @date 2025-03-12 15:02:07
 */
@Slf4j
@Service
public class SysUserWalletServiceImpl extends ServiceImpl<SysUserWalletMapper, SysUserWallet> implements ISysUserWalletService {

    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ISysWalletRecordService sysWalletRecordService;
    @Resource
    private PaymentMessageProducer paymentMessageProducer;
    @Resource
    private ISysWalletRecordService walletRecordService;


    @Override
    public UserBalanceResponse getUserBalance(UserBalanceQueryRequest request) {
        if (request == null || request.getUserId() == null) {
            throw new ServiceException("用户ID不能为空");
        }
        log.info("查询用户余额, userId: {}", request.getUserId());
        LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserWallet::getUserId, request.getUserId());
        SysUserWallet userWallet = this.getOne(queryWrapper);
        if (BeanUtil.isEmpty(userWallet)) {
            log.info("用户钱包不存在, userId: {}", request.getUserId());
            return new UserBalanceResponse().init(request.getUserId());
        }
        String balanceStr = userWallet.getFormattedBalanceStr();
        String frozenBalanceStr = userWallet.getFormattedFrozenBalanceStr();
        String totalBalanceStr = userWallet.getFormattedTotalBalanceStr();
        log.info("查询用户余额成功, userId: {}, balance: {}, frozenBalance: {}, totalBalance: {}",
                request.getUserId(), balanceStr, frozenBalanceStr, totalBalanceStr);
        return UserBalanceResponse.builder()
                .userId(request.getUserId())
                .balance(balanceStr)
                .frozenBalance(frozenBalanceStr)
                .totalBalance(totalBalanceStr)
                .build();
    }

    /**
     * 用户充值
     *
     * @param request 充值请求
     * @return 充值响应
     */
    @DistributedLock(key = "'wallet:recharge:'+#request.hashCode()", expire = 2)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserRechargeResponse recharge(UserRechargeRequest request) {
        UserRechargeRequest.checkRechargeRequest(request);
        Long userId = request.getUserId();
        Long operationId = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        BigDecimal amount = request.getAmount();
        log.info("用户充值开始: userId={}, amount={}", userId, amount);
        try {
            // 查询或创建用户钱包
            LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysUserWallet::getUserId, userId);
            SysUserWallet userWallet = this.getOne(queryWrapper);
            if (BeanUtil.isEmpty(userWallet)) {
                userWallet = new SysUserWallet(userId, amount, BigDecimal.ZERO);
                boolean saved = this.save(userWallet);
                if (!saved) {
                    throw new ServiceException("钱包创建失败");
                }
                log.info("创建新钱包成功: userId={}, balance={}", userId, amount);
            } else {
                // 更新钱包余额
                BigDecimal newBalance = userWallet.getBalance().add(amount);
                LambdaUpdateWrapper<SysUserWallet> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper
                        .eq(SysUserWallet::getUserId, userId)
                        .set(SysUserWallet::getBalance, newBalance);
                boolean updated = this.update(updateWrapper);
                if (!updated) {
                    throw new ServiceException("钱包余额更新失败");
                }
                userWallet.setBalance(newBalance);
                log.info("钱包余额更新成功: userId={}, oldBalance={}, newBalance={}", userId, userWallet.getBalance().subtract(amount), newBalance);
            }
            String recordId = sysWalletRecordService.createWalletRecord(userId, amount, WalletRecordType.RECHARGE, null, null, request.getRemark());
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            // 发送用户操作记录消息
            try {
                String operationDesc = String.format("用户充值%s美元", NumberUtils.formatDecimal(amount));
                UserOperationRecordMessage recordMessage = UserOperationRecordMessage.createWalletRechargeRecord(
                        userId,
                        operationId,
                        operationDesc,
                        recordId,
                        request.getRemark(),
                        NumberUtils.formatDecimal(amount),
                        userWallet.getFormattedBalanceStr());
                paymentMessageProducer.sendUserOperationRecord(recordMessage);
                log.info("用户操作记录消息发送成功: userId={}, recordId={}", userId, recordId);
            } catch (Exception e) {
                log.warn("用户操作记录消息发送失败: userId={}, error={}", userId, e.getMessage());
            }
            log.info("用户充值成功: userId={}, amount={}, recordId={}", userId, amount, recordId);
            return UserRechargeResponse.builder()
                    .recordId(recordId)
                    .userId(userId)
                    .amount(NumberUtils.formatDecimal(amount))
                    .balance(userWallet.getFormattedBalanceStr())
                    .status("SUCCESS")
                    .rechargeTime(currentTime)
                    .build();
        } catch (Exception e) {
            log.error("用户充值失败: userId={}, amount={}, error={}", userId, amount, e.getMessage(), e);
            throw new ServiceException("充值失败: " + e.getMessage());
        }
    }


    /**
     * 扣减钱包余额
     *
     * @param userId 用户id
     * @param amount 扣减金额
     * @return 是否扣减成功
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean deductionBalance(Long userId, BigDecimal amount) {
        LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserWallet::getUserId, userId);
        SysUserWallet userWallet = this.getOne(queryWrapper);
        if (BeanUtil.isEmpty(userWallet)) {
            throw new ServiceException("用户钱包不存在");
        }
        if (userWallet.getBalance().compareTo(amount) < 0) {
            throw new ServiceException("钱包余额不足");
        }
        // 扣减钱包余额
        BigDecimal newBalance = userWallet.getBalance().subtract(amount);
        userWallet.setBalance(newBalance);
        LambdaUpdateWrapper<SysUserWallet> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysUserWallet::getId, userWallet.getId())
                .set(SysUserWallet::getBalance, newBalance);
        return this.update(updateWrapper);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public UserDeductResponse deduct(UserDeductRequest request) {
        UserDeductRequest.checkDeductRequest(request);
        Long userId = request.getUserId();
        Long operationId = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        BigDecimal amount = request.getAmount();
        log.info("用户扣款开始: userId={}, amount={}", userId, amount);
        try {
            boolean balance = this.deductionBalance(userId, amount);
            if (balance) {
                log.info("用户扣款成功: userId={}, amount={}", userId, amount);
            }
            // 发送用户操作记录消息
            try {
                String recordId = walletRecordService.createWalletRecord(
                        userId, amount, WalletRecordType.DEDUCT,
                        null, null, request.getRemark());
                LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysUserWallet::getUserId, userId);
                SysUserWallet userWallet = this.getOne(queryWrapper);
                String operationDesc = String.format("管理员扣款%s美元", NumberUtils.formatDecimal(amount));
                UserOperationRecordMessage recordMessage = UserOperationRecordMessage.createWalletDeductRecord(
                        userId,
                        operationId,
                        operationDesc,
                        recordId,
                        request.getRemark(),
                        NumberUtils.formatDecimal(amount),
                        userWallet.getFormattedBalanceStr());
                paymentMessageProducer.sendUserOperationRecord(recordMessage);
                log.info("用户操作记录 扣款 消息发送成功: userId={}, recordId={}", userId, recordId);
                return new UserDeductResponse(
                        recordId,
                        userId,
                        NumberUtils.formatDecimal(amount),
                        userWallet.getFormattedBalanceStr(),
                        "SUCCESS",
                        DateUtil.now());
            } catch (Exception e) {
                log.warn("用户操作记录 扣款 消息发送失败: userId={}, error={}", userId, e.getMessage());
            }
        } catch (Exception e) {
            log.warn("用户操作记录  扣款 消息发送失败: userId={}, error={}", userId, e.getMessage());
        }
        return null;
    }
}
