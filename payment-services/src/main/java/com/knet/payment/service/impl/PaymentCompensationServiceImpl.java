package com.knet.payment.service.impl;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.OrderMessage;
import com.knet.common.enums.KnetPaymentGroupStatus;
import com.knet.common.exception.ServiceException;
import com.knet.payment.model.entity.SysPaymentGroup;
import com.knet.payment.service.IPaymentCompensationService;
import com.knet.payment.service.ISysPaymentGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/19 10:10
 * @description: 支付补偿服务实现
 */
@Slf4j
@Service
public class PaymentCompensationServiceImpl implements IPaymentCompensationService {

    @Resource
    private ISysPaymentGroupService paymentGroupService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderTimeout(String messageBody) {
        log.info("支付服务处理订单超时补偿: {}", messageBody);
        OrderMessage message = JSON.parseObject(messageBody, OrderMessage.class);
        try {
            String orderId = message.getOrderId();
            Long userId = message.getUserId();
            SysPaymentGroup paymentGroup = paymentGroupService.getByGroupIdByOrderId(orderId);
            if (paymentGroup == null) {
                log.info("订单对应的支付组不存在，无需处理: orderId={}", orderId);
                return;
            }
            // 检查支付组状态，只有未完成状态的才需要取消
            if (!KnetPaymentGroupStatus.UNFINISHED.equals(paymentGroup.getStatus())) {
                log.info("支付组状态不是未完成，无需处理: orderId={}, groupId={}, status={}",
                        orderId, paymentGroup.getGroupId(), paymentGroup.getStatus());
                return;
            }
            //  将支付组状态更新为已关闭
            paymentGroup.setStatus(KnetPaymentGroupStatus.CLOSED);
            boolean updated = paymentGroupService.updateById(paymentGroup);
            if (!updated) {
                throw new ServiceException("更新支付组状态失败: " + paymentGroup.getGroupId());
            }
            log.info("订单超时补偿处理完成，支付组状态已更新为已关闭: orderId={}, groupId={}, userId={}",
                    orderId, paymentGroup.getGroupId(), userId);
        } catch (Exception e) {
            log.error("处理订单超时补偿异常: orderId={}, error={}", message.getOrderId(), e.getMessage(), e);
            throw new ServiceException("处理订单超时补偿异常: " + e.getMessage());
        }
    }
}
