package com.knet.payment.service;

import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.req.PayPaymentRequest;
import com.knet.payment.model.dto.req.RefundPaymentRequest;
import com.knet.payment.model.dto.resp.CreatePaymentResponse;
import com.knet.payment.model.dto.resp.PayResponse;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:45
 * @description: 支付服务接口
 */
public interface IPaymentService {

    /**
     * 创建支付
     *
     * @param request 创建支付请求
     * @return 创建支付响应
     */
    CreatePaymentResponse createPayment(CreatePaymentRequest request);

    /**
     * 查询支付状态
     *
     * @param paymentId 支付流水ID
     * @return 支付状态信息
     */
    CreatePaymentResponse queryPaymentStatus(String paymentId);

    /**
     * 用户支付
     *
     * @param request 支付请求
     * @return 支付响应
     */
    PayResponse payPayment(PayPaymentRequest request);

    /**
     * 处理库存扣减失败补偿
     *
     * @param messageBody 消息体
     */
    void processInventoryFailed(String messageBody);

    /**
     * 订单退款
     *
     * @param request 退款请求
     */
    void refundPayment(RefundPaymentRequest request);
}
