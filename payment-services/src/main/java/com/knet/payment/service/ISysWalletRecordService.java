package com.knet.payment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.WalletRecordType;
import com.knet.payment.model.dto.req.UserWalletQueryRequest;
import com.knet.payment.model.dto.resp.SysWalletRecordResp;
import com.knet.payment.model.entity.SysWalletRecord;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 针对表【sys_wallet_record(用户钱包记录表)】的数据库操作Service
 * @date 2025-03-12 15:17:41
 */
public interface ISysWalletRecordService extends IService<SysWalletRecord> {

    /**
     * 查询用户钱包记录
     *
     * @param request 用户ID
     * @return 用户钱包记录列表
     */
    IPage<SysWalletRecordResp> findWalletRecordList(UserWalletQueryRequest request);

    /**
     * 创建钱包记录
     *
     * @param userId    用户ID
     * @param amount    变动金额
     * @param type      记录类型
     * @param paymentId 支付流水ID（可选）
     * @param orderId   订单ID（可选）
     * @param remarks   充值备注（可选）
     * @return 记录ID
     */
    String createWalletRecord(Long userId, BigDecimal amount, WalletRecordType type, String paymentId, String orderId, String remarks);
}
