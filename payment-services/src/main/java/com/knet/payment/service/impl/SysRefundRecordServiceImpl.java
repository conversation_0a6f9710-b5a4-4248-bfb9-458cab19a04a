package com.knet.payment.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.payment.mapper.SysRefundRecordMapper;
import com.knet.payment.model.entity.SysRefundRecord;
import com.knet.payment.service.ISysRefundRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【sys_refund_record(退款记录表)】的数据库操作Service实现
 * @date 2025-03-12 15:28:58
 */
@Service
public class SysRefundRecordServiceImpl extends ServiceImpl<SysRefundRecordMapper, SysRefundRecord>
        implements ISysRefundRecordService {
}




