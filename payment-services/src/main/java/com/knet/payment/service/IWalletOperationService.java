package com.knet.payment.service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description: 钱包操作服务接口 - 用于解决循环依赖
 */
public interface IWalletOperationService {

    /**
     * 扣减用户钱包余额
     *
     * @param userId 用户ID
     * @param amount 扣减金额
     * @return 是否扣减成功
     */
    boolean deductBalance(Long userId, BigDecimal amount);

    /**
     * 增加用户钱包余额
     *
     * @param userId 用户ID
     * @param amount 增加金额
     */
    void increaseBalance(Long userId, BigDecimal amount);
}
