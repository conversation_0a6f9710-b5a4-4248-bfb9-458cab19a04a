package com.knet.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.exception.ServiceException;
import com.knet.payment.mapper.SysUserWalletMapper;
import com.knet.payment.model.entity.SysUserWallet;
import com.knet.payment.service.IWalletOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description: 钱包操作服务实现 - 用于解决循环依赖
 */
@Slf4j
@Service
public class WalletOperationServiceImpl implements IWalletOperationService {

    @Resource
    private SysUserWalletMapper sysUserWalletMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean deductBalance(Long userId, BigDecimal amount) {
        log.info("钱包扣款操作开始: userId={}, amount={}", userId, amount);
        // 查询用户钱包
        LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserWallet::getUserId, userId);
        SysUserWallet userWallet = sysUserWalletMapper.selectOne(queryWrapper);
        if (userWallet == null) {
            log.error("用户钱包不存在: userId={}", userId);
            return false;
        }
        // 检查余额是否充足
        if (userWallet.getBalance().compareTo(amount) < 0) {
            log.error("用户钱包余额不足: userId={}, balance={}, deductAmount={}",
                    userId, userWallet.getBalance(), amount);
            return false;
        }
        // 扣减余额
        LambdaUpdateWrapper<SysUserWallet> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysUserWallet::getUserId, userId)
                .setSql("balance = balance - " + amount);
        int updateCount = sysUserWalletMapper.update(null, updateWrapper);
        boolean success = updateCount > 0;
        if (success) {
            log.info("钱包扣款成功: userId={}, amount={}", userId, amount);
        } else {
            log.error("钱包扣款失败: userId={}, amount={}", userId, amount);
        }

        return success;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void increaseBalance(Long userId, BigDecimal amount) {
        log.info("钱包充值操作开始: userId={}, amount={}", userId, amount);

        LambdaQueryWrapper<SysUserWallet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserWallet::getUserId, userId);
        SysUserWallet userWallet = sysUserWalletMapper.selectOne(queryWrapper);
        if (userWallet == null) {
            throw new ServiceException("用户钱包不存在");
        }
        // 更新钱包余额
        BigDecimal newBalance = userWallet.getBalance().add(amount);
        LambdaUpdateWrapper<SysUserWallet> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysUserWallet::getUserId, userId)
                .set(SysUserWallet::getBalance, newBalance);
        int updated = sysUserWalletMapper.update(null, updateWrapper);
        if (updated < 0) {
            throw new ServiceException("退款时钱包余额更新失败");
        }
        log.info("退款更新钱包余额成功: userId={}, oldBalance={}, newBalance={}",
                userId, userWallet.getBalance(), newBalance);
    }
}
