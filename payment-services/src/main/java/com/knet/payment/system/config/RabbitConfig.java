package com.knet.payment.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/5/20 20:05
 * @description: RabbitMQ配置 - 用户操作记录
 */
@Configuration
public class RabbitConfig {

    /**
     * 订单交换机（与order-service共享）
     */
    @Bean
    public TopicExchange orderExchange() {
        return new TopicExchange("order-exchange", true, false);
    }

    /**
     * 支付服务专用的订单队列
     */
    @Bean
    public Queue paymentOrderQueue() {
        return QueueBuilder
                .durable("order-queue.payment-services")
                .withArgument("x-dead-letter-exchange", "payment-service.dlx")
                .withArgument("x-dead-letter-routing-key", "payment.order.*")
                .build();
    }

    /**
     * 支付服务订单队列绑定
     */
    @Bean
    public Binding paymentOrderBinding() {
        return BindingBuilder
                .bind(paymentOrderQueue())
                .to(orderExchange())
                .with("order.*");
    }

    /**
     * 用户操作记录交换机
     * 注意：支付服务只负责发送消息到交换机，队列由用户服务自己管理
     */
    @Bean
    public TopicExchange userOperationExchange() {
        return new TopicExchange("user-operation-exchange", true, false);
    }

    /**
     * 支付服务专用死信交换机
     */
    @Bean
    public DirectExchange paymentDlxExchange() {
        return new DirectExchange("payment-service.dlx", true, false);
    }



    /**
     * 支付服务订单死信队列
     */
    @Bean
    public Queue paymentOrderDlxQueue() {
        return QueueBuilder
                .durable("payment-service.dlx.order.queue")
                .build();
    }

    /**
     * 支付服务订单死信绑定
     */
    @Bean
    public Binding paymentOrderDlxBinding() {
        return BindingBuilder
                .bind(paymentOrderDlxQueue())
                .to(paymentDlxExchange())
                .with("payment.order.*");
    }

    /**
     * 支付服务支付结果死信队列
     */
    @Bean
    public Queue paymentResultDlxQueue() {
        return QueueBuilder
                .durable("payment-service.dlx.payment-result.queue")
                .build();
    }

    /**
     * 支付服务支付结果死信绑定
     */
    @Bean
    public Binding paymentResultDlxBinding() {
        return BindingBuilder
                .bind(paymentResultDlxQueue())
                .to(paymentDlxExchange())
                .with("payment.payment.result.*");
    }

    /**
     * 支付服务库存补偿死信队列
     */
    @Bean
    public Queue paymentInventoryCompensationDlxQueue() {
        return QueueBuilder
                .durable("payment-service.dlx.inventory-compensation.queue")
                .build();
    }

    /**
     * 支付服务库存补偿死信绑定
     */
    @Bean
    public Binding paymentInventoryCompensationDlxBinding() {
        return BindingBuilder
                .bind(paymentInventoryCompensationDlxQueue())
                .to(paymentDlxExchange())
                .with("payment.inventory.failed.*");
    }



    /**
     * 支付结果事件交换机
     */
    @Bean
    public TopicExchange paymentResultExchange() {
        return new TopicExchange("payment-result-exchange", true, false);
    }

    /**
     * 支付结果事件队列
     */
    @Bean
    public Queue paymentResultQueue() {
        return QueueBuilder
                .durable("payment-result-queue." + "payment-services")
                .withArgument("x-dead-letter-exchange", "payment-service.dlx")
                .withArgument("x-dead-letter-routing-key", "payment.payment.result.*")
                .build();
    }

    /**
     * 支付结果事件队列绑定
     */
    @Bean
    public Binding paymentResultBinding() {
        return BindingBuilder
                .bind(paymentResultQueue())
                .to(paymentResultExchange())
                .with("payment.result");
    }

    /**
     * 库存补偿交换机
     */
    @Bean
    public TopicExchange inventoryCompensationExchange() {
        return new TopicExchange("inventory-compensation-exchange", true, false);
    }

    /**
     * 支付服务库存补偿队列
     */
    @Bean
    public Queue inventoryCompensationPaymentQueue() {
        return QueueBuilder
                .durable("inventory-compensation-queue.payment-services")
                .withArgument("x-dead-letter-exchange", "payment-service.dlx")
                .withArgument("x-dead-letter-routing-key", "payment.inventory.failed.*")
                .build();
    }

    /**
     * 支付服务库存补偿队列绑定
     */
    @Bean
    public Binding inventoryCompensationPaymentBinding() {
        return BindingBuilder
                .bind(inventoryCompensationPaymentQueue())
                .to(inventoryCompensationExchange())
                .with("inventory.failed");
    }

    /**
     * 订单服务支付结果队列（用于发送支付结果给订单服务）
     * 注意：这个队列应该由订单服务管理，支付服务只负责发送消息
     */
    @Bean
    public Queue paymentResultOrderQueue() {
        return QueueBuilder
                .durable("payment-result-queue.order-services")
                .withArgument("x-dead-letter-exchange", "order-service.dlx")
                .withArgument("x-dead-letter-routing-key", "order.payment.result.*")
                .build();
    }

    /**
     * 订单延迟交换机（与order-service共享）
     */
    @Bean
    public TopicExchange delayedExchange() {
        // 订单业务交换机
        return new TopicExchange("order.delayed.exchange", true, false);
    }

    /**
     * 支付服务订单超时队列
     */
    @Bean
    public Queue paymentOrderTimeoutQueue() {
        return new Queue("timeout.order.queue.payment-services", true);
    }

    /**
     * 支付服务订单超时队列绑定
     */
    @Bean
    public Binding paymentOrderTimeoutBinding() {
        return BindingBuilder.bind(paymentOrderTimeoutQueue())
                .to(delayedExchange())
                .with("timeout.order");
    }

    /**
     * 订单服务支付结果队列绑定
     */
    @Bean
    public Binding paymentResultOrderBinding() {
        return BindingBuilder
                .bind(paymentResultOrderQueue())
                .to(paymentResultExchange())
                .with("payment.result");
    }

    /**
     * 消息通知相关的配置
     * 注意：支付服务只负责发送消息到交换机，队列由通知服务自己管理
     */
    @Bean
    public TopicExchange notificationExchange() {
        return new TopicExchange("notification-exchange", true, false);
    }
}
