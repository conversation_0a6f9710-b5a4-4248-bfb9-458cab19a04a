package com.knet.payment.system.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI配置
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("knet b2b payment-services api")
                        .description("payment-services api")
                        .version("1.0")
                        .contact(new Contact()
                                .name("knet b2b payment-services api")
                                .url("http://knet")
                                .email("<EMAIL>")));
    }
}
