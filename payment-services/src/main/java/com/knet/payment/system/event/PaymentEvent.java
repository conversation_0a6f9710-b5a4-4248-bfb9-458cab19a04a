package com.knet.payment.system.event;

import com.knet.payment.model.entity.SysPaymentGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:10
 * @description: 支付事件
 */
@Getter
public class PaymentEvent extends ApplicationEvent {
    @Schema(description = "支付组")
    private final SysPaymentGroup paymentGroup;
    @Schema(description = "事件状态")
    private final String status;

    public PaymentEvent(Object source, SysPaymentGroup paymentGroup, String status) {
        super(source);
        this.paymentGroup = paymentGroup;
        this.status = status;
    }
}
