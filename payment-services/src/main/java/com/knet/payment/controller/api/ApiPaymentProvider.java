package com.knet.payment.controller.api;

import com.knet.common.base.HttpResult;
import com.knet.payment.model.dto.req.CreatePaymentRequest;
import com.knet.payment.model.dto.req.UserBalanceQueryRequest;
import com.knet.payment.model.dto.resp.CreatePaymentResponse;
import com.knet.payment.model.dto.resp.OrderPaymentInfoResponse;
import com.knet.payment.model.dto.resp.UserBalanceResponse;
import com.knet.payment.service.IPaymentService;
import com.knet.payment.service.ISysPaymentGroupService;
import com.knet.payment.service.ISysUserWalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @date 2025/6/3 11:10
 * @description: 支付服务对外提供接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "支付服务-对外提供接口", description = "支付服务-对外提供接口")
public class ApiPaymentProvider {
    @Resource
    private IPaymentService paymentService;
    @Resource
    private ISysPaymentGroupService sysPaymentGroupService;
    @Resource
    private ISysUserWalletService sysUserWalletService;

    /**
     * 创建支付
     *
     * @param request 创建支付请求
     * @return 创建支付响应
     */
    @PostMapping("/payment/create")
    @Operation(summary = "创建支付", description = "创建支付信息，支持多种支付渠道")
    public HttpResult<CreatePaymentResponse> createPayment(@Valid @RequestBody CreatePaymentRequest request) {
        log.info("创建支付请求:  userId={}, orderId={}, amount={}, channel={}",
                request.getUserId(), request.getOrderId(), request.getAmount(), request.getPaymentChannel());
        try {
            CreatePaymentResponse response = paymentService.createPayment(request);
            log.info("创建支付成功: paymentId={}, status={}", response.getPaymentId(), response.getStatus());
            return HttpResult.ok(response);
        } catch (Exception e) {
            log.error("创建支付失败: userId={}, orderId={}, error={}"
                    , request.getUserId(), request.getOrderId(), e.getMessage(), e);
            return HttpResult.error(e.getMessage());
        }
    }

    /**
     * 查询支付状态
     *
     * @param paymentId 支付流水ID
     * @return 支付状态信息
     */
    @GetMapping("/payment/status/{paymentId}")
    @Operation(summary = "查询支付状态", description = "根据支付流水ID查询支付状态")
    public HttpResult<CreatePaymentResponse> queryPaymentStatus(
            @Parameter(description = "支付流水ID", required = true, example = "GRP-123456789012345678")
            @PathVariable String paymentId) {
        log.info("查询支付状态:  paymentId={}", paymentId);
        try {
            CreatePaymentResponse response = paymentService.queryPaymentStatus(paymentId);
            return HttpResult.ok(response);
        } catch (Exception e) {
            log.error("查询支付状态失败: paymentId={}, error={}", paymentId, e.getMessage(), e);
            return HttpResult.error(e.getMessage());
        }
    }

    /**
     * 根据订单ID获取支付信息
     *
     * @param orderId 订单ID
     * @return 支付信息
     */
    @GetMapping("/order/{orderId}/payment")
    @Operation(summary = "根据订单ID获取支付信息", description = "供其他服务调用，获取订单的支付详细信息")
    public HttpResult<OrderPaymentInfoResponse> getPaymentInfoByOrderId(
            @Parameter(description = "订单ID", required = true, example = "ORD-123456789012345678")
            @PathVariable("orderId") String orderId) {
        log.info("获取订单支付信息: orderId={}", orderId);
        try {
            OrderPaymentInfoResponse paymentInfo = sysPaymentGroupService.getPaymentInfoByOrderId(orderId);
            if (paymentInfo == null) {
                log.warn("订单支付信息不存在: orderId={}", orderId);
                return HttpResult.error("订单支付信息不存在");
            }
            log.info("获取订单支付信息成功: orderId={}", orderId);
            return HttpResult.ok(paymentInfo);
        } catch (Exception e) {
            log.error("获取订单支付信息失败: orderId={}, error={}", orderId, e.getMessage(), e);
            return HttpResult.error("获取订单支付信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询用户余额", description = "根据userId查询用户余额信息")
    @GetMapping("/wallet/balance")
    public HttpResult<UserBalanceResponse> getUserBalance(@RequestParam(required = false)
                                                          @NotNull(message = "用户ID不能为空")
                                                          @Positive(message = "用户ID不合法") Long userId) {
        log.info("查询用户余额请求 userId:{} ", userId);
        UserBalanceResponse response = sysUserWalletService.getUserBalance(new UserBalanceQueryRequest(userId));
        return HttpResult.ok(response);
    }
}
