package com.knet.payment.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/5/20 17:05
 * @description: 用户充值响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户扣款响应")
public class UserDeductResponse extends BaseResponse {

    @Schema(description = "记录ID", example = "REC-123456789012345678")
    private String recordId;

    @Schema(description = "用户ID", example = "1001")
    private Long userId;

    @Schema(description = "扣款金额(美元)", example = "100.00")
    private String amount;

    @Schema(description = "扣款后余额(美元)", example = "1100.00")
    private String balance;

    @Schema(description = "扣款状态", example = "SUCCESS")
    private String status;

    @Schema(description = "扣款时间", example = "2025-05-20 17:05:30")
    private String rechargeTime;
}
