package com.knet.payment.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.WalletSearchType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/20 15:17
 * @description: UserAddressQueryRequest
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserWalletQueryRequest extends BasePageRequest {
    @Schema(description = "userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long userId;
    /**
     * @see WalletSearchType
     */
    @Schema(description = "交易类型,查询全部不传值")
    private WalletSearchType walletSearchType;
}
