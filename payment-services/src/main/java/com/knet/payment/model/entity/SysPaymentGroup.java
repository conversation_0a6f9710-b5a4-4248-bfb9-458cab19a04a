package com.knet.payment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.KnetPaymentGroupStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:15
 * @description: 支付组主表
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_payment_group", description = "支付组主表")
@TableName("sys_payment_group")
public class SysPaymentGroup extends BaseEntity {
    /**
     * 支付组ID（业务唯一标识）
     */
    @Schema(description = "支付组ID（业务唯一标识）")
    private String groupId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 关联订单ID
     */
    @Schema(description = "关联订单ID")
    private String orderId;

    /**
     * 需支付总金额
     */
    @Schema(description = "需支付总金额 美元", example = "100.00")
    private BigDecimal totalAmount;

    /**
     * 已支付金额
     */
    @Schema(description = "已支付金额 美元", example = "100.00")
    private BigDecimal paidAmount;

    /**
     * 状态：0-未完成,1-已完成,2-已关闭
     */
    @Schema(description = "状态：0-未完成,1-已完成,2-已关闭")
    private KnetPaymentGroupStatus status;

    /**
     * 创建支付组
     *
     * @param groupId 支付组ID
     * @param userId  用户ID
     * @param orderId 关联订单ID
     * @param amount  需支付总金额
     * @return 支付组
     */
    public static SysPaymentGroup create(String groupId, Long userId, String orderId, BigDecimal amount) {
        return SysPaymentGroup
                .builder()
                .groupId(groupId)
                .userId(userId)
                .orderId(orderId)
                .totalAmount(amount)
                .paidAmount(BigDecimal.ZERO)
                .status(KnetPaymentGroupStatus.UNFINISHED)
                .build();
    }
}