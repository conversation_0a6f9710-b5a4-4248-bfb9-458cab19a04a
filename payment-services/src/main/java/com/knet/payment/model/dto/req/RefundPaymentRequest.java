package com.knet.payment.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.PaymentChannel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/24 16:51
 * @description: 订单退款请求体
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "订单退款请求体")
public class RefundPaymentRequest extends BaseRequest {
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotBlank(message = "订单ID不能为空")
    @Schema(description = "关联订单ItemId", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private Long orderItemId;

    @Schema(description = "子订单no", example = "ORDI-123456789012345678")
    private String orderItemNo;

    @NotBlank(message = "父亲订单ID不能为空")
    @Schema(description = "关联订单ItemId", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private String prentOrderId;

    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "支付金额必须大于0.01美元")
    @Schema(description = "支付金额(美元)", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal amount;

    @Builder.Default
    @Schema(description = "支付渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "WALLET,默认钱包余额支付")
    private PaymentChannel paymentChannel = PaymentChannel.WALLET;

    @Schema(description = "取消备注", example = "订单取消")
    private String remark;
}
