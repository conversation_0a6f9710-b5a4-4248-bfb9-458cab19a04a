package com.knet.payment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.WalletRecordType;
import com.knet.payment.model.dto.resp.SysWalletRecordResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2025/3/12 14:15
 * @description: 用户钱包记录表
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_wallet_record", description = "用户钱包记录表")
@TableName("sys_wallet_record")
public class SysWalletRecord extends BaseEntity {
    /**
     * 记录ID（全局唯一）
     */
    @Schema(description = "记录ID（全局唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recordId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 变动金额
     */
    @Schema(description = "变动金额 美元", example = "100.00", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal amount;

    /**
     * 钱包交易类型
     *
     * @see WalletRecordType
     */
    @Schema(description = "钱包交易类型", example = "PAYMENT_DEDUCTION REFUND_INCOME RECHARGE WITHDRAW", requiredMode = Schema.RequiredMode.REQUIRED)
    private WalletRecordType type;


    @Schema(description = "备注", example = "备注信息")
    private String remarks;

    /**
     * 关联支付流水
     */
    @Schema(description = "关联支付流水")
    private String paymentId;

    /**
     * 关联订单
     */
    @Schema(description = "关联订单")
    private String orderId;

    /**
     * mapToSysWalletRecordResp
     *
     * @return SysWalletRecordResp
     */
    public SysWalletRecordResp mapToSysWalletRecordResp() {
        SysWalletRecordResp resp = new SysWalletRecordResp();
        resp.setRecordId(this.recordId);
        resp.setUserId(this.userId);
        resp.setAmount(this.amount.toString());
        resp.setType(this.type.name());
        resp.setCreateTime(this.getCreateTime());
        resp.setOrderId(this.orderId);
        return resp;
    }
}