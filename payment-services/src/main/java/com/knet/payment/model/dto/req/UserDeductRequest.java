package com.knet.payment.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/5 16:10
 * @description: 用户扣款请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户扣款请求体")
public class UserDeductRequest extends BaseRequest {
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotNull(message = "扣款金额不能为空")
    @DecimalMin(value = "0.01", message = "扣款金额必须大于0.01美元")
    @Schema(description = "扣款金额(美元)", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal amount;

    @Schema(description = "扣款备注", example = "扣款")
    private String remark;

    /**
     * 参数校验
     *
     * @param request 请求参数
     */
    public static void checkDeductRequest(UserDeductRequest request) {
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("扣款金额必须大于0");
        }
    }
}
