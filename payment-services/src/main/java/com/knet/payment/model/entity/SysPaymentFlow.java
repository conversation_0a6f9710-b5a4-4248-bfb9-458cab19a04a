package com.knet.payment.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.enums.PaymentChannel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:15
 * @description: 支付流水表
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_payment_flow", description = "支付流水表")
@TableName("sys_payment_flow")
public class SysPaymentFlow extends BaseEntity {
    /**
     * 支付流水ID（业务标识）
     */
    @Schema(description = "支付流水ID（业务标识）")
    private String paymentId;

    /**
     * 外键关联支付组
     */
    @Schema(description = "外键关联支付组")
    private String groupId;

    /**
     * 支付渠道
     */
    @Schema(description = "支付渠道")
    private Integer payChannel;

    /**
     * 本次支付金额
     */
    @Schema(description = "本次支付金额 美元", example = "100.00")
    private BigDecimal amount;

    /**
     * 支付状态 0-待支付 1-支付中 2-支付成功 3-支付失败
     */
    @Schema(description = "支付状态 0-待支付 1-支付中 2-支付成功 3-支付失败")
    private KnetPaymentFlowStatus status;

    /**
     * 渠道交易号（用于对账和退款）
     */
    @Schema(description = "渠道交易号（用于对账和退款）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String channelTxNo;


    /**
     * 创建支付流水
     *
     * @param paymentId      支付流水ID
     * @param groupId        支付组ID
     * @param paymentChannel 支付渠道
     * @param amount         支付金额
     * @return 支付流水
     */
    public static SysPaymentFlow create(String paymentId, String groupId, PaymentChannel paymentChannel, BigDecimal amount) {
        return SysPaymentFlow
                .builder()
                .paymentId(paymentId)
                .groupId(groupId)
                .payChannel(paymentChannel.getCode())
                .amount(amount)
                .status(KnetPaymentFlowStatus.PENDING)
                .build();
    }
}