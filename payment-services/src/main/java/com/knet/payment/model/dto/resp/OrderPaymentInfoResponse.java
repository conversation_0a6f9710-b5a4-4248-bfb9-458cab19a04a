package com.knet.payment.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:00
 * @description: 订单支付信息响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单支付信息响应")
public class OrderPaymentInfoResponse extends BaseResponse {

    @Schema(description = "支付组ID", example = "GRP-123456789012345678")
    private String groupId;

    @Schema(description = "关联订单ID", example = "ORD-123456789012345678")
    private String orderId;

    @Schema(description = "需支付总金额(美元)", example = "100.00")
    private String totalAmount;

    @Schema(description = "已支付金额(美元)", example = "100.00")
    private String paidAmount;

    @Schema(description = "支付状态", example = "FINISHED")
    private String status;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "支付时间")
    private Date paymentTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "创建时间")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "更新时间")
    private Date updateTime;
}
