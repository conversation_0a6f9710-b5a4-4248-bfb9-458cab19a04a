package com.knet.payment.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.exception.ServiceException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/20 17:00
 * @description: 用户充值请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户充值请求")
public class UserRechargeRequest extends BaseRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotNull(message = "充值金额不能为空")
    @DecimalMin(value = "0.01", message = "充值金额必须大于0.01美元")
    @Schema(description = "充值金额(美元)", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private BigDecimal amount;

    @Schema(description = "充值备注", example = "用户充值")
    private String remark;

    /**
     * 参数校验
     *
     * @param request 请求参数
     */
    public static void checkRechargeRequest(UserRechargeRequest request) {
        if (request.getAmount() == null || request.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("充值金额必须大于0");
        }
    }
}
