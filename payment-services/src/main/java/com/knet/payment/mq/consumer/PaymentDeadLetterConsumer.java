package com.knet.payment.mq.consumer;

import com.knet.common.utils.RedisCacheUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description: 支付服务死信消费者 - 处理进入死信队列的消息
 */
@Slf4j
@Component
public class PaymentDeadLetterConsumer {
    private static final String DLX_PROCESSED_KEY = "payment:dlx:processed:%s";
    private static final String PROCESSED = "PROCESSED";


    /**
     * 处理订单相关死信消息
     */
    @RabbitListener(queues = "payment-service.dlx.order.queue", ackMode = "MANUAL")
    public void handleOrderDeadLetter(
            @Payload String messageBody,
            @Header(value = "x-original-routing-key", required = false) String originalRoutingKey,
            @Header(value = "x-death", required = false) Object deathInfo,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Message message) {

        String messageId = extractMessageId(message);
        String dlxMessageId = "ORDER_DLX_" + messageId;

        try {
            if (!RedisCacheUtil.setIfAbsent(DLX_PROCESSED_KEY.formatted(dlxMessageId), PROCESSED, 300)) {
                log.warn("支付服务订单死信重复消息: messageId={}", dlxMessageId);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.error("支付服务订单死信消息处理: messageId={}, originalRoutingKey={}, messageBody={}, deathInfo={}",
                    messageId, originalRoutingKey, messageBody, deathInfo);

            recordDeadLetterMessage("ORDER", messageId, originalRoutingKey, messageBody, deathInfo);
            channel.basicAck(deliveryTag, false);

        } catch (Exception e) {
            log.error("处理支付服务订单死信消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            try {
                channel.basicAck(deliveryTag, false);
            } catch (IOException ex) {
                log.error("确认死信消息失败: messageId={}, error={}", messageId, ex.getMessage());
            }
        }
    }

    /**
     * 处理支付结果相关死信消息
     */
    @RabbitListener(queues = "payment-service.dlx.payment-result.queue", ackMode = "MANUAL")
    public void handlePaymentResultDeadLetter(
            @Payload String messageBody,
            @Header(value = "x-original-routing-key", required = false) String originalRoutingKey,
            @Header(value = "x-death", required = false) Object deathInfo,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Message message) {

        String messageId = extractMessageId(message);
        String dlxMessageId = "PAYMENT_RESULT_DLX_" + messageId;

        try {
            if (!RedisCacheUtil.setIfAbsent(DLX_PROCESSED_KEY.formatted(dlxMessageId), PROCESSED, 300)) {
                log.warn("支付服务支付结果死信重复消息: messageId={}", dlxMessageId);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.error("支付服务支付结果死信消息处理: messageId={}, originalRoutingKey={}, messageBody={}, deathInfo={}",
                    messageId, originalRoutingKey, messageBody, deathInfo);

            recordDeadLetterMessage("PAYMENT_RESULT", messageId, originalRoutingKey, messageBody, deathInfo);
            channel.basicAck(deliveryTag, false);

        } catch (Exception e) {
            log.error("处理支付服务支付结果死信消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            try {
                channel.basicAck(deliveryTag, false);
            } catch (IOException ex) {
                log.error("确认死信消息失败: messageId={}, error={}", messageId, ex.getMessage());
            }
        }
    }

    /**
     * 处理库存补偿相关死信消息
     */
    @RabbitListener(queues = "payment-service.dlx.inventory-compensation.queue", ackMode = "MANUAL")
    public void handleInventoryCompensationDeadLetter(
            @Payload String messageBody,
            @Header(value = "x-original-routing-key", required = false) String originalRoutingKey,
            @Header(value = "x-death", required = false) Object deathInfo,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Message message) {

        String messageId = extractMessageId(message);
        String dlxMessageId = "INVENTORY_COMPENSATION_DLX_" + messageId;

        try {
            if (!RedisCacheUtil.setIfAbsent(DLX_PROCESSED_KEY.formatted(dlxMessageId), PROCESSED, 300)) {
                log.warn("支付服务库存补偿死信重复消息: messageId={}", dlxMessageId);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.error("支付服务库存补偿死信消息处理: messageId={}, originalRoutingKey={}, messageBody={}, deathInfo={}",
                    messageId, originalRoutingKey, messageBody, deathInfo);

            recordDeadLetterMessage("INVENTORY_COMPENSATION", messageId, originalRoutingKey, messageBody, deathInfo);
            channel.basicAck(deliveryTag, false);

        } catch (Exception e) {
            log.error("处理支付服务库存补偿死信消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            try {
                channel.basicAck(deliveryTag, false);
            } catch (IOException ex) {
                log.error("确认死信消息失败: messageId={}, error={}", messageId, ex.getMessage());
            }
        }
    }


    /**
     * 从消息中提取messageId
     */
    private String extractMessageId(Message message) {
        Object messageIdHeader = message.getMessageProperties().getHeaders().get("messageId");
        if (messageIdHeader != null) {
            return messageIdHeader.toString();
        }
        return message.getMessageProperties().getMessageId() != null ?
                message.getMessageProperties().getMessageId() :
                "UNKNOWN_" + System.currentTimeMillis();
    }

    /**
     * 记录死信消息
     */
    private void recordDeadLetterMessage(String messageType, String messageId, String originalRoutingKey,
                                         String messageBody, Object deathInfo) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String logMessage = String.format(
                    "[PAYMENT_DEAD_LETTER_RECORD] Type: %s, MessageId: %s, OriginalRoutingKey: %s, Timestamp: %s, DeathInfo: %s, MessageBody: %s",
                    messageType, messageId, originalRoutingKey, timestamp, deathInfo, messageBody
            );

            log.error(logMessage);

            // TODO: 可以在这里添加以下功能：
            // 1. 保存到sys_dead_letter_message表
            // 2. 发送钉钉/邮件告警
            // 3. 推送到监控系统
            // 4. 对于支付相关的死信消息，需要特别关注并及时处理

        } catch (Exception e) {
            log.error("记录死信消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
        }
    }
}
