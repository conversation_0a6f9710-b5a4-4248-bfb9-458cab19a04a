package com.knet.payment.mq.consumer;

import com.knet.common.utils.RedisCacheUtil;
import com.knet.payment.service.IPaymentCompensationService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.PAYMENT_TIME_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/12/19 10:00
 * @description: 支付服务订单超时消费者
 */
@Slf4j
@Component
public class OrderTimeoutConsumer {
    @Resource
    private IPaymentCompensationService paymentCompensationService;

    @RabbitListener(
            queues = "timeout.order.queue.payment-services",
            ackMode = "MANUAL"
    )
    public void handleOrderTimeout(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("timeout.order".equals(routingKey)) {
                // 使用服务特定的消息ID前缀进行幂等性检查
                String paymentMessageId = "PAYMENT_" + messageId;
                if (!RedisCacheUtil.setIfAbsent(PAYMENT_TIME_PROCESSED.formatted(paymentMessageId), PROCESSED, 60)) {
                    log.warn("支付服务订单超时重复消息: {}", paymentMessageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                log.info("支付服务处理订单超时消息: messageId={}, messageBody={}", paymentMessageId, messageBody);
                paymentCompensationService.processOrderTimeout(messageBody);
                channel.basicAck(deliveryTag, false);
                log.info("支付服务订单超时处理完成: messageId={}", paymentMessageId);
            }
        } catch (Exception e) {
            log.error("支付服务订单超时处理失败: messageId={}, messageBody={}", messageId, messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }
}
