package com.knet.gateway.filter;

import com.knet.gateway.service.IAuthService;
import com.knet.gateway.system.config.WhiteList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Objects;

import static com.knet.common.constants.SystemConstant.TOKEN;

/**
 * <AUTHOR>
 * @date 2025/2/12 10:54
 * @description: 全局身份验证
 */
@Slf4j
@Component
public class AuthFilter implements GlobalFilter, Ordered {
    @Autowired
    private WhiteList whiteList;

    @Lazy
    @Autowired
    private IAuthService authService;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().toString();
        String method = Objects.requireNonNull(request.getMethod()).toString();
        String clientIp = getClientIp(request);
        
        // 白名单检查
        if (whiteList.contains(path)) {
            log.debug("白名单路径访问 - Path: {}, Method: {}, IP: {}", path, method, clientIp);
            return chain.filter(exchange);
        }
        // Token检查
        if (!request.getHeaders().containsKey(TOKEN)) {
            log.warn("缺少Authorization头 - Path: {}, IP: {}", path, clientIp);
            return unauthorizedResponse(exchange, "Missing Authorization Header");
        }
        String token = Objects.requireNonNull(request.getHeaders().get(TOKEN)).get(0);
        if (token.trim().isEmpty()) {
            log.warn("空的Authorization头 - Path: {}, IP: {}", path, clientIp);
            return unauthorizedResponse(exchange, "Empty Authorization Header");
        }
        // Token验证
        return authService.validateToken(token)
                .flatMap(valid -> {
                    if (valid) {
                        log.debug("Token验证成功 - Path: {}, IP: {}", path, clientIp);
                        return chain.filter(exchange);
                    } else {
                        log.warn("Token验证失败 - Path: {}, IP: {}, Token: {}***", path, clientIp,
                                token.length() > 10 ? token.substring(0, 10) : token);
                        return unauthorizedResponse(exchange, "Invalid Token");
                    }
                })
                .onErrorResume(e -> {
                    log.error("Token验证异常 - Path: {}, IP: {}, Error: {}", path, clientIp, e.getMessage());
                    return unauthorizedResponse(exchange, "Token Validation Error");
                });
    }

    /**
     * 获取客户端真实IP
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        return Objects.requireNonNull(request.getRemoteAddress()).getAddress().getHostAddress();
    }

    /**
     * 返回401未授权响应
     *
     * @param exchange exchange
     * @param message  提示信息
     * @return Mono<Void>
     */
    private Mono<Void> unauthorizedResponse(ServerWebExchange exchange, String message) {
        exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
        exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);
        DataBuffer buffer = new DefaultDataBufferFactory().wrap(message.getBytes(StandardCharsets.UTF_8));
        return exchange.getResponse().writeWith(Mono.just(buffer));
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
