package com.knet.gateway.system.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/26 16:15
 * @description: Gateway全局异常处理器
 */
@Slf4j
@Order(-1)
@Component
public class GlobalExceptionHandler implements ErrorWebExceptionHandler {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        ServerHttpResponse response = exchange.getResponse();

        if (response.isCommitted()) {
            return Mono.error(ex);
        }
        // 设置响应头
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        // 根据异常类型设置状态码和错误信息
        HttpStatus status;
        String message;
        String errorCode;
        if (ex instanceof NotFoundException) {
            status = HttpStatus.NOT_FOUND;
            message = "服务不可用";
            errorCode = "SERVICE_NOT_FOUND";
            log.warn("服务未找到: {}", ex.getMessage());
        } else if (ex instanceof ResponseStatusException responseStatusException) {
            status = responseStatusException.getStatus();
            message = responseStatusException.getReason();
            errorCode = "RESPONSE_STATUS_ERROR";
            log.warn("响应状态异常: {}", ex.getMessage());
        } else if (ex instanceof IllegalStateException) {
            status = HttpStatus.BAD_GATEWAY;
            message = "网关配置错误";
            errorCode = "GATEWAY_CONFIG_ERROR";
            log.error("网关配置错误: {}", ex.getMessage(), ex);
        } else {
            status = HttpStatus.INTERNAL_SERVER_ERROR;
            message = "内部服务器错误";
            errorCode = "INTERNAL_SERVER_ERROR";
            log.error("未处理的异常: {}", ex.getMessage(), ex);
        }

        response.setStatusCode(status);

        // 构建错误响应
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("timestamp", LocalDateTime.now().toString());
        errorResponse.put("status", status.value());
        errorResponse.put("error", status.getReasonPhrase());
        errorResponse.put("message", message);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("path", exchange.getRequest().getPath().value());

        try {
            String errorJson = objectMapper.writeValueAsString(errorResponse);
            DataBuffer buffer = response.bufferFactory().wrap(errorJson.getBytes(StandardCharsets.UTF_8));
            return response.writeWith(Mono.just(buffer));
        } catch (JsonProcessingException e) {
            log.error("序列化错误响应失败", e);
            return response.setComplete();
        }
    }
}
