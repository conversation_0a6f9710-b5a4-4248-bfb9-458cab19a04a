package com.knet.gateway.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 11:44
 * @description: 白名单
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "whitelist")
public class WhiteList {
    /**
     * 白名单路径
     */
    private List<String> paths;

    public boolean contains(String path) {
        return paths.contains(path);
    }
}