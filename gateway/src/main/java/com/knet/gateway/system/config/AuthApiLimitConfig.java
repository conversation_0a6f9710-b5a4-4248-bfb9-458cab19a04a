package com.knet.gateway.system.config;

/**
 * <AUTHOR>
 * @date 2025/2/27 17:14
 * @description: gateway接口限流设置
 */
//@Configuration
public class AuthApiLimitConfig {

    /**
     * 按接口路径限流（例如 /api/v1/resource）
     *
     * @return KeyResolver
     */
/*    @Bean
    public KeyResolver loginKeyResolver() {
        return exchange -> {
            String path = exchange.getRequest().getPath().value();
            String ip = Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getAddress().getHostAddress();
            // 仅当路径为登录接口时才应用限流
            if (path.equals("/userServices/auth/login")) {
                return Mono.just(ip + ":" + path);
            }
            return Mono.just("unlimited");
        };
    }*/
    /**
     *   # 用户服务登录接口限流路由
     *         - id: user-login-route
     *           uri: lb://user-services
     *           predicates:
     *             - Path=/userServices/auth/captcha  # 精准匹配登录接口
     *           filters:
     *             - name: RequestRateLimiter
     *               args:
     *                 redis-rate-limiter:
     *                   replenishRate: 1      # 每秒生成1个令牌
     *                   burstCapacity: 1      # 允许突发5次请求（适应验证码重试场景）
     *                 key-resolver: "#{@loginKeyResolver}"  # 引用组合键解析器
     */
}