package com.knet.gateway.system.filter;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2025/7/23
 * @description: Gateway专用的日志过滤器，兼容WebFlux
 */
@Slf4j
@Component
public class GatewayLoggingFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        // 获取或生成请求ID
        final String requestId;
        String existingRequestId = request.getHeaders().getFirst("X-Request-ID");
        if (existingRequestId != null && !existingRequestId.trim().isEmpty()) {
            // 使用现有的请求ID
            requestId = existingRequestId;
        } else {
            // 生成新的请求ID
            requestId = generateRequestId();
        }
        // 设置MDC
        MDC.put("requestId", requestId);
        MDC.put("serviceName", "gateway");
        // 添加请求ID到响应头
        response.getHeaders().add("X-Request-ID", requestId);
        final long startTime = System.currentTimeMillis();
        // 记录请求开始日志
        logRequest(requestId, request, startTime);
        return chain.filter(exchange)
                .doFinally(signalType -> {
                    // 记录请求完成日志
                    long duration = System.currentTimeMillis() - startTime;
                    logResponse(requestId, request, response, duration);
                    // 清理MDC
                    MDC.clear();
                });
    }

    /**
     * 记录请求日志
     */
    private void logRequest(String requestId, ServerHttpRequest request, long startTime) {
        try {
            String method = request.getMethod().name();
            String uri = request.getURI().toString();
            String remoteAddress = getRemoteAddress(request);

            StringBuilder sb = new StringBuilder(256);
            sb.append("\n🌐 |== GATEWAY REQUEST ================");
            sb.append("\n   | REQUEST_ID : ").append(requestId);
            sb.append("\n   | Method     : ").append(method);
            sb.append("\n   | URI        : ").append(uri);
            sb.append("\n   | From       : ").append(remoteAddress);
            sb.append("\n   | Time       : ").append(startTime);
            sb.append("\n   |===================================");

            log.info(sb.toString());
        } catch (Exception e) {
            log.error("Failed to log gateway request: {}", e.getMessage());
        }
    }

    /**
     * 记录响应日志
     */
    private void logResponse(String requestId, ServerHttpRequest request,
                             ServerHttpResponse response, long duration) {
        try {
            String method = request.getMethod().name();
            String uri = request.getURI().getPath();
            int statusCode = response.getStatusCode() != null ?
                    response.getStatusCode().value() : 0;
            StringBuilder sb = new StringBuilder(256);
            if (duration > 5000) {
                // 超慢请求
                sb.append("\n🐌 |== GATEWAY SLOW RESPONSE ==========");
            } else if (duration > 1000) {
                // 慢请求
                sb.append("\n⚠️ |== GATEWAY RESPONSE ===============");
            } else if (statusCode >= 400) {
                // 错误响应
                sb.append("\n❌ |== GATEWAY ERROR ==================");
            } else {
                // 正常响应
                sb.append("\n✅ |== GATEWAY RESPONSE ===============");
            }
            sb.append("\n   | REQUEST_ID : ").append(requestId);
            sb.append("\n   | Method     : ").append(method);
            sb.append("\n   | URI        : ").append(uri);
            sb.append("\n   | Status     : ").append(statusCode);
            sb.append("\n   | Time       : ").append(duration).append(" ms");
            sb.append("\n   |===================================");
            if (statusCode >= 400 || duration > 1000) {
                log.warn(sb.toString());
            } else {
                log.info(sb.toString());
            }
        } catch (Exception e) {
            log.error("Failed to log gateway response: {}", e.getMessage());
        }
    }

    /**
     * 获取远程地址
     */
    private String getRemoteAddress(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        return request.getRemoteAddress() != null ?
                request.getRemoteAddress().getAddress().getHostAddress() : "unknown";
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 10000);
        return String.format("GW%d%04d", timestamp % 100000000, random);
    }

    /**
     * 最高优先级
     */
    @Override
    public int getOrder() {
        return -1;
    }
}
