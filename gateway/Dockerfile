#1.依赖的环境：
FROM openjdk:17-jdk-slim

# 将工作目录设置为 /app
WORKDIR /app

#2.定义作者信息：
LABEL maintainer="<EMAIL>"
LABEL org.opencontainers.image.title="gateway-service"
LABEL org.opencontainers.image.version="1.0.0"

#3.将jar包添加到容器（将jar包存入镜像中）：其他项目参考本文件
ADD ./target/gateway-1.0-SNAPSHOT.jar gateway-service.jar

#4.指定这个容器对外暴露的端口号
EXPOSE 7000

# 容器启动命令
CMD ["java", "-Djava.awt.headless=true", "--add-opens", "java.base/java.lang=ALL-UNNAMED", "--add-opens", "java.base/java.util=ALL-UNNAMED", "--add-opens", "java.base/java.time=ALL-UNNAMED", "-jar", "gateway-service.jar"]
