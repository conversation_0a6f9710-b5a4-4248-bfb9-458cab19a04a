<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.order.mapper.SysShippingLabelMapper">

    <select id="getListByItemIds" resultType="com.knet.order.model.dto.resp.OrderShipInfo">
        SELECT
        label.*,
        rel.item_id AS itemId
        FROM
        sys_shipping_label label
        LEFT JOIN sys_shipping_item_rel rel ON label.id = rel.label_id
        <where>
            rel.item_id IN
            <foreach collection="itemIds" item="itemId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </where>
    </select>
</mapper>