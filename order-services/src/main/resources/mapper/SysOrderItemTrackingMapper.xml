<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.knet.order.mapper.SysOrderItemTrackingMapper">

    <!-- 查询处于指定状态的订单项及其物流信息 -->
    <select id="getOrderItemsWithTrackingInfo" resultType="com.knet.order.model.vo.OrderItemTrackingVO">
        SELECT item.item_id,
               item.parent_order_id,
               item.order_id,
               item.`status`,
               label.tracking_number
        FROM sys_order_item item
                 LEFT JOIN sys_shipping_item_rel rel ON item.item_id = rel.item_id
                 LEFT JOIN sys_shipping_label label ON rel.label_id = label.id
        WHERE item.`status` = #{status}
          AND label.tracking_number IS NOT NULL
    </select>

    <!-- 统计处于指定状态的订单项数量 -->
    <select id="countInTransitOrderItems" resultType="int">
        SELECT COUNT(*)
        FROM sys_order_item item
                 LEFT JOIN sys_shipping_item_rel rel ON item.item_id = rel.item_id
                 LEFT JOIN sys_shipping_label label ON rel.label_id = label.id
        WHERE item.`status` = #{status}
          AND label.tracking_number IS NOT NULL
    </select>

    <!-- 分页查询处于指定状态的订单项及其物流信息 -->
    <select id="getOrderItemsWithTrackingInfoByPage" resultType="com.knet.order.model.vo.OrderItemTrackingVO">
        SELECT item.item_id,
               item.parent_order_id,
               item.order_id,
               item.`status`,
               label.tracking_number
        FROM sys_order_item item
                 LEFT JOIN sys_shipping_item_rel rel ON item.item_id = rel.item_id
                 LEFT JOIN sys_shipping_label label ON rel.label_id = label.id
        WHERE item.`status` = #{status}
          AND label.tracking_number IS NOT NULL
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
