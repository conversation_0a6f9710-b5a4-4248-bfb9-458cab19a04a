package com.knet.order.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/5/20 16:32
 * @description: 用户余额响应
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户余额响应")
public class InnerUserBalanceResponse extends BaseResponse {
    @Schema(description = "用户ID", example = "1001")
    private Long userId;
    @Schema(description = "可用余额(美元)", example = "100.00")
    private String balance;
    @Schema(description = "冻结金额(美元)", example = "10.00")
    private String frozenBalance;
    @Schema(description = "总余额(美元),展示总余额字段，其他忽略", example = "110.00")
    private String totalBalance;

    public InnerUserBalanceResponse init(Long userId) {
        return InnerUserBalanceResponse.builder()
                .userId(userId)
                .balance("0.00")
                .frozenBalance("0.00")
                .totalBalance("0.00")
                .build();
    }
}
