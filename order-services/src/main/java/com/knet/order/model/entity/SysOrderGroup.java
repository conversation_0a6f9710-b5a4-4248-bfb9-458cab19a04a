package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.KnetOrderGroupStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:15
 * @description: B2B订单主表
 */
@NoArgsConstructor
@Builder
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_order_group", description = "B2B订单主表")
@TableName("sys_order_group")
public class SysOrderGroup extends BaseEntity {
    /**
     * 订单ID（全局唯一）
     */
    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    /**
     * 用户ID，关联企业账户
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 订单总金额（含税价，单位：美元）
     */
    @Schema(description = "订单总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalAmount;

    /**
     * 状态：
     *
     * @see KnetOrderGroupStatus
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderGroupStatus status;

    /**
     * 用户地址ID
     */
    @Schema(description = "订单邮寄地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long addressId;

    /**
     * 创建订单主表记录
     */
    public static SysOrderGroup createOrderGroup(String orderId, Long userId, BigDecimal totalAmount, Long addressId) {
        return SysOrderGroup
                .builder()
                .orderId(orderId)
                .userId(userId)
                .addressId(addressId)
                .totalAmount(totalAmount)
                .status(KnetOrderGroupStatus.PENDING_PAYMENT)
                .build();
    }
}