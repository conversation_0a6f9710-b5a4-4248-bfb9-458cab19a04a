package com.knet.order.model.dto.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.utils.PriceFormatUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:01
 * @description: b2b平台对外提供订单信息
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class KnetB2bOrderQueryVo extends BaseResponse {
    /**
     * 父订单ID
     */
    @Schema(description = "父订单ID")
    private String parentOrderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * sku
     */
    @Schema(description = "sku")
    private String sku;

    /**
     * size
     */
    @Schema(description = "size")
    private String size;

    /**
     * 商品名称
     */
    @Schema(description = "productName")
    private String productName;

    /**
     * 商品图片URL
     */
    @Schema(description = "imgUrl")
    private String imgUrl;

    /**
     * KG oneId
     */
    @Schema(description = "KG oneId")
    private String oneId;

    /**
     * b2b 商品唯一id
     */
    @Schema(description = "b2b 商品唯一id")
    private String listingId;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格", example = "100.00,单位：美分")
    private Long salePrice;


    @Schema(description = " 卖家到手价格（含税，单位：美分）")
    private Long sellerOwningPrice;

    /**
     * Kg 到手价格（含税，单位：美分）
     */
    @Schema(description = " Kg 到手价格（含税，单位：美分）")
    private Long kgOwningPrice;


    /**
     * 状态：
     *
     * @see com.knet.common.enums.KnetOrderItemStatus
     */
    @Schema(description = "状态")
    private KnetOrderItemStatus status;
    /**
     * 售出时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单售出时间")
    private Date soldTime;

    /**
     * 取消时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单取消时间")
    private Date cancelTime;

    /**
     * 物流-面单地址
     */
    @Schema(description = "面单地址")
    private String labelUrl;
    /**
     * 物流-物流单号
     */
    @Schema(description = "物流单号")
    private String trackingNumber;
    /**
     * 物流-物流公司
     */
    @Schema(description = "物流公司")
    private String carrier;

    /**
     * 销售渠道
     */
    @Builder.Default
    @Schema(description = "销售渠道")
    private String saleChannel = "B2B";

    /**
     * 是否发货
     */
    @Builder.Default
    @Schema(description = "是否发货", example = "true")
    private Boolean needShip = true;

    /**
     * 商品来源
     */
    @Schema(description = "source", requiredMode = Schema.RequiredMode.REQUIRED)
    private String source;

    public static KnetB2bOrderQueryVo create(OrderAndLabelVo orderAndLabelVo) {
        return KnetB2bOrderQueryVo.builder()
                .parentOrderId(orderAndLabelVo.getParentOrderId())
                .orderNo(orderAndLabelVo.getOrderNo())
                .sku(orderAndLabelVo.getSku())
                .size(orderAndLabelVo.getSize())
                .productName(orderAndLabelVo.getProductName())
                .imgUrl(orderAndLabelVo.getImgUrl())
                .oneId(orderAndLabelVo.getOneId())
                .listingId(orderAndLabelVo.getListingId())
                .salePrice(PriceFormatUtil.formatYuanToCents(orderAndLabelVo.getSalePrice()))
                .sellerOwningPrice(PriceFormatUtil.formatYuanToCents(orderAndLabelVo.getSellerOwningPrice()))
                .kgOwningPrice(PriceFormatUtil.formatYuanToCents(orderAndLabelVo.getKgOwningPrice()))
                .status(orderAndLabelVo.getStatus())
                .soldTime(orderAndLabelVo.getSoldTime())
                .cancelTime(orderAndLabelVo.getCancelTime())
                .labelUrl(orderAndLabelVo.getLabelUrl())
                .trackingNumber(orderAndLabelVo.getTrackingNumber())
                .carrier(orderAndLabelVo.getCarrier())
                .saleChannel(orderAndLabelVo.getSaleChannel())
                .needShip(orderAndLabelVo.getNeedShip())
                .source(orderAndLabelVo.getSource())
                .build();
    }
}
