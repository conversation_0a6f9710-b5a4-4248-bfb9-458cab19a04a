package com.knet.order.model.vo;

import com.knet.common.enums.KnetOrderItemStatus;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/30 16:40
 * @description: 订单项物流追踪信息VO
 */
@Data
public class OrderItemTrackingVO {

    /**
     * 订单项ID
     */
    private Long itemId;

    /**
     * 父订单ID
     */
    private String parentOrderId;

    /**
     * 子订单ID
     */
    private String orderId;

    /**
     * 订单项状态
     */
    private KnetOrderItemStatus status;

    /**
     * 物流单号
     */
    private String trackingNumber;
}
