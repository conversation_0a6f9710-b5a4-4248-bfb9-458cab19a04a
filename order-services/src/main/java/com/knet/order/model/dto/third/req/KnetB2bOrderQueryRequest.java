package com.knet.order.model.dto.third.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.KnetOrderItemStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:02
 * @description: b2b对外提供查询订单请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnetB2bOrderQueryRequest extends BasePageRequest {
    /**
     * 父订单ID
     */
    @Schema(description = "父订单ID")
    private String parentOrderId;
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;
    /**
     * 订单状态
     *
     * @see KnetOrderItemStatus
     */
    @Schema(description = "订单状态")
    private KnetOrderItemStatus status;
}
