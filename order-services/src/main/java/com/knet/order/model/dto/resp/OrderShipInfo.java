package com.knet.order.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/18 18:14
 * @description: 订单物流信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderShipInfo extends BaseResponse {
    @Schema(description = "sys_shipping_label_id")
    private Long id;

    @Schema(description = "sys_order_item_id")
    private Long itemId;

    @Schema(description = "批次编号")
    public String batchNo;

    @Schema(description = "物流单号")
    private String trackingNumber;

    @Schema(description = "电子面单地址")
    private String labelUrl;

    @Schema(description = "物流公司")
    private String expressCompany;

    @Schema(description = "总包裹数")
    private Integer packageCount;

    @Schema(description = "运费(单位：分)")
    private Long shippingFee;

    @Schema(description = "发货仓库")
    private String warehouse;
}
