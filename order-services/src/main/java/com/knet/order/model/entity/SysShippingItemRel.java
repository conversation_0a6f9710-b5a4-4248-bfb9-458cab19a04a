package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:09
 * @description: 订单item物流运单关系模型
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_shipping_item_rel", description = "订单item物流运单关系模型")
@TableName("sys_shipping_item_rel")
public class SysShippingItemRel extends BaseEntity {

    @Schema(description = "sys_order_item_id")
    private Long itemId;

    @Schema(description = "sys_shipping_label_id")
    private Long labelId;
}
