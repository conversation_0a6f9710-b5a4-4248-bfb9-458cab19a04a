package com.knet.order.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.KnetOrderItemStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/6/17 14:35
 * @description: knet发货请求体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UpdatedOrderRequest extends BaseRequest {

    @NotBlank(message = "orderNo 不能为空")
    @Schema(description = "orderNo", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;
    /**
     * @see KnetOrderItemStatus
     */
    @NotNull(message = "订单状态不能为空")
    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderItemStatus status;
}
