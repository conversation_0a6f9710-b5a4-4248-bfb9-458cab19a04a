package com.knet.order.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/8/7 10:00
 * @description: 管理员订单列表响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "管理员订单列表响应")
public class AdminOrderListResponse extends BaseResponse {

    @Schema(description = "父订单列表")
    private List<AdminParentOrderResponse> orders;

    /**
     * 管理员父订单响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "管理员父订单响应")
    public static class AdminParentOrderResponse {

        @Schema(description = "父订单ID")
        private String parentOrderId;

        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "用户账号")
        private String userAccount;

        @Schema(description = "订单状态")
        private KnetOrderGroupStatus status;

        @Schema(description = "总商品数量")
        private Integer totalQuantity;

        @Schema(description = "订单总金额（美元）")
        private String totalAmount;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
        @Schema(description = "订单创建时间")
        private Date createTime;

        @Schema(description = "子订单汇总列表")
        private List<OrderListResponse.SubOrderSummaryResponse> subOrders;
    }
}
