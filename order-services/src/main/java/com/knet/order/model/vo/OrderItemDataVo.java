package com.knet.order.model.vo;

import com.knet.order.model.dto.req.CreateOrderRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/4 14:48
 * @description: OrderItemDto
 */
@Data
public class OrderItemDataVo {
    private String sku;
    private String productName;
    private String imageUrl;
    private String size;
    private Integer quantity;
    @Schema(description = "策略价格，用于计算orderGroup和SysOrder的totalAmount")
    private BigDecimal unitPrice;
    @Schema(description = "原始价格，用于SysOrderItem.price")
    private BigDecimal originalPrice;

    public static OrderItemDataVo createOrderItemDataDto(CreateOrderRequest.OrderItemRequest requestItem
            , CreateOrderRequest.SizeDetailRequest sizeDetailRequest
            , BigDecimal unitPrice, BigDecimal originalPrice) {
        OrderItemDataVo orderItemData = new OrderItemDataVo();
        orderItemData.setSku(requestItem.getSku());
        orderItemData.setProductName(requestItem.getProductName());
        orderItemData.setImageUrl(requestItem.getImageUrl());
        orderItemData.setSize(sizeDetailRequest.getSize());
        orderItemData.setQuantity(sizeDetailRequest.getQuantity());
        orderItemData.setUnitPrice(unitPrice);
        orderItemData.setOriginalPrice(originalPrice);
        return orderItemData;
    }
}
