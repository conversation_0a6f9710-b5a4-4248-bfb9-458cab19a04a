package com.knet.order.model.vo;

import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/10 17:28
 * @description: 父订单信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubOrderGroupVo extends BaseResponse {
    /**
     * 父订单ID
     */
    private String parentOrderId;
    /**
     * 总价格（BigDecimal类型，用于接收SQL查询结果）
     */
    private BigDecimal totalPrice;
    /**
     * 状态：
     *
     * @see KnetOrderGroupStatus
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderGroupStatus status;

}
