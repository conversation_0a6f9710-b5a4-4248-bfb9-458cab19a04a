package com.knet.order.model.vo;

import com.knet.common.enums.KnetOrderGroupStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/6 10:30
 * @description: 子订单汇总数据传输对象（用于Mapper查询结果）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SubOrderSummaryVo {

    /**
     * 父订单ID
     */
    private String parentOrderId;

    /**
     * 商品SKU
     */
    private String sku;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品图片URL
     */
    private String imageUrl;

    /**
     * 聚合后的商品总数量
     */
    private Integer totalQuantity;

    /**
     * 平均价格（BigDecimal类型，用于接收SQL查询结果）
     */
    private BigDecimal avgPrice;

    /**
     * 总价格（BigDecimal类型，用于接收SQL查询结果）
     */
    private BigDecimal totalPrice;

    /**
     * 子订单状态
     */
    private KnetOrderGroupStatus status;
}
