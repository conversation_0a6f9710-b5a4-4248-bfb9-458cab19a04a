package com.knet.order.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseWithNotIdEntity;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.model.vo.OrderItemDataVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:15
 * @description: 订单商品明细
 */

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_order_item", description = "订单商品明细")
@TableName("sys_order_item")
public class SysOrderItem extends BaseWithNotIdEntity {
    /**
     * 明细项ID
     */
    @TableId(type = IdType.AUTO)
    private Long itemId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String itemNo;

    /**
     * 关联子订单
     */
    @Schema(description = "关联子订单")
    private String orderId;

    /**
     * 关联父订单
     */
    @Schema(description = "关联父订单ID")
    private String parentOrderId;

    /**
     * sku
     */
    @Schema(description = "sku")
    private String sku;

    /**
     * 尺码
     */
    @Schema(description = "尺码")
    private String size;

    /**
     * 品名
     */
    @Schema(description = "品名")
    private String name;

    /**
     * 商品图片URL
     */
    @Schema(description = "商品图片URL")
    private String imageUrl;

    /**
     * 单价（含税，单位：美元）
     */
    @Schema(description = " 单价（含税，单位：美元）")
    private BigDecimal price;

    /**
     * KG oneId
     */
    @Schema(description = "KG oneId")
    private String oneId;

    /**
     * b2b 商品唯一id
     */
    @Schema(description = "b2b 商品唯一id")
    private String knetListingId;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer count;

    @TableField("warehouse")
    @Schema(description = "存储仓库")
    private String warehouse;
    /**
     * 状态：
     *
     * @see com.knet.common.enums.KnetOrderItemStatus
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderItemStatus status;

    @Schema(description = "支付时间")
    private Date paidTime;

    @Schema(description = "推送至KG 在途时间")
    private Date inTransitTime;

    @Schema(description = "完成时间")
    private Date completedTime;

    @Schema(description = "取消时间")
    private Date cancelledTime;

    @Schema(description = "销售渠道")
    private String sellChannel;

    @Schema(description = "商品来源")
    private String source;

    /**
     * 卖家到手价格（含税，单位：美元）
     */
    @Schema(description = " 卖家到手价格（含税，单位：美元）")
    private BigDecimal sellerOwningPrice;

    /**
     * Kg 到手价格（含税，单位：美元）
     */
    @Schema(description = " Kg 到手价格（含税，单位：美元）")
    private BigDecimal kgOwningPrice;


    public static SysOrderItem createSysOrderItem(String subOrderId, String parentOrderId, OrderItemDataVo itemData
            , String orderNo, BigDecimal kgOwingPrice, BigDecimal sellerOwingPrice) {
        SysOrderItem orderItem = new SysOrderItem();
        orderItem.setOrderId(subOrderId);
        orderItem.setItemNo(orderNo);
        orderItem.setParentOrderId(parentOrderId);
        orderItem.setSku(itemData.getSku());
        orderItem.setSize(itemData.getSize());
        orderItem.setName(itemData.getProductName());
        orderItem.setImageUrl(itemData.getImageUrl());
        // 使用原始价格（去掉策略后的价格）
        orderItem.setPrice(itemData.getOriginalPrice());
        orderItem.setCount(1);
        orderItem.setStatus(KnetOrderItemStatus.PENDING_PAYMENT);
        orderItem.setSellChannel("B2B");
        orderItem.setKgOwningPrice(kgOwingPrice);
        orderItem.setSellerOwningPrice(sellerOwingPrice);
        return orderItem;
    }
}