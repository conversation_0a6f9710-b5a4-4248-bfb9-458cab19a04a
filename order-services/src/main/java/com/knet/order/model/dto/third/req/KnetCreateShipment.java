package com.knet.order.model.dto.third.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.knet.common.base.BaseRequest;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:02
 * @description: kg 创建物流单请求体
 */

@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetCreateShipment extends BaseRequest {

    @Schema(description = "仓库")
    private String warehouse;

    @Schema(description = "收货人")
    @JsonProperty("ShipTo")
    private ShipmentParty shipTo;

    @Schema(description = "包裹数")
    @JsonProperty("count")
    private Long count;

    @Data
    public static class ShipmentParty {
        @JsonProperty("AddressId")
        private Integer addressId;
        @JsonProperty("Name")
        private String name;
        @JsonProperty("AttentionName")
        private String attentionName;
        @JsonProperty("CompanyDisplayableName")
        private String companyDisplayableName;
        @JsonProperty("TaxIdentificationNumber")
        private String taxIdentificationNumber;
        @JsonProperty("Phone")
        private Phone phone;
        @JsonProperty("ShipperNumber")
        private String shipperNumber;
        @JsonProperty("FaxNumber")
        private String faxNumber;
        @JsonProperty("EmailAddress")
        private String emailAddress;
        @JsonProperty("Address")
        private Address address;

        @Data
        public static class Phone {
            @JsonProperty("Number")
            private String number;
            @JsonProperty("Extension")
            private String extension;
        }

        @Data
        public static class Address {
            @JsonProperty("AddressLine")
            private List<String> addressLine;
            @JsonProperty("City")
            private String city;
            @JsonProperty("StateProvinceCode")
            private String stateProvinceCode;
            @JsonProperty("PostalCode")
            private String postalCode;
            @JsonProperty("CountryCode")
            private String countryCode;
        }
    }

    public static KnetCreateShipment create(UserAddressDtoResp addressInfo, String warehouse, Long count) {
        KnetCreateShipment knetCreateShipment = new KnetCreateShipment();
        knetCreateShipment.setWarehouse(warehouse);
        knetCreateShipment.setShipTo(createShipmentParty(addressInfo));
        knetCreateShipment.setCount(count);
        return knetCreateShipment;
    }

    private static ShipmentParty createShipmentParty(UserAddressDtoResp addressInfo) {
        ShipmentParty shipmentParty = new ShipmentParty();
        ShipmentParty.Address address = new ShipmentParty.Address();
        address.setAddressLine(List.of(addressInfo.getAddressLine1(), addressInfo.getAddressLine2()));
        address.setCountryCode("US");
        address.setStateProvinceCode(addressInfo.getState());
        address.setCity(addressInfo.getCity());
        address.setPostalCode(addressInfo.getZipCode());
        shipmentParty.setAddress(address);

        shipmentParty.setName(addressInfo.getFullName());
        ShipmentParty.Phone phone = new ShipmentParty.Phone();
        phone.setNumber(addressInfo.getPhoneNumber());
        shipmentParty.setPhone(phone);

        return shipmentParty;
    }
}
