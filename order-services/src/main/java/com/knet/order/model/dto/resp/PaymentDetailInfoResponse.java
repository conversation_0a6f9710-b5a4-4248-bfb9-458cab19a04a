package com.knet.order.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/20 15:23
 * @description: 支付细节信息
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
public class PaymentDetailInfoResponse extends BaseResponse {
    @Schema(description = "父订单ID")
    private String parentOrderId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单状态")
    private KnetOrderGroupStatus status;

    @Schema(description = "订单总金额（美元）")
    private String totalAmount;

    @Schema(description = "总商品数量")
    private Integer totalQuantity;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    @Schema(description = "订单创建时间")
    private Date createTime;

    @Schema(description = "可用余额(美元)", example = "100.00")
    private String balance;

    /**
     * @see UserAddressDtoResp
     */
    @Schema(description = "收货地址信息")
    private UserAddressDtoResp shippingAddress;

    public static PaymentDetailInfoResponse create(OrderDetailResponse orderDetail) {
        KnetOrderGroupStatus displayStatus = KnetOrderGroupStatus.displayStatus(orderDetail.getStatus());
        return PaymentDetailInfoResponse
                .builder()
                .parentOrderId(orderDetail.getParentOrderId())
                .userId(orderDetail.getUserId())
                .status(displayStatus)
                .totalAmount(orderDetail.getTotalAmount())
                .totalQuantity(orderDetail.getTotalQuantity())
                .createTime(orderDetail.getCreateTime())
                .shippingAddress(orderDetail.getShippingAddress())
                .build();
    }
}
