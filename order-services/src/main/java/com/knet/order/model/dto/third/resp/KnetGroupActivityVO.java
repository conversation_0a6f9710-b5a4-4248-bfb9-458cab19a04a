package com.knet.order.model.dto.third.resp;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/30 15:32
 * @description: kg 物流状态返回体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetGroupActivityVO extends BaseResponse {
    @Schema(description = "状态")
    LabelCenterStatus labelCenterStatus;

    @Getter
    @AllArgsConstructor
    public enum LabelCenterStatus {
        //状态
        CREATED("CREATED", "标签刚刚完成创建"),
        TRANSIT("TRANSIT", "包裹处于运输中"),
        DELIVERED("DELIVERED", "包裹已经成功送达"),
        CANCELED("CANCELED", "已取消"),
        UNKNOWN("UNKNOWN", "未知");
        @JsonValue
        @EnumValue
        private final String code;
        private final String message;

        @Override
        public String toString() {
            return this.code;
        }
    }

    public static boolean isOrderDelivered(KnetGroupActivityVO activityVO) {
        return KnetGroupActivityVO.LabelCenterStatus.DELIVERED.equals(activityVO.getLabelCenterStatus());
    }
}
