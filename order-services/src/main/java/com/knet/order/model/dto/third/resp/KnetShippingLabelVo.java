package com.knet.order.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/13 10:56
 * @description: 发货物流信息
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class KnetShippingLabelVo extends BaseResponse {

    @Schema(description = "面单地址")
    private String labelUrl;

    @Schema(description = "物流单号")
    private String trackingNumber;

    @Schema(description = "物流公司")
    private String carrier;
}
