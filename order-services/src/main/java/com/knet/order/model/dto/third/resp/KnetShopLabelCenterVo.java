package com.knet.order.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/16 13:58
 * @description: KG 创建物流标签返回体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetShopLabelCenterVo extends BaseResponse {
    @Schema(description = "物流单号")
    private String trackingNumber;

    @Schema(description = "电子面单地址")
    private String labelUrl;

    @Schema(description = "物流公司")
    private String expressCompany;

    @Schema(description = "总包裹数")
    private Integer packageCount;

    @Schema(description = "运费(单位：分)")
    private Long shippingFee;
}
