package com.knet.order.model.dto.req;

import com.knet.common.base.BasePageRequest;
import com.knet.common.enums.OrderSearchType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/8/7 10:00
 * @description: 管理员订单列表查询请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminOrderListQueryRequest extends BasePageRequest {

    @Schema(description = "订单号（支持父订单ID或子订单ID搜索）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String orderId;


    @Schema(description = "用户账号（可选，用于筛选特定用户）", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String userAccount;

    /**
     * 订单状态，查询全部不传值
     *
     * @see OrderSearchType
     */
    @Schema(description = "订单状态，查询全部不传值", example = "PENDING")
    private OrderSearchType searchType;
}
