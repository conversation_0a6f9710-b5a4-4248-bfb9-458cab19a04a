package com.knet.order.service;

import com.knet.order.model.dto.third.req.KnetCreateShipment;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:11
 * @description: 第三方API调用服务 服务定义
 */
public interface IThirdApiService {
    /**
     * 创建物流标签
     *
     * @param shipment 物流标签创建请求
     * @return 物流标签
     */
    KnetShopLabelCenterVo getShippingLabel(KnetCreateShipment shipment);
}
