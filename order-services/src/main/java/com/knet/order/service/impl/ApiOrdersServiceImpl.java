package com.knet.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.common.annotation.DistributedLock;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.dto.third.req.KnetB2bOrderQueryRequest;
import com.knet.order.model.dto.third.req.UpdatedOrderRequest;
import com.knet.order.model.dto.third.resp.KnetB2bOrderQueryVo;
import com.knet.order.model.dto.third.resp.OrderAndLabelVo;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.SubOrderGroupVo;
import com.knet.order.model.vo.SubOrderItemVo;
import com.knet.order.service.IApiOrdersService;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:34
 * @description:
 */
@Slf4j
@Service
public class ApiOrdersServiceImpl implements IApiOrdersService {
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private SysOrderItemMapper sysOrderItemMapper;
    @Resource
    private ISysOrderProcessService iSysOrderProcessService;

    @Override
    public List<SubOrderItemVo> getOrderItemsByPrentOrderId(String prentOrderId) {
        List<SysOrderItem> items = orderItemService.getOrderItemsByPrentOrderId(prentOrderId);
        Map<String, SubOrderItemVo> groupedItems = new HashMap<>(12);
        for (SysOrderItem item : items) {
            String groupKey = item.getSku() + "_" + item.getSize() + "_" + item.getPrice();
            if (groupedItems.containsKey(groupKey)) {
                SubOrderItemVo existingDto = groupedItems.get(groupKey);
                existingDto.setCount(existingDto.getCount() + item.getCount());
            } else {
                SubOrderItemVo newDto = SubOrderItemVo.create(item);
                groupedItems.put(groupKey, newDto);
            }
        }
        return new ArrayList<>(groupedItems.values());
    }

    @Override
    public SubOrderGroupVo getOrderGroupByPrentOrderId(String prentOrderId) {
        SysOrderGroup sysOrderGroup = orderGroupService.getOrderGroupByOrderId(prentOrderId);
        if (BeanUtil.isNotEmpty(sysOrderGroup)) {
            SubOrderGroupVo dto = new SubOrderGroupVo();
            dto.setParentOrderId(sysOrderGroup.getOrderId());
            dto.setStatus(sysOrderGroup.getStatus());
            dto.setTotalPrice(sysOrderGroup.getTotalAmount());
            return dto;
        }
        return null;
    }

    @Override
    public IPage<KnetB2bOrderQueryVo> getOrderItemsByPage(KnetB2bOrderQueryRequest request) {
        log.info("分页查询订单项，参数：{}", request);
        Page<OrderAndLabelVo> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<OrderAndLabelVo> db = sysOrderItemMapper.getOrderItemsByPage(page, request);
        return db.convert(KnetB2bOrderQueryVo::create);
    }

    @DistributedLock(key = "'KG_UPDATE_ORDER_STATUS:' + #request.hashCode()", expire = 1)
    @Override
    public boolean updateOrderStatus(UpdatedOrderRequest request) {
        return iSysOrderProcessService.smartUpdateOrderStatus(request);
    }

    @Override
    public SysOrderItem getOrderItemDetails(String itemId) {
        return orderItemService.getById(Long.parseLong(itemId));
    }

    @Override
    public List<SysOrderItem> queryOrderItemsByPrentOrderId(String prentOrderId) {
        return orderItemService.getOrderItemsByPrentOrderId(prentOrderId);
    }
}
