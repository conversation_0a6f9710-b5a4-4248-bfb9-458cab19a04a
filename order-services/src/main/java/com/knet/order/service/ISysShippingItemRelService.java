package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.entity.SysShippingItemRel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:50:02
 * @description: 针对表【SysShippingItemRel】的数据库操作Service
 */
public interface ISysShippingItemRelService extends IService<SysShippingItemRel> {
    /**
     * 保存物流单和商品的关联关系
     *
     * @param labelId 物流单ID
     * @param itemId  商品ID
     */
    void saveShippingItemRel(Long labelId, Long itemId);

    /**
     * 根据父订单ID查询已分配物流标签的订单项ID列表
     *
     * @param parentOrderId 父订单ID
     * @return 已分配物流标签的订单项ID列表
     */
    List<Long> getShippedItemIdsByParentOrderId(String parentOrderId);
}
