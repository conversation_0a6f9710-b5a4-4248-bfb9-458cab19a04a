package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.dto.resp.OrderShipInfo;
import com.knet.order.model.entity.SysShippingLabel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:50:02
 * @description: 针对表【sys_shipping_label(物流运单)】的数据库操作Service
 */
public interface ISysShippingLabelService extends IService<SysShippingLabel> {

    /**
     * 创建物流运单
     *
     * @param shippingLabel 物流运单信息
     * @return 创建的物流运单
     */
    SysShippingLabel createShippingLabel(SysShippingLabel shippingLabel);

    /**
     * 批量创建物流运单
     *
     * @param shippingLabels 物流运单信息列表
     * @return 创建的物流运单列表
     */
    List<SysShippingLabel> batchCreateShippingLabels(List<SysShippingLabel> shippingLabels);

    /**
     * 根据物流单号查询物流运单
     *
     * @param trackingNumber 物流单号
     * @return 物流运单
     */
    SysShippingLabel getShippingLabelByTrackingNumber(String trackingNumber);

    /**
     * 更新物流运单信息
     *
     * @param shippingLabel 物流运单信息
     * @return 是否更新成功
     */
    boolean updateShippingLabel(SysShippingLabel shippingLabel);

    /**
     * 根据父订单ID查询物流运单
     *
     * @param prentOrderId 父订单ID
     * @return 物流运单列表
     */
    List<OrderShipInfo> getShippingLabelsByPrentOrderId(String prentOrderId);
}
