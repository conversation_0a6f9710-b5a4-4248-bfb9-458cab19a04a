package com.knet.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.order.mapper.SysShippingItemRelMapper;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.entity.SysShippingItemRel;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysShippingItemRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:55:02
 * @description: 针对表【SysShippingItemRel】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysShippingItemRelServiceImpl extends ServiceImpl<SysShippingItemRelMapper, SysShippingItemRel> implements ISysShippingItemRelService {

    @Resource
    private ISysOrderItemService iSysOrderItemService;

    /**
     * 保存订单项与物流标签关联
     *
     * @param labelId 物流标签ID
     * @param itemId  订单项ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveShippingItemRel(Long labelId, Long itemId) {
        SysShippingItemRel shippingItemRel = SysShippingItemRel.builder()
                .labelId(labelId)
                .itemId(itemId)
                .build();
        this.save(shippingItemRel);
        log.info("保存订单项与物流标签关联: labelId={}, itemId={}", labelId, itemId);
    }

    /**
     * 根据父订单ID查询已分配物流标签的订单项ID列表
     *
     * @param parentOrderId 父订单ID
     * @return 已分配物流标签的订单项ID列表
     */
    @Override
    public List<Long> getShippedItemIdsByParentOrderId(String parentOrderId) {
        // 先获取父订单下的所有订单项
        List<SysOrderItem> orderItems = iSysOrderItemService.getOrderItemsByPrentOrderId(parentOrderId);
        if (orderItems.isEmpty()) {
            log.warn("父订单下没有订单项: parentOrderId={}", parentOrderId);
            return List.of();
        }
        // 获取所有订单项ID
        List<Long> itemIds = orderItems.stream().map(SysOrderItem::getItemId).toList();
        // 查询这些订单项中已经分配了物流标签的项
        LambdaQueryWrapper<SysShippingItemRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysShippingItemRel::getItemId, itemIds);
        List<SysShippingItemRel> relList = this.list(queryWrapper);
        // 返回已分配物流标签的订单项ID列表
        return relList.stream().map(SysShippingItemRel::getItemId).toList();
    }

}
