package com.knet.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.dto.message.InventoryFailedMessage;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.mapper.SysOrderItemMapper;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.IInventoryConsumerService;
import com.knet.order.service.ISysOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 14:01
 * @description: 消费库存消息服务实现
 */
@Slf4j
@Service
public class InventoryConsumerServiceImpl implements IInventoryConsumerService {

    @Resource
    private SysOrderItemMapper sysOrderItemMapper;
    @Resource
    private ISysOrderProcessService orderProcessService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processInventoryFailed(String messageBody) {
        InventoryFailedMessage failedEvent = JSON.parseObject(messageBody, InventoryFailedMessage.class);
        log.info("订单服务处理库存扣减失败补偿: {}", messageBody);
        try {
            String orderId = failedEvent.getOrderId();
            orderProcessService.cancelOrder(orderId);
            // 清除用户订单缓存
            clearUserOrderListCache(failedEvent.getUserId());
            log.info("库存扣减失败补偿处理完成，订单状态已更新为已取消: orderId={}, reason={}", orderId, failedEvent.getFailureReason());
        } catch (Exception e) {
            log.error("处理库存扣减失败补偿异常: orderId={}, error={}", failedEvent.getOrderId(), e.getMessage());
            throw new ServiceException("处理库存扣减失败补偿异常: " + e.getMessage());
        }
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            String cacheKeyPattern = "order-service:orderList:" + userId + ":*";
            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processInventoryLockSuccess(InventoryLockSuccessMessage lockSuccessMessage) {
        String orderId = lockSuccessMessage.getOrderId();
        Long userId = lockSuccessMessage.getUserId();
        List<InventoryLockSuccessMessage.LockedProductInfo> lockedProducts = lockSuccessMessage.getLockedProducts();
        log.info("开始处理库存锁定成功消息: orderId={}, userId={}, 锁定商品数量={}",
                orderId, userId, lockedProducts != null ? lockedProducts.size() : 0);
        if (CollUtil.isEmpty(lockedProducts)) {
            log.warn("锁定商品列表为空: orderId={}", orderId);
            return;
        }
        for (InventoryLockSuccessMessage.LockedProductInfo lockedProduct : lockedProducts) {
            updateOrderItemsWithLockInfo(orderId, lockedProduct);
        }
        log.info("库存锁定成功消息处理完成: orderId={}", orderId);
    }

    /**
     * 更新订单项的锁定信息
     *
     * @param orderId       订单ID
     * @param lockedProduct 锁定的商品信息
     */
    private void updateOrderItemsWithLockInfo(String orderId, InventoryLockSuccessMessage.LockedProductInfo lockedProduct) {
        String sku = lockedProduct.getSku();
        String size = lockedProduct.getSize();
        String priceStr = lockedProduct.getPrice();
        List<InventoryLockSuccessMessage.ProductDetail> productDetails = lockedProduct.getProductDetails();
        if (CollUtil.isEmpty(productDetails)) {
            log.warn("商品详情列表为空: orderId={}, sku={}, size={}", orderId, sku, size);
            return;
        }
        // 将价格字符串转换为BigDecimal
        BigDecimal price = new BigDecimal(priceStr);
        // 查询匹配的订单项
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysOrderItem::getParentOrderId, orderId)
                .eq(SysOrderItem::getSku, sku)
                .eq(SysOrderItem::getSize, size)
                .eq(SysOrderItem::getPrice, price)
                .orderByAsc(SysOrderItem::getItemId);
        List<SysOrderItem> orderItems = sysOrderItemMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(orderItems)) {
            log.warn("未找到匹配的订单项: orderId={}, sku={}, size={}, price={}",
                    orderId, sku, size, price);
            return;
        }
        if (orderItems.size() != productDetails.size()) {
            log.warn("订单项数量与锁定商品数量不匹配: orderId={}, sku={}, size={}, 订单项数量={}, 锁定商品数量={}",
                    orderId, sku, size, orderItems.size(), productDetails.size());
        }
        // 按顺序更新订单项的oneId和knetListingId
        int updateCount = Math.min(orderItems.size(), productDetails.size());
        for (int i = 0; i < updateCount; i++) {
            SysOrderItem orderItem = orderItems.get(i);
            InventoryLockSuccessMessage.ProductDetail productDetail = productDetails.get(i);
            LambdaUpdateWrapper<SysOrderItem> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysOrderItem::getItemId, orderItem.getItemId())
                    .set(SysOrderItem::getOneId, productDetail.getOneId())
                    .set(SysOrderItem::getKnetListingId, productDetail.getKnetListingId())
                    .set(SysOrderItem::getWarehouse, productDetail.getWarehouse())
                    .set(SysOrderItem::getSource, productDetail.getSource());
            int updated = sysOrderItemMapper.update(null, updateWrapper);
            if (updated > 0) {
                log.info("订单项锁定信息更新成功: itemId={}, oneId={}, knetListingId={}, warehouse={},source={}",
                        orderItem.getItemId(), productDetail.getOneId(), productDetail.getKnetListingId(), productDetail.getWarehouse(), productDetail.getSource());
            } else {
                log.error("订单项锁定信息更新失败: itemId={}, oneId={}, knetListingId={}, warehouse={},source={}",
                        orderItem.getItemId(), productDetail.getOneId(), productDetail.getKnetListingId(), productDetail.getWarehouse(), productDetail.getSource());
            }
        }
    }
}
