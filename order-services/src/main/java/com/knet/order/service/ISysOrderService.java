package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.model.entity.SysOrder;
import com.knet.order.model.vo.OrderItemDataVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:35
 * @description: 针对表【sys_order(B2B子订单表)】的数据库操作Service
 */
public interface ISysOrderService extends IService<SysOrder> {

    /**
     * 创建子订单
     *
     * @param parentOrderId 母订单ID
     * @param userId        用户ID
     * @param sku           商品SKU
     * @param itemDataList  商品明细数据
     * @return 子订单
     */
    SysOrder createSysOrder(String parentOrderId, Long userId, String sku, List<OrderItemDataVo> itemDataList);

    /**
     * 根据父订单ID更新子订单状态
     *
     * @param parentOrderId 父订单ID
     * @param status        新状态
     * @return 是否更新成功
     */
    boolean updateOrderStatusByParentId(String parentOrderId, KnetOrderGroupStatus status);

    /**
     * 检查所有关联的item订单是否都处于 指定状态
     *
     * @param parentOrderId 父订单ID
     * @param status        指定状态
     * @return 是否所有订单项都处于指定状态
     */
    boolean checkAllItemsStatus(String parentOrderId, KnetOrderItemStatus status);
}
