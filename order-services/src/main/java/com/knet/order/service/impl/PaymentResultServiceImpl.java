package com.knet.order.service.impl;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.PaymentResultMessage;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.enums.KnetPaymentFlowStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.IPaymentResultService;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/17 13:56
 * @description: 支付结果 消息处理服务
 */
@Service
@Slf4j
public class PaymentResultServiceImpl implements IPaymentResultService {
    @Resource
    private ISysOrderGroupService orderGroupService;
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderItemService orderItemService;

    /**
     * 处理支付结果
     *
     * @param messageBody 消息体
     */
    @Override
    public void processPaymentResult(String messageBody) {
        PaymentResultMessage message = JSON.parseObject(messageBody, PaymentResultMessage.class);
        log.info("订单服务处理支付结果消息: {}", messageBody);
        try {
            String orderId = message.getOrderId();
            String paymentStatus = message.getStatus();
            // 根据支付状态更新订单状态
            if (KnetPaymentFlowStatus.SUCCESS.getName().equals(paymentStatus)) {
                // 支付成功，更新订单状态为已支付
                updateOrderStatus(orderId, KnetOrderGroupStatus.PAID);
                log.info("订单支付成功，订单状态已更新: orderId={}", orderId);
            }
            if (KnetPaymentFlowStatus.FAILED.getName().equals(paymentStatus)) {
                // 支付失败，更新订单状态为支付失败
                updateOrderStatus(orderId, KnetOrderGroupStatus.PAY_FAILED);
                log.info("订单支付失败，订单状态已更新为支付失败: orderId={}", orderId);
            }
            //清除订单缓存
            clearUserOrderListCache(message.getUserId());
        } catch (Exception e) {
            log.error("处理支付结果失败: orderId={}, error={}", message.getOrderId(), e.getMessage());
            throw new ServiceException("处理支付结果失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status  新状态
     */
    private void updateOrderStatus(String orderId, KnetOrderGroupStatus status) {
        // 更新父订单状态
        boolean groupUpdated = orderGroupService.updateOrderStatus(orderId, status);
        if (!groupUpdated) {
            throw new ServiceException("更新父订单状态失败: " + orderId);
        }
        // 更新子订单状态
        boolean orderUpdated = orderService.updateOrderStatusByParentId(orderId, status);
        if (!orderUpdated) {
            throw new ServiceException("更新子订单状态失败: " + orderId);
        }
        // 更新item订单状态
        switch (status) {
            case PAID -> orderItemService.updateOrderStatusByParentId(orderId, KnetOrderItemStatus.PAID);
            case PAY_FAILED -> orderItemService.updateOrderStatusByParentId(orderId, KnetOrderItemStatus.PAY_FAILED);
            default -> log.info("更新item订单状态: orderId={}, status={}", orderId, status.getName());
        }
        log.info("订单状态更新成功: orderId={}, status={}", orderId, status.getName());
    }

    /**
     * 清除用户订单列表缓存
     * 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
     */
    private void clearUserOrderListCache(Long userId) {
        try {
            // 构建缓存key模式，匹配该用户的所有订单列表缓存
            String cacheKeyPattern = "order-service:orderList:" + userId + ":*";
            // 使用RedisCacheUtil的deleteByPattern方法进行模糊匹配删除
            RedisCacheUtil.deleteByPattern(cacheKeyPattern);
            log.info("已清除用户订单列表缓存，用户ID: {}, 缓存key模式: {}", userId, cacheKeyPattern);
        } catch (Exception e) {
            log.warn("清除用户订单列表缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
        }
    }
}
