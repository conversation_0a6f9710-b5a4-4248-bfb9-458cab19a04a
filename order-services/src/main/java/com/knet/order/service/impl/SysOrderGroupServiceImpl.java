package com.knet.order.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.utils.RandomStrUtil;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.vo.OrderItemDataVo;
import com.knet.order.service.ISysOrderGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderGroupServiceImpl extends ServiceImpl<SysOrderGroupMapper, SysOrderGroup> implements ISysOrderGroupService {

    @Resource
    private RandomStrUtil randomStrUtil;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysOrderGroup createOrderGroup(Long userId, Map<String, List<OrderItemDataVo>> productGroupMap, Long addressId) {
        String parentOrderId = randomStrUtil.getOrderId();
        BigDecimal totalAmount = calculateTotalAmountFromGroups(productGroupMap);
        SysOrderGroup orderGroup = SysOrderGroup.createOrderGroup(parentOrderId, userId, totalAmount, addressId);
        this.save(orderGroup);
        return orderGroup;
    }


    /**
     * 计算母订单总金额（从商品分组计算）
     */
    private BigDecimal calculateTotalAmountFromGroups(Map<String, List<OrderItemDataVo>> productGroupMap) {
        return productGroupMap.values()
                .stream()
                .flatMap(List::stream)
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public SysOrderGroup getOrderGroupByOrderId(String orderId) {
        LambdaQueryWrapper<SysOrderGroup> groupQueryWrapper = new LambdaQueryWrapper<>();
        groupQueryWrapper.eq(SysOrderGroup::getOrderId, orderId);
        return this.getOne(groupQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(String orderId, KnetOrderGroupStatus status) {
        log.info("更新父订单状态: orderId={}, status={}", orderId, status.getName());
        try {
            LambdaUpdateWrapper<SysOrderGroup> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(SysOrderGroup::getOrderId, orderId)
                    .set(SysOrderGroup::getStatus, status);
            boolean updated = this.update(null, updateWrapper);
            log.info("父订单状态更新结果: orderId={}, status={}, result={}", orderId, status.getName(), updated);
            return updated;
        } catch (Exception e) {
            log.error("更新父订单状态失败: orderId={}, status={}, error={}", orderId, status.getName(), e.getMessage(), e);
            return false;
        }
    }
}
