package com.knet.order.service;

import com.knet.common.dto.message.InventoryLockSuccessMessage;

/**
 * <AUTHOR>
 * @date 2025/6/17 14:01
 * @description: 消费库存消息服务
 */
public interface IInventoryConsumerService {

    /**
     * 处理库存扣减失败补偿
     *
     * @param messageBody 消息体
     */
    void processInventoryFailed(String messageBody);

    /**
     * 处理库存锁定成功消息
     * 将锁定的商品信息（oneId、knetListingId,wareHouse）回写到订单项中
     *
     * @param lockSuccessMessage 库存锁定成功消息
     */
    void processInventoryLockSuccess(InventoryLockSuccessMessage lockSuccessMessage);
}
