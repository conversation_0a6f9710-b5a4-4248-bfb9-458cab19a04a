package com.knet.order;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:10
 * @description: 订单服务 主启动类
 */
@EnableRetry
@EnableCaching
@ComponentScan(basePackages = {"com.knet.order", "com.knet.common"})
@MapperScan("com.knet.order.mapper")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class OrderServicesApplication {
    public static void main(String[] args) {
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(OrderServicesApplication.class);
        System.out.println(" 🚀Order Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
