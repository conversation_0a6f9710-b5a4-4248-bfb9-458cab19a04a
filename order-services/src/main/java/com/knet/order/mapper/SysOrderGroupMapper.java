package com.knet.order.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.order.model.dto.req.AdminOrderListQueryRequest;
import com.knet.order.model.dto.req.OrderListQueryRequest;
import com.knet.order.model.dto.resp.OrderListResponse;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.vo.SubOrderSummaryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order_group(B2B订单主表)】的数据库操作Mapper
 */
@Mapper
public interface SysOrderGroupMapper extends BaseMapper<SysOrderGroup> {

    /**
     * 分页查询订单列表（父订单信息）
     *
     * @param page    分页参数
     * @param request 查询条件
     * @return 父订单列表
     */
    IPage<OrderListResponse.ParentOrderResponse> queryOrderList(Page<OrderListResponse.ParentOrderResponse> page, @Param("request") OrderListQueryRequest request);

    /**
     * 查询指定父订单的子订单汇总信息
     *
     * @param parentOrderIds 父订单ID列表
     * @return 子订单汇总列表
     */
    List<SubOrderSummaryVo> querySubOrderSummary(@Param("parentOrderIds") List<String> parentOrderIds);

    /**
     * 管理员分页查询所有用户的订单列表（父订单信息）
     *
     * @param page    分页参数
     * @param request 管理员查询条件
     * @return 父订单列表
     */
    IPage<OrderListResponse.ParentOrderResponse> queryOrderListForAdmin(Page<OrderListResponse.ParentOrderResponse> page, @Param("request") AdminOrderListQueryRequest request);
}
