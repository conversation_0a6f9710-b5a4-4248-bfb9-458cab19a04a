package com.knet.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.order.model.dto.resp.OrderShipInfo;
import com.knet.order.model.entity.SysShippingLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-13 14:45:02
 * @description: 针对表【sys_shipping_label(物流运单)】的数据库操作Mapper
 */
@Mapper
public interface SysShippingLabelMapper extends BaseMapper<SysShippingLabel> {
    /**
     * 根据订单项ID列表查询物流运单
     *
     * @param itemIds 订单项ID列表
     * @return 物流运单列表
     */
    List<OrderShipInfo> getListByItemIds(@Param("itemIds") List<Long> itemIds);
}
