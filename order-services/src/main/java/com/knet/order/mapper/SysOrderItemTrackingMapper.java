package com.knet.order.mapper;

import com.knet.order.model.vo.OrderItemTrackingVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30 16:35
 * @description: 订单项物流追踪Mapper
 */
@Mapper
public interface SysOrderItemTrackingMapper {

    /**
     * 查询处于指定状态的订单项及其物流信息
     *
     * @param status 订单项状态
     * @return 订单项物流追踪信息列表
     */
    List<OrderItemTrackingVO> getOrderItemsWithTrackingInfo(@Param("status") String status);

    /**
     * 统计处于指定状态的订单项数量
     *
     * @param status 订单项状态
     * @return 订单项数量
     */
    int countInTransitOrderItems(@Param("status") String status);

    /**
     * 分页查询处于指定状态的订单项及其物流信息
     *
     * @param status 订单项状态
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 订单项物流追踪信息列表
     */
    List<OrderItemTrackingVO> getOrderItemsWithTrackingInfoByPage(
            @Param("status") String status,
            @Param("offset") int offset,
            @Param("limit") int limit);
}
