package com.knet.order.system.handler.impl;

import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.handler.OrderStatusUpdateStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/19 13:24
 * @description: 运输中状态更新策略实现
 */
@Component
public class InTransitStatusHandler implements OrderStatusUpdateStrategy {
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderGroupService orderGroupService;

    @Override
    public KnetOrderItemStatus getHandleType() {
        return KnetOrderItemStatus.IN_TRANSIT;
    }

    /**
     * 订单已发货，所有item订单都处于已发货的状态，其对应的父子订单全部处于已发货的状态
     *
     * @param parentOrderId 父订单ID
     * @param orderNo       订单号
     * @return 是否更新成功
     */
    @Override
    public boolean handleStatusUpdate(String parentOrderId, String orderNo) {
        // 1. 检查所有关联的item订单是否都处于运输中
        boolean allItemsShipped = orderService.checkAllItemsStatus(parentOrderId, KnetOrderItemStatus.IN_TRANSIT);
        // 如果不是所有item都已发货，则不进行状态更新
        if (!allItemsShipped) {
            boolean orderUpdated = orderService.updateOrderStatusByParentId(parentOrderId, KnetOrderGroupStatus.MULTIPLE_STATES);
            // 2. 更新订单组状态为部分发货
            boolean groupUpdated = orderGroupService.updateOrderStatus(parentOrderId, KnetOrderGroupStatus.MULTIPLE_STATES);
            return orderUpdated && groupUpdated;
        }
        boolean orderUpdated = orderService.updateOrderStatusByParentId(parentOrderId, KnetOrderGroupStatus.IN_TRANSIT);
        boolean groupUpdated = orderGroupService.updateOrderStatus(parentOrderId, KnetOrderGroupStatus.IN_TRANSIT);
        return orderUpdated && groupUpdated;
    }
}