package com.knet.order.system.handler.impl;

import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.order.openfeign.ApiDelayedProvider;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/26 13:46
 * @description: knet 延迟消息服务 降级处理机制
 */
@Component
public class ApiDelayedServiceFallbackImpl implements ApiDelayedProvider {
    @Override
    public HttpResult<String> addDelayedMessage(DelayedMessage delayedMessage) {
        return HttpResult.error("延迟消息 服务暂时不可用，请稍后重试");
    }
}
