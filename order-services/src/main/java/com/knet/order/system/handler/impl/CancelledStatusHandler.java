package com.knet.order.system.handler.impl;

import com.knet.common.enums.KnetOrderGroupStatus;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.handler.OrderStatusUpdateStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/23 14:49
 * @description: 取消状态策略实现
 */
@Slf4j
@Component
public class CancelledStatusHandler implements OrderStatusUpdateStrategy {
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderGroupService orderGroupService;

    @Override
    public KnetOrderItemStatus getHandleType() {
        return KnetOrderItemStatus.CANCELLED;
    }


    /**
     * 处理订单取消状态更新
     * 如果所有item订单都处于已取消的状态，其对应的父子订单处于完全取消的状态
     * 否则处于部分取消状态
     *
     * @param parentOrderId 父订单ID
     * @param orderNo       订单号
     * @return 是否更新成功
     */
    @Override
    public boolean handleStatusUpdate(String parentOrderId, String orderNo) {
        // 检查所有关联的item订单是否都处于已取消状态
        boolean allItemsCancelled = orderService.checkAllItemsStatus(parentOrderId, KnetOrderItemStatus.CANCELLED);
        KnetOrderGroupStatus targetStatus;
        if (allItemsCancelled) {
            // 如果所有item都已取消，则设置为完全取消状态
            targetStatus = KnetOrderGroupStatus.CANCELLED;
        } else {
            // 否则设置为部分取消状态
            targetStatus = KnetOrderGroupStatus.MULTIPLE_STATES;
        }
        boolean orderUpdated = orderService.updateOrderStatusByParentId(parentOrderId, targetStatus);
        boolean groupUpdated = orderGroupService.updateOrderStatus(parentOrderId, targetStatus);
        return orderUpdated && groupUpdated;
    }
}
