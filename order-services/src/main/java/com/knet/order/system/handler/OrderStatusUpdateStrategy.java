package com.knet.order.system.handler;

import com.knet.common.enums.KnetOrderItemStatus;

/**
 * <AUTHOR>
 * @date 2025/6/19 13:21
 * @description:
 */
public interface OrderStatusUpdateStrategy {

    /**
     * 获取处理的订单状态类型
     *
     * @return 状态类型
     */
    KnetOrderItemStatus getHandleType();

    /**
     * 处理订单状态更新
     *
     * @param parentOrderId 父订单ID
     * @param orderNo       订单号
     * @return 处理结果
     */
    boolean handleStatusUpdate(String parentOrderId, String orderNo);
}
