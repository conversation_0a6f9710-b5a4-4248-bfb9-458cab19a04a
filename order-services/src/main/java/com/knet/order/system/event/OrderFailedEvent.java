package com.knet.order.system.event;

import com.knet.order.model.dto.req.CreateOrderRequest;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:00
 * @description: 订单创建失败事件
 */
@Getter
public class OrderFailedEvent extends ApplicationEvent {
    /**
     * 创建订单请求
     */
    private final CreateOrderRequest request;

    public OrderFailedEvent(Object source, CreateOrderRequest request) {
        super(source);
        //深拷贝
        this.request = request;
    }
}
