package com.knet.order.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.system.event.OrderFailedEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:24
 * @description: OrderEventMessageListener 订单事件消息监听器
 */
@Component
public class OrderFailedEventMessageListener {
    @Resource
    private OrderProducer orderProducer;

    @TransactionalEventListener(
            classes = OrderFailedEvent.class,
            phase = TransactionPhase.AFTER_COMPLETION
    )
    public void handleOrderFailedEvent(OrderFailedEvent event) {
        CreateOrderRequest request = event.getRequest();
        orderProducer.sendOrderCreateFailedEvent(JSON.toJSONString(request));
    }
}
