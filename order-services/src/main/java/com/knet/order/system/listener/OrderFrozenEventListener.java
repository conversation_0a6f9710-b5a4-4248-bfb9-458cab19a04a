package com.knet.order.system.listener;

import com.knet.order.service.IDistributionShippingLabelService;
import com.knet.order.system.event.OrderFrozenEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/13 16:00
 * @description: 订单待发货监听器
 */
@Component
public class OrderFrozenEventListener {

    @Resource
    private IDistributionShippingLabelService iDistributionShippingLabelService;

    @EventListener(classes = OrderFrozenEvent.class)
    public void handleOrderFrozenEvent(OrderFrozenEvent event) {
        //从KG获取物流标签 根据仓库信息，同一个仓库10件商品一个shipping_label
        iDistributionShippingLabelService.distributionShippingLabel(event.getOrderId());
    }
}
