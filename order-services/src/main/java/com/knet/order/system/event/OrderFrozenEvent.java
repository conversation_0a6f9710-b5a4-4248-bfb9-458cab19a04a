package com.knet.order.system.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:53
 * @description: 订单需要获取shipping_label事件
 */
@Getter
public class OrderFrozenEvent extends ApplicationEvent {
    private final String orderId;

    public OrderFrozenEvent(Object source, String orderId) {
        super(source);
        this.orderId = orderId;
    }
}
