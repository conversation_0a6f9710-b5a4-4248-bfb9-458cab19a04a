package com.knet.order.system.event;

import com.knet.order.model.entity.SysOrderGroup;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:09
 * @description: 订单创建事件
 */
@Getter
public class OrderCreatedEvent extends ApplicationEvent {
    private final SysOrderGroup order;

    public OrderCreatedEvent(Object source, SysOrderGroup order) {
        super(source);
        //深拷贝
        this.order = order;
    }
}
