package com.knet.order.system.handler;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/7/1 16:19
 * @description: KnetGroup到手价计算实现
 */
@Slf4j
@Component
public class ReversePriceCalculator {

    /**
     * KnetGroup手续费
     */
    public static final Float B2B_FEE = 1.1f;

    /**
     * 获取卖家到手价
     *
     * @param finalDollar 最终价格（美元，如19.09美元）
     * @return 原始价格（美元，保留2位小数），若无解返回null
     */
    public static BigDecimal getSellerOwingPrice(BigDecimal finalDollar) {
        return finalDollar;
    }

    /**
     * 获取knetGroup 到手价
     * 高于100块乘法10% ，低于100块，固定加10块
     *
     * @param finalDollar 最终价格（美元整数）
     * @return 原始价格（美元整数），若无解返回null
     */
    public static BigDecimal getKgOwningPrice(BigDecimal finalDollar) {
        // 1. 原始价格 < 100美元（固定加价10美元）
        // 100美元 + 10美元 = 110美元
        if (NumberUtil.isLess(finalDollar, new BigDecimal("110"))) {
            BigDecimal result = NumberUtil.add(finalDollar, 10).setScale(2, RoundingMode.DOWN);
            log.info("获取卖家到手价,listingPrice{},sellerOwingPrice{}", finalDollar, result);
            return result;
        }
        // 2. 原始价格 ≥ 100美元（加价10%）
        // 保留2位小数（向下取整）
        BigDecimal result = NumberUtil.mul(finalDollar, B2B_FEE, 2);
        log.info("获取卖家到手价,listingPrice{},sellerOwingPrice{}", finalDollar, result);
        return result;
    }
}
