package com.knet.order.system.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.knet.common.exception.ServiceException;
import com.knet.order.system.config.AuthConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/2/13 13:23
 * @description: jwt工具类
 */
@Component
public class JwtUtil {
    @Autowired
    private AuthConfig authConfig;


    /**
     * 从token中获取账户account
     *
     * @param token token
     * @return account
     */
    public String getAccountFromToken(String token) {
        JWT jwt = JWTUtil.parseToken(token);
        return jwt.getPayload("account").toString();
    }

    /**
     * 从token中获取用户名
     *
     * @param token token
     * @return 角色名称
     */
    public String getRolerFromToken(String token) {
        JWT jwt = JWTUtil.parseToken(token);
        Object role = jwt.getPayload("role");
        if (BeanUtil.isEmpty(role)) {
            throw new ServiceException("token invalid");
        }
        return role.toString();
    }

    /**
     * 从token中获取userId
     *
     * @param token token
     * @return 用户id
     */
    public String getUserIdFromToken(String token) {
        JWT jwt = JWTUtil.parseToken(token);
        Object userId = jwt.getPayload("userId");
        if (BeanUtil.isEmpty(userId)) {
            throw new ServiceException("token invalid");
        }
        return userId.toString();
    }

    /**
     * 验证token
     *
     * @param token token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            JWT jwt = JWTUtil.parseToken(token);
            return jwt.setKey(authConfig.getAuthSecretKey().getBytes()).verify();
        } catch (Exception e) {
            return false;
        }
    }
}
