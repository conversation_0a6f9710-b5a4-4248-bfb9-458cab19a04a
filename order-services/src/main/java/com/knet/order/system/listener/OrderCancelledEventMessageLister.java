package com.knet.order.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.OrderRefundMessage;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.system.event.OrderCancelledEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/25 09:42
 * @description: 订单取消消息监听器
 */
@Component
public class OrderCancelledEventMessageLister {

    @Resource
    private OrderProducer orderProducer;

    @TransactionalEventListener(
            classes = OrderCancelledEvent.class,
            // 事务提交后执行
            phase = TransactionPhase.AFTER_COMMIT
    )
    public void handleOrderCreatedEvent(OrderCancelledEvent event) {
        OrderRefundMessage cancelledMessage = OrderRefundMessage.builder()
                .prentOrderId(event.getPrentOrderId())
                .orderItemId(event.getOrderItemId())
                .orderItemNo(event.getOrderItemNo())
                .userId(event.getUserId())
                .amount(event.getAmount())
                .timestamp(event.getTimestamp())
                .releaseInventory(true)
                .build();
        orderProducer.sendOrderRefundEvent(JSON.toJSONString(cancelledMessage));
    }
}
