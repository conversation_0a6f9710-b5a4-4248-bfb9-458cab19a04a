package com.knet.order.system.schedule;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderProcessService;
import com.knet.order.system.event.OrderFrozenEvent;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.knet.common.constants.OrderServicesConstants.ORDER_PAID_TIMEOUT_TIME;

/**
 * <AUTHOR>
 * @date 2025/6/13 14:47
 * @description: 订单待发货定时任务
 */
@Slf4j
@Component
public class OrderNeedToShipJob {
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private ISysOrderProcessService orderProcessService;

    /**
     * 每5分钟执行一次，订单已经支付超过10分钟后，转换为FROZEN 冻结
     */
    @XxlJob("needToShipOrder")
    public ReturnT<String> needToShipOrder() {
        log.info("任务 needToShipOrder 开始执行 ");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("needToShipOrder");
        stopWatch.start();
        // 查询条件：已支付状态的订单
        LambdaQueryWrapper<SysOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOrderItem::getStatus, KnetOrderItemStatus.PAID);
        // 计算订单支付超时时间点
        LocalDateTime timeoutThreshold = LocalDateTime.now().minus(Duration.ofMinutes(ORDER_PAID_TIMEOUT_TIME));
        queryWrapper.lt(SysOrderItem::getUpdateTime, timeoutThreshold);
        // 一次性获取所有已支付状态的订单
        List<SysOrderItem> sysOrderItems = orderItemService.list(queryWrapper);
        long updateCount = sysOrderItems.size();
        if (CollUtil.isNotEmpty(sysOrderItems)) {
            frozenOrderAndPushEvent(sysOrderItems);
            log.info("处理完成: 成功处理{}条订单", updateCount);
        } else {
            log.info("无符合条件订单");
        }
        // 记录执行结果
        stopWatch.stop();
        String resultMsg = updateCount > 0 ? "成功更新" + updateCount + "条订单状态为冻结" : "无符合条件订单";
        log.info("任务结束: {}", resultMsg);
        XxlJobHelper.log(resultMsg + "，耗时: {}ms", stopWatch.getTotalTimeMillis());
        return ReturnT.SUCCESS;
    }

    /**
     * 冻结订单并推送事件
     *
     * @param orderItems 订单明细项集合
     */
    private void frozenOrderAndPushEvent(List<SysOrderItem> orderItems) {
        String parentOrderId = orderItems.get(0).getParentOrderId();
        boolean updateResult = orderProcessService.freezeOrderItem(orderItems);
        if (updateResult) {
            applicationEventPublisher.publishEvent(new OrderFrozenEvent(this, parentOrderId));
            log.info("为订单组 {} 发送了 OrderPendingShipmentEvent 事件", parentOrderId);
        } else {
            log.warn("订单组 {} 状态更新失败", parentOrderId);
        }
    }
}
