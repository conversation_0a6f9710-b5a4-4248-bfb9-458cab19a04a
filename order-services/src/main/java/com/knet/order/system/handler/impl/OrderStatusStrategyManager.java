package com.knet.order.system.handler.impl;

import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.exception.ServiceException;
import com.knet.order.system.handler.OrderStatusUpdateStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/19 13:22
 * @description: 订单状态策略管理器
 */
@Slf4j
@Component
public class OrderStatusStrategyManager {
    private final Map<KnetOrderItemStatus, OrderStatusUpdateStrategy> strategyMap = new HashMap<>();

    @Autowired
    public OrderStatusStrategyManager(List<OrderStatusUpdateStrategy> strategies) {
        strategies.forEach(strategy -> {
            strategyMap.put(strategy.getHandleType(), strategy);
            log.info("注册订单状态更新策略: {} -> {}", strategy.getHandleType(), strategy.getClass().getSimpleName());
        });
        log.info("已加载{}个订单状态更新策略", strategyMap.size());
    }

    public OrderStatusUpdateStrategy getStrategy(KnetOrderItemStatus status) {
        OrderStatusUpdateStrategy strategy = strategyMap.get(status);
        if (strategy == null) {
            throw new ServiceException("未找到状态 " + status + " 对应的更新策略");
        }
        return strategy;
    }
}
