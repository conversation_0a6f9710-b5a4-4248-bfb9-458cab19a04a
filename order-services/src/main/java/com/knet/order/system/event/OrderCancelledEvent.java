package com.knet.order.system.event;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/24 16:00
 * @description: 订单取消事件
 */
@Getter
public class OrderCancelledEvent extends ApplicationEvent {

    @Schema(description = "关联订单ItemId", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private final Long orderItemId;

    @Schema(description = "关联订单ItemNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private final String orderItemNo;

    @Schema(description = "关联订单父id", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDI-123456789012345678")
    private final String prentOrderId;

    @Schema(description = "退款金额(美元)", requiredMode = Schema.RequiredMode.REQUIRED, example = "100.00")
    private final BigDecimal amount;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private final Long userId;

    public OrderCancelledEvent(Object source, Long orderItemId, String prentOrderId, BigDecimal amount, Long userId, String orderItemNo) {
        super(source);
        this.orderItemId = orderItemId;
        this.prentOrderId = prentOrderId;
        this.amount = amount;
        this.userId = userId;
        this.orderItemNo = orderItemNo;
    }
}
