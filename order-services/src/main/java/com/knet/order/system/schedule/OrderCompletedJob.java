package com.knet.order.system.schedule;

import cn.hutool.core.util.StrUtil;
import com.knet.common.base.HttpResult;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.order.mapper.SysOrderItemTrackingMapper;
import com.knet.order.model.dto.third.resp.KnetGroupActivityVO;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.model.vo.OrderItemTrackingVO;
import com.knet.order.openfeign.ApiKnetGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.knet.common.constants.OrderServicesConstants.ORDER_COMPLETED_BATCH_SIZE;

/**
 * <AUTHOR>
 * @date 2025/6/30 15:27
 * @description: 根据物流状态变更订单状态
 */
@Slf4j
@Component
public class OrderCompletedJob {

    @Resource
    private ISysOrderItemService sysOrderItemService;
    @Resource
    private ApiKnetGroupService apiKnetGroupService;
    @Resource
    private SysOrderItemTrackingMapper sysOrderItemTrackingMapper;


    /**
     * 每12小时执行一次，检查运输中订单的物流状态并更新订单状态
     * 注意：此方法需要在XXL-Job管理平台配置，JobHandler名称为：checkOrderLogisticsStatus
     * 建议配置Cron表达式：0 0 0/12 * * ? （每天0点和12点执行）
     */
    @XxlJob("checkOrderLogisticsStatus")
    public ReturnT<String> checkOrderStatus() {
        log.info("开始执行订单物流状态检查任务");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 订单物流状态检查任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("checkOrderLogisticsStatus");
        stopWatch.start();
        try {
            // 1. 获取总数据量，用于分批处理
            int totalCount = sysOrderItemTrackingMapper.countInTransitOrderItems(KnetOrderItemStatus.IN_TRANSIT.name());
            if (totalCount == 0) {
                String message = "没有处于运输中且有物流信息的订单需要检查";
                log.info(message);
                XxlJobHelper.log(message);
                stopWatch.stop();
                XxlJobHelper.log("任务执行完成，耗时: {}ms", stopWatch.getTotalTimeMillis());
                return ReturnT.SUCCESS;
            }
            log.info("共找到{}个处于运输中且有物流信息的订单项需要检查", totalCount);
            XxlJobHelper.log("共找到{}个处于运输中且有物流信息的订单项需要检查", totalCount);
            // 2. 计算需要处理的批次数
            int batchCount = (totalCount + ORDER_COMPLETED_BATCH_SIZE - 1) / ORDER_COMPLETED_BATCH_SIZE;
            log.info("将分{}批处理订单项，每批{}个", batchCount, ORDER_COMPLETED_BATCH_SIZE);
            int totalUpdatedCount = 0;
            // 3. 分批处理
            for (int i = 0; i < batchCount; i++) {
                int offset = i * ORDER_COMPLETED_BATCH_SIZE;
                log.info("开始处理第{}批订单项，偏移量: {}", i + 1, offset);
                List<OrderItemTrackingVO> batchItems =
                        sysOrderItemTrackingMapper.getOrderItemsWithTrackingInfoByPage(
                                KnetOrderItemStatus.IN_TRANSIT.name(), offset, ORDER_COMPLETED_BATCH_SIZE);
                if (CollectionUtils.isEmpty(batchItems)) {
                    log.info("第{}批没有需要处理的订单项", i + 1);
                    continue;
                }
                int updatedCount = processBatch(batchItems);
                totalUpdatedCount += updatedCount;
                log.info("第{}批处理完成，更新了{}个订单项状态", i + 1, updatedCount);
            }
            stopWatch.stop();
            String resultMsg = String.format("订单状态检查任务执行完成，共检查%d个订单项，更新%d个订单项状态", totalCount, totalUpdatedCount);
            log.info(resultMsg);
            XxlJobHelper.log(resultMsg + "，耗时: {}ms", stopWatch.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            stopWatch.stop();
            String errorMsg = "订单状态检查任务执行异常: " + e.getMessage();
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg);
            XxlJobHelper.log("任务执行失败，耗时: {}ms", stopWatch.getTotalTimeMillis());
            return ReturnT.FAIL;
        }
    }

    /**
     * 处理一批订单项
     *
     * @param orderItemsWithTracking 订单项批次数据
     * @return 更新的订单项数量
     */
    private int processBatch(List<OrderItemTrackingVO> orderItemsWithTracking) {
        // 1. 按物流单号分组，减少对KG服务的调用次数
        Map<String, List<OrderItemTrackingVO>> groupByTrackingNumber = orderItemsWithTracking.stream()
                .filter(item -> StrUtil.isNotBlank(item.getTrackingNumber()))
                .collect(Collectors.groupingBy(OrderItemTrackingVO::getTrackingNumber));
        log.info("当前批次订单项按物流单号分组结果: 共{}个不同的物流单号", groupByTrackingNumber.size());
        // 2. 处理每个物流单号对应的订单项
        List<Long> itemIdsToUpdate = new ArrayList<>();
        // 并行处理多个物流单号，提高效率
        List<CompletableFuture<List<Long>>> futures = new ArrayList<>();
        for (Map.Entry<String, List<OrderItemTrackingVO>> entry : groupByTrackingNumber.entrySet()) {
            String trackingNumber = entry.getKey();
            List<OrderItemTrackingVO> items = entry.getValue();
            // 使用CompletableFuture异步处理每个物流单号
            CompletableFuture<List<Long>> future = CompletableFuture.supplyAsync(() -> {
                return processTrackingNumber(trackingNumber, items);
            }).exceptionally(ex -> {
                log.error("处理物流单号[{}]时发生异常", trackingNumber, ex);
                return List.of();
            });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<List<Long>> future : futures) {
            try {
                List<Long> ids = future.get();
                if (!CollectionUtils.isEmpty(ids)) {
                    itemIdsToUpdate.addAll(ids);
                }
            } catch (Exception e) {
                log.error("获取异步任务结果时发生异常", e);
            }
        }
        // 3. 批量更新订单项状态
        if (!itemIdsToUpdate.isEmpty()) {
            // 使用条件更新，只更新指定字段
            boolean updated = sysOrderItemService.lambdaUpdate()
                    .in(SysOrderItem::getItemId, itemIdsToUpdate)
                    .set(SysOrderItem::getStatus, KnetOrderItemStatus.COMPLETED)
                    .set(SysOrderItem::getCompletedTime, new Date())
                    .update();
            if (updated) {
                log.info("成功批量更新{}个订单项状态为已完成", itemIdsToUpdate.size());
                return itemIdsToUpdate.size();
            } else {
                log.warn("批量更新订单项状态失败");
                return 0;
            }
        }
        return 0;
    }

    /**
     * 处理单个物流单号
     *
     * @param trackingNumber 物流单号
     * @param items          关联的订单项列表
     * @return 需要更新状态的订单项ID列表
     */
    private List<Long> processTrackingNumber(String trackingNumber, List<OrderItemTrackingVO> items) {
        // 直接调用KG服务获取物流状态，不使用缓存
        try {
            HttpResult<KnetGroupActivityVO> result = apiKnetGroupService.tracking(trackingNumber);
            if (result.getData() == null) {
                log.warn("获取物流单[{}]状态失败", trackingNumber);
                return List.of();
            }
            KnetGroupActivityVO activityVO = result.getData();
            log.info("物流单[{}]状态: {}", trackingNumber, activityVO.getLabelCenterStatus());
            // 判断是否已送达
            if (KnetGroupActivityVO.isOrderDelivered(activityVO)) {
                // 如果物流已送达，返回所有订单项ID
                List<Long> itemIds = items.stream()
                        .map(OrderItemTrackingVO::getItemId)
                        .toList();
                log.info("物流单[{}]已送达，添加{}个关联订单项到待更新列表", trackingNumber, items.size());
                return itemIds;
            }
        } catch (Exception e) {
            log.error("处理物流单[{}]状态时发生异常", trackingNumber, e);
        }
        return List.of();
    }
}
