package com.knet.order.system.handler.impl;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.req.KnetCreateShipment;
import com.knet.order.model.dto.third.resp.KnetGroupActivityVO;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;
import com.knet.order.openfeign.ApiKnetGroupService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19 17:07
 * @description: kg 服务降级处理机制
 */
@Component
public class ApiKnetGroupServiceFallbackImpl implements ApiKnetGroupService {

    @Override
    public HttpResult<List<KnetShopLabelCenterVo>> getShippingLabel(KnetCreateShipment shipment) {
        return HttpResult.error("KG shipping_label 服务暂时不可用，请稍后重试");
    }

    @Override
    public HttpResult<KnetGroupActivityVO> tracking(String trackingNo) {
        return HttpResult.error("KG tracking 服务暂时不可用，请稍后重试");
    }
}
