package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.req.KnetCreateShipment;
import com.knet.order.model.dto.third.resp.KnetGroupActivityVO;
import com.knet.order.model.dto.third.resp.KnetShopLabelCenterVo;
import com.knet.order.system.handler.impl.ApiKnetGroupServiceFallbackImpl;
import feign.FeignException;
import feign.RetryableException;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.ConnectException;
import java.net.SocketException;
import java.util.List;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2025/2/25 11:02
 * @description: kg 服务提供API
 */
@RefreshScope
@FeignClient(name = "knetGroup-services", url = "${feign.client.config.knetGroupServices.url}", fallback = ApiKnetGroupServiceFallbackImpl.class)
public interface ApiKnetGroupService {
    /**
     * KG 创建物流单
     *
     * @param shipment shipment
     * @return 物流信息
     */
    @Retryable(
            //重试策略，3次，间隔1秒
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
            },
            exclude = {
                    //排除参数异常
                    IllegalArgumentException.class
            }
    )
    @Schema(description = "KG 创建shipping_label")
    @PostMapping("/api/system/create/shipment")
    HttpResult<List<KnetShopLabelCenterVo>> getShippingLabel(@RequestBody KnetCreateShipment shipment);

    /**
     * 物流追踪
     *
     * @param trackingNo 物流单号
     * @return 物流信息
     */
    @Retryable(
            //重试策略，3次，间隔1秒
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
            },
            exclude = {
                    //排除参数异常
                    IllegalArgumentException.class
            }
    )
    @Schema(description = "物流追踪")
    @GetMapping("/api/system/tracking")
    HttpResult<KnetGroupActivityVO> tracking(@RequestParam("trackingNo") String trackingNo);
}
