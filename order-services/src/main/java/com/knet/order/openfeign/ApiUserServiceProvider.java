package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.resp.UserAddressDtoResp;
import com.knet.order.model.dto.third.resp.UserInfoDtoResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:00
 * @description: 用户服务API客户端
 */
@FeignClient(name = "user-services", path = "userServices/api")
public interface ApiUserServiceProvider {

    /**
     * 根据地址ID获取地址信息
     *
     * @param addressId 地址ID
     * @return 地址信息
     */
    @GetMapping("/address/{addressId}")
    HttpResult<UserAddressDtoResp> getAddressById(@PathVariable("addressId") Long addressId);

    /**
     * 清空用户购物车
     *
     * @param userId 用户ID
     * @return void
     */
    @Operation(summary = "清空用户购物车", description = "清空购物车")
    @GetMapping("/shop-cart/remove/{userId}")
    HttpResult<Void> removeFromCart(@Parameter(description = "用户ID", required = true, example = "1")
                                    @PathVariable("userId") Long userId);

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID列表
     * @return 用户信息列表
     */
    @PostMapping("/users/batch")
    HttpResult<List<UserInfoDtoResp>> getUsersByIds(@RequestBody List<Long> userIds);
}
