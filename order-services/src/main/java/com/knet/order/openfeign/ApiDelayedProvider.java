package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.order.system.handler.impl.ApiDelayedServiceFallbackImpl;
import feign.FeignException;
import feign.RetryableException;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.ConnectException;
import java.net.SocketException;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2025/6/26 10:37
 * @description: 延迟消息服务
 */
@FeignClient(name = "delayed-services", path = "delayedServices/api", fallback = ApiDelayedServiceFallbackImpl.class)
public interface ApiDelayedProvider {

    /**
     * 添加延迟消息
     *
     * @param delayedMessage 延迟消息
     * @return 添加结果
     */
    @Schema(description = "添加延迟消息")
    @Retryable(
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
            },
            exclude = {IllegalArgumentException.class}
    )
    @PostMapping("/add")
    HttpResult<String> addDelayedMessage(@RequestBody DelayedMessage delayedMessage);
}
