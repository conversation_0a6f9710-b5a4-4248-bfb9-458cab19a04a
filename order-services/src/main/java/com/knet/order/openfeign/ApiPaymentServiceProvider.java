package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.third.resp.InnerUserBalanceResponse;
import com.knet.order.model.dto.third.resp.OrderPaymentInfoResponse;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025/6/4 16:00
 * @description: 支付服务API客户端
 */
@FeignClient(name = "payment-services", path = "paymentService/api")
public interface ApiPaymentServiceProvider {

    /**
     * 根据订单ID获取支付信息
     *
     * @param orderId 订单ID
     * @return 支付信息
     */
    @GetMapping("/order/{orderId}/payment")
    HttpResult<OrderPaymentInfoResponse> getPaymentInfoByOrderId(@PathVariable("orderId") String orderId);

    /**
     * 查询用户余额
     *
     * @param userId 用户ID
     * @return 用户余额信息
     */
    @Operation(summary = "查询用户余额", description = "根据userId查询用户余额信息")
    @GetMapping("/wallet/balance")
    HttpResult<InnerUserBalanceResponse> getUserBalance(@RequestParam("userId") Long userId);
}
