package com.knet.order.mq.consumer;

import com.knet.common.utils.RedisCacheUtil;
import com.knet.order.service.IPaymentResultService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.ORDER_PAYMENT_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/6/11 15:30
 * @description: 支付结果消息消费者
 */
@Slf4j
@Component
public class PaymentResultConsumer {

    @Resource
    private IPaymentResultService paymentResultService;

    @RabbitListener(
            queues = "payment-result-queue.order-services",
            ackMode = "MANUAL"
    )
    public void handlePaymentResult(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if ("payment.result".equals(routingKey)) {
                // 幂等性检查
                if (!RedisCacheUtil.setIfAbsent(ORDER_PAYMENT_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("order服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                paymentResultService.processPaymentResult(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("支付结果处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("消息拒绝失败: {}", ex.getMessage());
            }
        }
    }
}
