-- rate_limiter.lua
local key = KEYS[1]
local capacity = tonumber(ARGV[1])
local rate = tonumber(ARGV[2])

redis.replicate_commands()

-- 获取当前时间（秒.微秒）
local time_arr = redis.call('TIME')
local now = tonumber(time_arr[1]) + (tonumber(time_arr[2])/1000000)

-- 获取桶状态
local bucket = redis.call('HMGET', key, 'tokens', 'lastRefill', 'capacity', 'rate')
local tokens = tonumber(bucket[1]) or capacity
local lastRefill = tonumber(bucket[2]) or 0
local stored_cap = tonumber(bucket[3]) or capacity
local stored_rate = tonumber(bucket[4]) or rate

-- 首次访问初始化
if lastRefill == 0 then
    tokens = stored_cap
    lastRefill = now
    redis.call('HMSET', key,
        'tokens', tokens-1,
        'lastRefill', lastRefill,
        'capacity', stored_cap,
        'rate', stored_rate
    )
    redis.call('EXPIRE', key, math.ceil(stored_cap/stored_rate)*2)
    return {1, tokens-1}
end

-- 计算新增令牌
local elapsed = math.max(now - lastRefill, 0)
local refill_tokens = math.floor(elapsed * stored_rate)
tokens = math.min(tokens + refill_tokens, stored_cap)

-- 判断是否允许请求
if tokens >= 1 then
    tokens = tokens - 1
    redis.call('HMSET', key, 'tokens', tokens, 'lastRefill', now)
    return {1, tokens}
else
    local deficit = 1 - tokens
    local wait_seconds = math.ceil(deficit / stored_rate)
    return {0, wait_seconds}
end