package com.knet.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.knet.common.constants.SystemConstant.API_RATE_LIMIT;

/**
 * <AUTHOR>
 * @date 2025/2/28 16:53
 * @description: 流量控制注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimiter {
    String key() default API_RATE_LIMIT;// Redis键前缀

    int capacity() default 10;            // 令牌桶容量

    int refillRate() default 1;            // 每秒补充的令牌数

    String message() default "请求过于频繁，请稍后再试"; // 限流提示信息

    String keyExpression() default "";     // 动态Key的SpEL表达式（如#userId）
}
