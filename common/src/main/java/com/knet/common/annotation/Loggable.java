package com.knet.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/3/6 11:31 (2025/7/23 优化)
 * @description: 请求响应日志注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Loggable {

    /**
     * 日志描述
     */
    String value() default "";

    /**
     * 是否记录请求参数
     */
    boolean logArgs() default true;

    /**
     * 是否记录返回结果
     */
    boolean logResult() default true;

    /**
     * 是否记录异常信息
     */
    boolean logException() default true;

    /**
     * 慢请求阈值（毫秒），超过此时间会记录为慢请求
     */
    long slowThreshold() default 2000L;
}
