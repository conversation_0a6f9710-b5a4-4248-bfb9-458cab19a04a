package com.knet.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025/2/13 14:15
 * @description: 校验用户权限
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface PermissionCheck {
    /**
     * 角色名称
     * 默认为空，表示不限制角色
     */
    String role() default "";

    /**
     * 权限集合
     * 默认为空，表示不限制角色
     */
    String[] permission() default {};
}
