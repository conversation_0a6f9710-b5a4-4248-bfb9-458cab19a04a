package com.knet.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.knet.common.constants.SystemConstant.X_API_KEY;

/**
 * <AUTHOR>
 * @date 2025/4/9 14:59
 * @description: 获取自定义请求头ModifyHeader
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ModifyHeader {
    /**
     * 请求头名称，默认为X-API-KEY
     */
    String value() default X_API_KEY;

    /**
     * 是否必填，默认为true
     */
    boolean required() default true;

    /**
     * 处理器类型，用于区分不同模块的处理逻辑
     * 可选值：USER_TOKEN, API_KEY, CUSTOM
     */
    String handlerType() default "USER_TOKEN";
}
