package com.knet.common.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:15
 * @description: 价格变动事件消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "价格变动事件消息")
public class PriceChangeMessage {

    @Schema(description = "事件ID")
    private String eventId;

    @Schema(description = "事件类型")
    private EventType eventType;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "事件时间")
    private LocalDateTime eventTime;

    @Schema(description = "变动的商品列表")
    private List<ProductPriceInfo> products;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "商品价格信息")
    public static class ProductPriceInfo {
        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "商品规格")
        private String spec;

        @Schema(description = "价格（美元）")
        private Long price;

        @Schema(description = "商品状态")
        private String status;
    }

    @Getter
    public enum EventType {
        // 商品创建
        CREATE("商品创建"),
        UPDATE_PRICE("价格更新"),
        OFF_SALE("商品下架");

        private final String description;

        EventType(String description) {
            this.description = description;
        }

    }
}
