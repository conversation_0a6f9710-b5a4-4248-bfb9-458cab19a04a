package com.knet.common.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/3 13:26
 * @description: 支付结果事件
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentResultMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "支付组ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "PAY-123456789012345678")
    private String paymentGroupId;
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;
    @Schema(description = "订单ID")
    private String orderId;
    @Schema(description = "支付状态")
    private String status;
    @Schema(description = "结果时间戳")
    private Long resultTime;
}
