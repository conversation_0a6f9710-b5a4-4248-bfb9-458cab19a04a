package com.knet.common.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:49
 * @description: 消息实体
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DelayedMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "消息唯一ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String payloadJson;

    @Schema(description = "触发时间戳(ms)", requiredMode = Schema.RequiredMode.REQUIRED)
    private long triggerTime;

    @Schema(description = "目标交换机", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetExchange;

    @Schema(description = "目标路由键", requiredMode = Schema.RequiredMode.REQUIRED)
    private String targetRoutingKey;
}
