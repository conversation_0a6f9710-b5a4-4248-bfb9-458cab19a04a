package com.knet.common.dto.message;

import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:04
 * @description: 订单消息体
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OrderMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    @Schema(description = "订单总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal totalAmount;

    @Schema(description = "事件类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String eventType;

    @Schema(description = "事件时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    private long timestamp;

    /**
     * 订单创建消息
     *
     * @param orderId     订单ID
     * @param userId      用户ID
     * @param totalAmount 订单总金额
     * @return 订单创建消息
     */
    public static OrderMessage orderCreate(String orderId, Long userId, BigDecimal totalAmount) {
        return new OrderMessage(orderId, userId, totalAmount, "ORDER_CREATED", DateUtil.current());
    }

    /**
     * 订单取消消息
     *
     * @param orderId     订单ID
     * @param userId      用户ID
     * @param totalAmount 订单总金额
     * @return 订单取消消息
     */
    public static OrderMessage cancelCreate(String orderId, Long userId, BigDecimal totalAmount) {
        return new OrderMessage(orderId, userId, totalAmount, "ORDER_CANCEL", DateUtil.current());
    }
}
