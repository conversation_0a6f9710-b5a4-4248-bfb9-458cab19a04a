package com.knet.common.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/3/7 16:05
 * @description: 第三方调用请求实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ThirdApiRequest extends BaseRequest {
    /**
     * 业务数据转换成json串字符串 按照字母顺序生序，对称加密aes256
     */
    @Schema(description = "bizData 业务数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizData;

    @NotNull(message = "signature is not null")
    @Schema(description = "signature 签名体", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetSignature signature;
}
