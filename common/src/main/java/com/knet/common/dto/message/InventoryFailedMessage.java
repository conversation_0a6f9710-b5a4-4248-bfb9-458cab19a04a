package com.knet.common.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/11 17:10
 * @description: 库存扣减失败消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryFailedMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORD-123456789012345678")
    private String orderId;
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;
    @Schema(description = "失败原因")
    private String failureReason;
    @Schema(description = "失败时间戳")
    private Long failedTime;

    public static InventoryFailedMessage create(String orderId, Long userId, String failureReason) {
        return InventoryFailedMessage.builder()
                .orderId(orderId)
                .userId(userId)
                .failureReason(failureReason)
                .failedTime(System.currentTimeMillis())
                .build();
    }
}
