package com.knet.common.dto.req;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/3/7 16:18
 * @description: knet签名体
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KnetSignature extends BaseResponse {
    @NotBlank(message = "appId is not null")
    @Schema(description = "appId Client unique identifier", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appId;
    @NotBlank(message = "timestamp is not null")
    @Schema(description = "Timestamp Request initiation timestamp (in ms)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String timestamp;
    @NotBlank(message = "nonce is not null")
    @Schema(description = "Random string (at least 10 characters) must not repeat", requiredMode = Schema.RequiredMode.REQUIRED)
    private String nonce;
    /**
     * biz + token 生成签名
     */
    @NotBlank(message = "sign is not null")
    @Schema(description = "sign", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sign;
}
