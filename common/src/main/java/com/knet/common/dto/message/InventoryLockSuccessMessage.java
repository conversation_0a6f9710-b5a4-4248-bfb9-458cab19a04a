package com.knet.common.dto.message;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/19 15:30
 * @description: 库存锁定成功消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryLockSuccessMessage implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 锁定成功的商品信息列表
     */
    private List<LockedProductInfo> lockedProducts;

    /**
     * 锁定成功的商品信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LockedProductInfo {
        /**
         * SKU
         */
        private String sku;
        /**
         * 尺码
         */
        private String size;
        /**
         * 价格（美元字符串）
         */
        private String price;
        /**
         * 锁定的商品详情列表
         */
        private List<ProductDetail> productDetails;
    }

    /**
     * 商品详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductDetail {
        @Schema(description = "商品ID")
        private Long productId;

        @Schema(description = "KG oneId")
        private String oneId;

        @Schema(description = "knet listingId")
        private String knetListingId;

        @Schema(description = "存储仓库")
        private String warehouse;

        @Schema(description = "商品来源")
        private String source;
    }
}
