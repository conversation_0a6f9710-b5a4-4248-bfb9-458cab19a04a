package com.knet.common.handler;

import com.knet.common.annotation.ModifyHeader;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/12/19 
 * @description: 请求头处理器接口
 */
public interface HeaderHandler {
    
    /**
     * 处理请求头
     * @param request HTTP请求
     * @param modifyHeader 注解信息
     * @param headerName 请求头名称
     * @param headerValue 请求头值
     */
    void handleHeader(HttpServletRequest request, ModifyHeader modifyHeader, String headerName, String headerValue);
    
    /**
     * 清理上下文
     */
    void clearContext();
    
    /**
     * 获取处理器类型
     * @return 处理器类型
     */
    String getHandlerType();
}
