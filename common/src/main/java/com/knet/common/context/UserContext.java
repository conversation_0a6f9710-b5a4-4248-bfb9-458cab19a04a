package com.knet.common.context;

/**
 * <AUTHOR>
 * @date 2025/4/22 15:46
 * @description: 通用用户上下文，用于存储用户信息
 */
public class UserContext {
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();

    /**
     * 设置用户上下文
     * @param userInfo 用户信息（通常是token或用户ID）
     */
    public static void setContext(String userInfo) {
        CONTEXT.set(userInfo);
    }

    /**
     * 获取用户上下文
     * @return 用户信息
     */
    public static String getContext() {
        return CONTEXT.get();
    }

    /**
     * 清理用户上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }

    /**
     * 检查是否有用户上下文
     * @return true如果存在用户上下文
     */
    public static boolean hasContext() {
        return CONTEXT.get() != null;
    }
}
