package com.knet.common.context;

/**
 * <AUTHOR>
 * @date 2025/4/9 15:16
 * @description: 通用API Key上下文，用于存储API Key信息
 */
public class ApiKeyContext {
    private static final ThreadLocal<String> CONTEXT = new ThreadLocal<>();

    /**
     * 设置API Key
     * @param apiKey API Key值
     */
    public static void setApiKey(String apiKey) {
        CONTEXT.set(apiKey);
    }

    /**
     * 获取API Key
     * @return API Key值
     */
    public static String getApiKey() {
        return CONTEXT.get();
    }

    /**
     * 清理API Key上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }

    /**
     * 检查是否有API Key上下文
     * @return true如果存在API Key上下文
     */
    public static boolean hasApiKey() {
        return CONTEXT.get() != null;
    }
}
