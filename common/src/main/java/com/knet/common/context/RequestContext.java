package com.knet.common.context;

import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @date 2025/7/23 优化
 * @description: 请求上下文管理，支持请求ID追踪和用户信息存储
 */
public class RequestContext {

    /**
     * MDC键名常量
     */
    public static final String REQUEST_ID_KEY = "requestId";
    public static final String USER_ID_KEY = "userId";
    public static final String SERVICE_NAME_KEY = "serviceName";

    // ThreadLocal存储
    private static final ThreadLocal<String> USER_CONTEXT = new ThreadLocal<>();
    private static final ThreadLocal<Long> START_TIME = new ThreadLocal<>();

    /**
     * 初始化请求上下文
     *
     * @param serviceName 服务名称
     */
    public static void initRequest(String serviceName) {
        String requestId = generateRequestId();
        MDC.put(REQUEST_ID_KEY, requestId);
        MDC.put(SERVICE_NAME_KEY, serviceName);
        START_TIME.set(System.currentTimeMillis());
    }

    /**
     * 设置用户上下文
     *
     * @param userInfo 用户信息（通常是token或用户ID）
     */
    public static void setUserContext(String userInfo) {
        USER_CONTEXT.set(userInfo);
        if (userInfo != null) {
            MDC.put(USER_ID_KEY, userInfo);
        }
    }

    /**
     * 获取用户上下文
     *
     * @return 用户信息
     */
    public static String getUserContext() {
        return USER_CONTEXT.get();
    }

    /**
     * 获取请求ID
     *
     * @return 请求ID
     */
    public static String getRequestId() {
        return MDC.get(REQUEST_ID_KEY);
    }

    /**
     * 获取请求开始时间
     *
     * @return 开始时间戳
     */
    public static Long getStartTime() {
        return START_TIME.get();
    }

    /**
     * 计算请求耗时
     *
     * @return 耗时（毫秒）
     */
    public static long getElapsedTime() {
        Long startTime = START_TIME.get();
        return startTime != null ? System.currentTimeMillis() - startTime : 0;
    }

    /**
     * 清理请求上下文
     */
    public static void clear() {
        USER_CONTEXT.remove();
        START_TIME.remove();
        MDC.clear();
    }

    /**
     * 检查是否有用户上下文
     *
     * @return true如果存在用户上下文
     */
    public static boolean hasUserContext() {
        return USER_CONTEXT.get() != null;
    }

    /**
     * 生成请求ID - 使用时间戳+随机数，更简单可靠
     *
     * @return 请求ID
     */
    private static String generateRequestId() {
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 10000);
        return String.format("%d%04d", timestamp % 100000000, random);
    }
}
