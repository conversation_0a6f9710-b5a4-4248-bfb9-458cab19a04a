package com.knet.common.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description: 死信消息记录表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sys_dead_letter_message")
@Schema(description = "死信消息记录")
public class SysDeadLetterMessage extends BaseEntity {

    @TableField("service_name")
    @Schema(description = "服务名称")
    private String serviceName;

    @TableField("message_id")
    @Schema(description = "消息ID")
    private String messageId;

    @TableField("message_type")
    @Schema(description = "消息类型")
    private String messageType;

    @TableField("original_exchange")
    @Schema(description = "原始交换机")
    private String originalExchange;

    @TableField("original_routing_key")
    @Schema(description = "原始路由键")
    private String originalRoutingKey;

    @TableField("dead_letter_exchange")
    @Schema(description = "死信交换机")
    private String deadLetterExchange;

    @TableField("dead_letter_routing_key")
    @Schema(description = "死信路由键")
    private String deadLetterRoutingKey;

    @TableField("message_body")
    @Schema(description = "消息内容")
    private String messageBody;

    @TableField("death_reason")
    @Schema(description = "死信原因")
    private String deathReason;

    @TableField("death_info")
    @Schema(description = "死信详细信息")
    private String deathInfo;

    @TableField("retry_count")
    @Schema(description = "重试次数")
    private Integer retryCount;

    @TableField("max_retry_count")
    @Schema(description = "最大重试次数")
    private Integer maxRetryCount;

    @TableField("status")
    @Schema(description = "处理状态：PENDING-待处理，PROCESSING-处理中，RESOLVED-已解决，FAILED-处理失败")
    private String status;

    @TableField("error_message")
    @Schema(description = "错误信息")
    private String errorMessage;

    @TableField("resolved_time")
    @Schema(description = "解决时间")
    private LocalDateTime resolvedTime;

    @TableField("resolved_by")
    @Schema(description = "解决人")
    private String resolvedBy;

    @TableField("resolved_method")
    @Schema(description = "解决方式")
    private String resolvedMethod;

    @TableField("resolved_remark")
    @Schema(description = "解决备注")
    private String resolvedRemark;

    @TableField("alert_sent")
    @Schema(description = "是否已发送告警：0-未发送，1-已发送")
    private Integer alertSent;

    @TableField("alert_time")
    @Schema(description = "告警时间")
    private LocalDateTime alertTime;

    @TableField("priority")
    @Schema(description = "优先级：1-低，2-中，3-高，4-紧急")
    private Integer priority;

    @TableField("business_key")
    @Schema(description = "业务关键字（如订单号、用户ID等）")
    private String businessKey;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;
}
