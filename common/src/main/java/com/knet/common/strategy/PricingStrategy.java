package com.knet.common.strategy;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * @description: 价格策略接口
 */
public interface PricingStrategy {
    /**
     * 应用价格策略
     *
     * @param originalPrice 原始价格（美分）
     * @return 策略价格（美分）
     */
    Long applyStrategy(Long originalPrice);

    /**
     * 移除价格策略（反向计算）
     *
     * @param strategyPrice 策略价格（美分）
     * @return 原始价格（美分）
     */
    Long removeStrategy(Long strategyPrice);
}
