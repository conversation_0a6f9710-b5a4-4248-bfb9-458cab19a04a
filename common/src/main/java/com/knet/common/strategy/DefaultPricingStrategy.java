package com.knet.common.strategy;

import com.knet.common.constants.PricingConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.knet.common.constants.GoodsServicesConstants.KG_FEE;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * @description: 默认价格策略实现
 * 规则：价格低于100美元加10美元，高于等于100美元加10%
 */
@Slf4j
@Component
public class DefaultPricingStrategy implements PricingStrategy {

    @Override
    public Long applyStrategy(Long originalPrice) {
        if (originalPrice == null || originalPrice <= 0) {
            return originalPrice;
        }
        long strategyPrice;
        if (originalPrice <= PricingConstants.PRICE_THRESHOLD_CENTS) {
            // 低于100美元，加10美元
            strategyPrice = originalPrice + PricingConstants.FIXED_MARKUP_CENTS;
            log.debug("应用固定加价策略: 原价{}美分 -> 策略价{}美分", originalPrice, strategyPrice);
        } else {
            // 1. 将美分转换为美元并取整（去除小数部分）
            long dollars = originalPrice / 100;
            // 2. 应用10%加价并向上取整 高于等于100美元，加10%（直接乘以1.1）向上取整
            long markedUpDollars = (long) Math.ceil(dollars * KG_FEE);
            // 3. 将美元转换回美分
            strategyPrice = markedUpDollars * 100;
            log.debug("应用百分比加价策略: 原价{}美分 ->  取整{}美元 -> 策略价{}美分", originalPrice, dollars, strategyPrice);
        }
        return strategyPrice;
    }

    @Override
    public Long removeStrategy(Long strategyPrice) {
        if (strategyPrice == null || strategyPrice <= 0) {
            return strategyPrice;
        }
        // 简化的反向计算原始价格
        long originalPrice;
        // 低于10美元（1000美分）抛出异常
        if (strategyPrice < 1000L) {
            throw new IllegalArgumentException("策略价格不能低于10美元，当前价格: " + strategyPrice + "美分");
        }
        if (strategyPrice <= PricingConstants.PRICE_THRESHOLD_CENTS + PricingConstants.FIXED_MARKUP_CENTS) {
            // 小于等于110美元（11000美分），按固定减价策略反推：减10美元
            originalPrice = strategyPrice - PricingConstants.FIXED_MARKUP_CENTS;
            log.debug("移除固定加价策略: 策略价{}美分 -> 原价{}美分", strategyPrice, originalPrice);
        } else {
            // 1. 将美分转换为美元并取整（去除小数部分）
            long dollars = strategyPrice / 100;
            // 2. 按百分比减价策略反推：除以1.1 向下取整
            long markedUpDollars = (long) Math.floor(dollars / KG_FEE);
            //3. 将美元转换回美分
            originalPrice = markedUpDollars * 100;
            log.debug("移除百分比加价策略: 策略价{}美分 -> 取整{}美元 -> 原价{}美分", strategyPrice, dollars, originalPrice);
        }
        return originalPrice;
    }
}
