package com.knet.common.service;

import com.knet.common.constants.PricingConstants;
import com.knet.common.strategy.PricingStrategy;
import com.knet.common.utils.RedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * @description: 价格策略服务
 */
@Slf4j
@Service
public class PricingStrategyService {

    @Resource
    private PricingStrategy pricingStrategy;

    /**
     * 应用价格策略
     *
     * @param originalPrice 原始价格（美分）
     * @return 策略价格（美分）
     */
    public Long applyPricingStrategy(Long originalPrice) {
        if (originalPrice == null) {
            return null;
        }
        // 检查缓存
        String cacheKey = buildStrategyCacheKey(originalPrice);
        Object cachedPriceObj = RedisCacheUtil.get(cacheKey);
        if (cachedPriceObj instanceof Long cachedPrice) {
            log.debug("从缓存获取策略价格: {}", cachedPrice);
            return cachedPrice;
        }
        // 应用策略
        Long strategyPrice = pricingStrategy.applyStrategy(originalPrice);
        // 缓存结果 缓存5分钟
        if (strategyPrice != null) {
            RedisCacheUtil.set(cacheKey, strategyPrice, 300L);
        }
        return strategyPrice;
    }

    /**
     * 移除价格策略，返回原始价格
     *
     * @param strategyPrice 策略价格（美分）
     * @return 原始价格（美分）
     */
    public Long removePricingStrategy(Long strategyPrice) {
        if (strategyPrice == null) {
            return null;
        }
        // 检查缓存
        String cacheKey = buildOriginalCacheKey(strategyPrice);
        Object cachedPriceObj = RedisCacheUtil.get(cacheKey);
        if (cachedPriceObj instanceof Long cachedPrice) {
            log.debug("从缓存获取原始价格: {}", cachedPrice);
            return cachedPrice;
        }
        // 移除策略
        Long originalPrice = pricingStrategy.removeStrategy(strategyPrice);
        //缓存结果  缓存5分钟
        if (originalPrice != null) {
            RedisCacheUtil.set(cacheKey, originalPrice, 300L);
        }
        return originalPrice;
    }

    /**
     * 批量应用价格策略
     *
     * @param originalPrices 原始价格列表（美分）
     * @return 策略价格列表（美分）
     */
    public List<Long> batchApplyPricingStrategy(List<Long> originalPrices) {
        if (originalPrices == null || originalPrices.isEmpty()) {
            return originalPrices;
        }
        return originalPrices.stream()
                .map(this::applyPricingStrategy)
                .toList();
    }

    /**
     * 批量移除价格策略
     *
     * @param strategyPrices 策略价格列表（美分）
     * @return 原始价格列表（美分）
     */
    public List<Long> batchRemovePricingStrategy(List<Long> strategyPrices) {
        if (strategyPrices == null || strategyPrices.isEmpty()) {
            return strategyPrices;
        }
        return strategyPrices.stream()
                .map(this::removePricingStrategy)
                .toList();
    }

    /**
     * 构建策略价格缓存key
     */
    private String buildStrategyCacheKey(Long originalPrice) {
        return PricingConstants.STRATEGY_PRICE_CACHE_PREFIX + originalPrice;
    }

    /**
     * 构建原始价格缓存key
     */
    private String buildOriginalCacheKey(Long strategyPrice) {
        return PricingConstants.ORIGINAL_PRICE_CACHE_PREFIX + strategyPrice;
    }
}
