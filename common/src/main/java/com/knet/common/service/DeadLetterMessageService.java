package com.knet.common.service;

import com.knet.common.entity.SysDeadLetterMessage;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description: 死信消息管理服务接口
 */
public interface DeadLetterMessageService {

    /**
     * 记录死信消息
     *
     * @param serviceName 服务名称
     * @param messageId 消息ID
     * @param messageType 消息类型
     * @param originalExchange 原始交换机
     * @param originalRoutingKey 原始路由键
     * @param deadLetterExchange 死信交换机
     * @param deadLetterRoutingKey 死信路由键
     * @param messageBody 消息内容
     * @param deathReason 死信原因
     * @param deathInfo 死信详细信息
     * @param businessKey 业务关键字
     */
    void recordDeadLetterMessage(String serviceName, String messageId, String messageType,
                               String originalExchange, String originalRoutingKey,
                               String deadLetterExchange, String deadLetterRoutingKey,
                               String messageBody, String deathReason, String deathInfo,
                               String businessKey);

    /**
     * 记录死信消息（简化版本）
     *
     * @param serviceName 服务名称
     * @param messageType 消息类型
     * @param messageId 消息ID
     * @param originalRoutingKey 原始路由键
     * @param messageBody 消息内容
     * @param deathInfo 死信详细信息
     */
    void recordDeadLetterMessage(String serviceName, String messageType, String messageId,
                               String originalRoutingKey, String messageBody, Object deathInfo);

    /**
     * 更新死信消息状态
     *
     * @param messageId 消息ID
     * @param status 状态
     * @param errorMessage 错误信息
     */
    void updateDeadLetterMessageStatus(String messageId, String status, String errorMessage);

    /**
     * 标记死信消息为已解决
     *
     * @param messageId 消息ID
     * @param resolvedBy 解决人
     * @param resolvedMethod 解决方式
     * @param resolvedRemark 解决备注
     */
    void markDeadLetterMessageResolved(String messageId, String resolvedBy, 
                                     String resolvedMethod, String resolvedRemark);

    /**
     * 发送死信告警
     *
     * @param deadLetterMessage 死信消息
     */
    void sendDeadLetterAlert(SysDeadLetterMessage deadLetterMessage);

    /**
     * 获取死信消息统计信息
     *
     * @param serviceName 服务名称
     * @return 统计信息
     */
    DeadLetterStatistics getDeadLetterStatistics(String serviceName);

    /**
     * 死信消息统计信息
     */
    class DeadLetterStatistics {
        private long totalCount;
        private long pendingCount;
        private long resolvedCount;
        private long failedCount;
        private long todayCount;
        private long highPriorityCount;

        // getters and setters
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }
        
        public long getPendingCount() { return pendingCount; }
        public void setPendingCount(long pendingCount) { this.pendingCount = pendingCount; }
        
        public long getResolvedCount() { return resolvedCount; }
        public void setResolvedCount(long resolvedCount) { this.resolvedCount = resolvedCount; }
        
        public long getFailedCount() { return failedCount; }
        public void setFailedCount(long failedCount) { this.failedCount = failedCount; }
        
        public long getTodayCount() { return todayCount; }
        public void setTodayCount(long todayCount) { this.todayCount = todayCount; }
        
        public long getHighPriorityCount() { return highPriorityCount; }
        public void setHighPriorityCount(long highPriorityCount) { this.highPriorityCount = highPriorityCount; }
    }
}
