package com.knet.common.filter;

import com.knet.common.context.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/7/23
 * @description: 请求上下文过滤器，自动为每个请求设置上下文信息
 */
@Slf4j
@Order(1)
public class RequestContextFilter implements Filter {

    @Value("${spring.application.name:unknown-service}")
    private String serviceName;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("RequestContextFilter initialized for service: {}", serviceName);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        try {
            // 从请求头中获取已有的请求ID，如果没有则生成新的
            String requestId = httpRequest.getHeader("X-Request-ID");
            if (requestId == null || requestId.trim().isEmpty()) {
                // 初始化请求上下文，会自动生成新的请求ID
                RequestContext.initRequest(serviceName);
                requestId = RequestContext.getRequestId();
            } else {
                // 使用现有的请求ID
                RequestContext.initRequest(serviceName);
                // 这里需要重新设置请求ID
                org.slf4j.MDC.put(RequestContext.REQUEST_ID_KEY, requestId);
            }
            // 从请求头中获取用户信息
            String userToken = httpRequest.getHeader("Authorization");
            if (userToken == null) {
                userToken = httpRequest.getHeader("token");
            }
            if (userToken != null && !userToken.isEmpty()) {
                RequestContext.setUserContext(userToken);
            }
            // 将请求ID添加到响应头中，便于客户端追踪
            httpResponse.setHeader("X-Request-ID", requestId);
            // 继续执行请求
            chain.doFilter(request, response);
        } catch (Exception e) {
            log.error("Error in RequestContextFilter: {}", e.getMessage(), e);
            throw e;
        } finally {
            // 清理上下文
            RequestContext.clear();
        }
    }

    @Override
    public void destroy() {
        log.info("RequestContextFilter destroyed for service: {}", serviceName);
    }
}
