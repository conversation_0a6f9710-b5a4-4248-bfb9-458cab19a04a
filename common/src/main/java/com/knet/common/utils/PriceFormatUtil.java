package com.knet.common.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/5/22 10:30
 * @description: 价格格式化工具类
 */
public class PriceFormatUtil {

    /**
     * 将分转换为元，并格式化为两位小数
     *
     * @param priceInCents 分为单位的价格
     * @return 格式化后的价格字符串（元为单位，两位小数）
     */
    public static String formatCentsToYuan(long priceInCents) {
        BigDecimal priceInYuan = new BigDecimal(priceInCents).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        return formatPrice(priceInYuan);
    }

    /**
     * 将分转换为元，并格式化为两位小数
     *
     * @param priceInCents 分为单位的价格
     * @return 格式化后的价格字符串（元为单位，两位小数）
     */
    public static String formatCentsToYuan(String priceInCents) {
        try {
            if (StrUtil.isEmpty(priceInCents)) {
                return "0.00";
            }
            long cents = Long.parseLong(priceInCents);
            return formatCentsToYuan(cents);
        } catch (NumberFormatException e) {
            // 如果解析失败，返回原字符串
            return priceInCents;
        }
    }

    /**
     * 将元转换为美分
     *
     * @param priceInYuan 元为单位的价格字符串
     * @return 美分为单位的价格，如果解析失败则返回0
     */
    public static long formatYuanToCents(String priceInYuan) {
        try {
            BigDecimal yuan = new BigDecimal(priceInYuan);
            // 元转换为分需要乘以100，然后四舍五入为整数
            return yuan.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();
        } catch (NumberFormatException e) {
            // 如果解析失败，返回0
            return 0;
        }
    }


    public static Long formatYuanToCents(BigDecimal price) {
        if (BeanUtil.isEmpty(price)) {
            return 0L;
        }
        return price.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();
    }

    /**
     * 格式化价格为两位小数
     *
     * @param price 价格（元为单位）
     * @return 格式化后的价格字符串（两位小数）
     */
    public static String formatPrice(BigDecimal price) {
        return String.format("%.2f", price.setScale(2, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 格式化价格为两位小数
     *
     * @param price 价格（元为单位）
     * @return 格式化后的价格字符串（两位小数）
     */
    public static String formatPrice(double price) {
        return String.format("%.2f", BigDecimal.valueOf(price).setScale(2, RoundingMode.HALF_UP).doubleValue());
    }

    /**
     * 格式化价格为两位小数
     *
     * @param price 价格字符串（元为单位）
     * @return 格式化后的价格字符串（两位小数）
     */
    public static String formatPrice(String price) {
        try {
            if (StrUtil.isEmpty(price)) {
                return "0.00";
            }
            double priceValue = Double.parseDouble(price);
            return formatPrice(priceValue);
        } catch (NumberFormatException e) {
            // 如果解析失败，返回原字符串
            return price;
        }
    }
}
