package com.knet.common.utils;

import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.knet.common.constants.UserServicesConstants.KNET_BRANDS_EXPIRED_TIME;

/**
 * <AUTHOR>
 * @date 2025/2/13 14:35
 * @description: spring redis 工具类
 */
@SuppressWarnings(value = {"unchecked", "rawtypes"})
@Component
public class RedisCacheUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    private static RedisTemplate<String, Object> getRedisTemplate() {
        return applicationContext.getBean("redisTemplate", RedisTemplate.class);
    }

    private static DefaultRedisScript<List> getRateLimitScript() {
        try {
            return applicationContext.getBean("rateLimitScript", DefaultRedisScript.class);
        } catch (Exception e) {
            return null;
        }
    }

    // =============================common============================

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     */
    public static boolean expire(String key, long time) {
        try {
            if (time > 0) {
                getRedisTemplate().expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public static long getExpire(String key) {
        return getRedisTemplate().getExpire(key, TimeUnit.SECONDS);
    }


    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public static boolean hasKey(String key) {
        try {
            return getRedisTemplate().hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public static void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                getRedisTemplate().delete(key[0]);
            } else {
                getRedisTemplate().delete(Arrays.asList(key));
            }
        }
    }


    // ============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        return key == null ? null : getRedisTemplate().opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */

    public static boolean set(String key, Object value) {
        try {
            getRedisTemplate().opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */

    public static boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                getRedisTemplate().opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 尝试设置一个键的值，仅当该键不存在时设置。
     *
     * @param key   键名
     * @param value 值
     * @param time  过期时间
     * @return 如果键原来不存在并且值被设置了返回 true，否则返回 false
     */
    public static boolean setIfAbsent(String key, Object value, long time) {
        return Boolean.TRUE.equals(getRedisTemplate().opsForValue().setIfAbsent(key, value, time, TimeUnit.SECONDS));
    }


    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     */
    public static long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return getRedisTemplate().opsForValue().increment(key, delta);
    }


    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     */
    public static long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return getRedisTemplate().opsForValue().increment(key, -delta);
    }


    // ================================Map=================================

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     */
    public static Object hget(String key, String item) {
        return getRedisTemplate().opsForHash().get(key, item);
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public static Map<Object, Object> hmget(String key) {
        return getRedisTemplate().opsForHash().entries(key);
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     */
    public static boolean hmset(String key, Map<String, Object> map) {
        try {
            getRedisTemplate().opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public static boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            getRedisTemplate().opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public static boolean hset(String key, String item, Object value) {
        try {
            getRedisTemplate().opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   锁
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public static boolean hset(String key, String item, Object value, long time) {
        try {
            getRedisTemplate().opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public static void hdel(String key, Object... item) {
        getRedisTemplate().opsForHash().delete(key, item);
    }


    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public static boolean hHasKey(String key, String item) {
        return getRedisTemplate().opsForHash().hasKey(key, item);
    }


    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     */
    public static double hincr(String key, String item, double by) {
        return getRedisTemplate().opsForHash().increment(key, item, by);
    }


    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     */
    public static double hdecr(String key, String item, double by) {
        return getRedisTemplate().opsForHash().increment(key, item, -by);
    }


    // ============================set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     */
    public static Set<Object> sGet(String key) {
        try {
            return getRedisTemplate().opsForSet().members(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public static boolean sHasKey(String key, Object value) {
        try {
            return getRedisTemplate().opsForSet().isMember(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public static long sSet(String key, Object... values) {
        try {
            return getRedisTemplate().opsForSet().add(key, values);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public static long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = getRedisTemplate().opsForSet().add(key, values);
            if (time > 0)
                expire(key, time);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 获取set缓存的长度
     *
     * @param key 键
     */
    public static long sGetSetSize(String key) {
        try {
            return getRedisTemplate().opsForSet().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */

    public static long setRemove(String key, Object... values) {
        try {
            Long count = getRedisTemplate().opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    // ===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     */
    public static List<Object> lGet(String key, long start, long end) {
        try {
            return getRedisTemplate().opsForList().range(key, start, end);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 获取list缓存的长度
     *
     * @param key 键
     */
    public static long lGetListSize(String key) {
        try {
            return getRedisTemplate().opsForList().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     */
    public static Object lGetIndex(String key, long index) {
        try {
            return getRedisTemplate().opsForList().index(key, index);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     */
    public static boolean lSet(String key, Object value) {
        try {
            getRedisTemplate().opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     */
    public static boolean lSet(String key, Object value, long time) {
        try {
            getRedisTemplate().opsForList().rightPush(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }


    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public static boolean lSet(String key, List<Object> value) {
        try {
            getRedisTemplate().opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }


    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public static boolean lSet(String key, List<Object> value, long time) {
        try {
            getRedisTemplate().opsForList().rightPushAll(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */

    public static boolean lUpdateIndex(String key, long index, Object value) {
        try {
            getRedisTemplate().opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */

    public static long lRemove(String key, long count, Object value) {
        try {
            Long remove = getRedisTemplate().opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }

    }

    /**
     * Redis Lua脚本实现令牌桶算法（原子操作）
     *
     * @param key        限流key
     * @param capacity   令牌桶容量
     * @param refillRate 令牌桶填充速率
     * @return 是否允许访问
     */
    public static boolean isAllowed(String key, int capacity, int refillRate) {
        List<String> keys = Collections.singletonList("{rate_limit}:" + key);
        DefaultRedisScript<List> script = getRateLimitScript();
        if (script == null) {
            return true; // 如果脚本不存在，允许访问
        }
        List<Long> results = getRedisTemplate().execute(
                script,
                keys,
                capacity,
                refillRate
        );
        return results != null && results.size() > 0 && results.get(0).equals(1L);
    }

    /**
     * 分布式锁 通过setnx实现 加锁
     *
     * @param lockKey   锁的key
     * @param lockValue 锁的value
     * @param expire    过期时间（秒）
     * @return 是否加锁成功
     */
    public static Boolean distributedLock(String lockKey, String lockValue, long expire) {
        return getRedisTemplate().opsForValue()
                .setIfAbsent(lockKey, lockValue, expire, TimeUnit.SECONDS);
    }

    /**
     * 分布式锁 通过setnx实现 解锁
     *
     * @param lockKey   锁的key
     * @param lockValue 锁的value
     */
    public static void distributedUnlock(String lockKey, String lockValue) {
        // 释放锁（Lua脚本保证原子性）
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        getRedisTemplate().execute(
                new DefaultRedisScript<>(script, Long.class),
                Collections.singletonList(lockKey),
                lockValue
        );
    }

    /**
     * 缓存品牌
     *
     * @return 缓存品牌
     */
    public static List<String> cachedBrands() {
        List<Object> cachedBrandsObjects = getRedisTemplate().opsForList().range("knet_brands", 0, -1);
        List<String> cachedBrands = new ArrayList<>(32);
        if (CollUtil.isNotEmpty(cachedBrandsObjects)) {
            for (Object obj : cachedBrandsObjects) {
                cachedBrands.add(obj.toString());
            }
        }
        return cachedBrands;
    }

    /**
     * 设置品牌缓存
     *
     * @param brands 品牌列表
     */
    public static void setBrands(List<String> brands) {
        if (CollUtil.isNotEmpty(brands)) {
            // 更新Redis缓存
            getRedisTemplate().opsForList().rightPushAll("knet_brands", brands.toArray());
            getRedisTemplate().expire("knet_brands", KNET_BRANDS_EXPIRED_TIME, TimeUnit.SECONDS);
        }
    }

    /**
     * 根据模式删除缓存
     *
     * @param pattern 键模式
     */
    public static void deleteByPattern(String pattern) {
        Set<String> keys = getRedisTemplate().keys(pattern);
        if (keys != null && !keys.isEmpty()) {
            getRedisTemplate().delete(keys);
        }
    }

    /**
     * 添加元素到 Z set
     */
    public static void addZSet(String key, String value, double score) {
        getRedisTemplate().opsForZSet().add(key, value, score);
    }

    /**
     * 移除元素 Z set
     */
    public static void removeZSet(String key, Object value) {
        getRedisTemplate().opsForZSet().remove(key, value);
    }

    /**
     * 获取Z set 集合
     */
    public static Set<Object> getZSet(String key, Long maxScore) {
        return getRedisTemplate().opsForZSet().rangeByScore(key, 0, maxScore);
    }

    /**
     * 删除hash表中的值
     *
     * @param skuCacheKey 键 不能为null
     */
    public static void hmdel(String skuCacheKey) {
        getRedisTemplate().delete(skuCacheKey);
    }

    /**
     * 分批扫描Hash中的所有键值对，避免一次性加载大量数据导致OOM
     *
     * @param key       Hash键
     * @param batchSize 每批处理的数量
     * @param processor 处理每批数据的回调函数
     */
    public static void hscanWithBatch(String key, int batchSize, Consumer<Map<Object, Object>> processor) {
        try {
            ScanOptions options = ScanOptions.scanOptions()
                    .count(batchSize)
                    .build();
            Cursor<Map.Entry<Object, Object>> cursor = getRedisTemplate().opsForHash().scan(key, options);
            Map<Object, Object> batch = new HashMap<>();
            int count = 0;
            while (cursor.hasNext()) {
                Map.Entry<Object, Object> entry = cursor.next();
                batch.put(entry.getKey(), entry.getValue());
                count++;
                // 当达到批次大小时，处理当前批次
                if (count >= batchSize) {
                    processor.accept(new HashMap<>(batch));
                    batch.clear();
                    count = 0;
                }
            }
            // 处理最后一批数据（如果有剩余）
            if (!batch.isEmpty()) {
                processor.accept(batch);
            }
            cursor.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("分批扫描Hash数据失败", e);
        }
    }

    /**
     * 分批扫描Hash中匹配指定模式的键值对
     *
     * @param key       Hash键
     * @param pattern   匹配模式（可以使用通配符*和?）
     * @param batchSize 每批处理的数量
     * @param processor 处理每批数据的回调函数
     */
    public static void hscanWithPattern(String key, String pattern, int batchSize, Consumer<Map<Object, Object>> processor) {
        try {
            ScanOptions options = ScanOptions.scanOptions()
                    .match(pattern)
                    .count(batchSize)
                    .build();
            Cursor<Map.Entry<Object, Object>> cursor = getRedisTemplate().opsForHash().scan(key, options);
            Map<Object, Object> batch = new HashMap<>();
            int count = 0;
            while (cursor.hasNext()) {
                Map.Entry<Object, Object> entry = cursor.next();
                batch.put(entry.getKey(), entry.getValue());
                count++;
                // 当达到批次大小时，处理当前批次
                if (count >= batchSize) {
                    processor.accept(new HashMap<>(batch));
                    batch.clear();
                    count = 0;
                }
            }
            // 处理最后一批数据（如果有剩余）
            if (!batch.isEmpty()) {
                processor.accept(batch);
            }
            cursor.close();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("分批扫描Hash数据失败", e);
        }
    }
}
