package com.knet.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2025/5/20 16:24
 * @description: 数字工具类
 */
public class NumberUtils {
    /**
     * 格式化金额(4舍5入 保留两位小数)
     *
     * @param amount 金额
     * @return 格式化后的金额
     */
    public static String formatDecimal(BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.setScale(2, RoundingMode.HALF_UP).toString();
    }
}
