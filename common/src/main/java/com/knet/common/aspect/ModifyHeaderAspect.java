package com.knet.common.aspect;

import com.knet.common.annotation.ModifyHeader;
import com.knet.common.exception.ServiceException;
import com.knet.common.handler.HeaderHandler;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/9 15:01
 * @description: 通用ModifyHeader注解切面
 */
@RefreshScope
@Slf4j
@Component
@Aspect
public class ModifyHeaderAspect {

    private final Map<String, HeaderHandler> handlerMap;
    /**
     * 使用ThreadLocal存储当前请求的处理器类型，确保清理时能找到正确的处理器
     */
    private static final ThreadLocal<String> CURRENT_HANDLER_TYPE = new ThreadLocal<>();

    @Autowired
    public ModifyHeaderAspect(List<HeaderHandler> handlers) {
        this.handlerMap = handlers.stream()
                .collect(Collectors.toMap(HeaderHandler::getHandlerType, Function.identity()));
    }

    @Pointcut("@annotation(modifyHeader)")
    public void permissionPointCut(ModifyHeader modifyHeader) {
    }

    /**
     * 处理请求头
     *
     * @param joinPoint    joinPoint
     * @param modifyHeader modifyHeader
     */
    @Before(value = "permissionPointCut(modifyHeader)", argNames = "joinPoint,modifyHeader")
    public void checkApiKey(JoinPoint joinPoint, ModifyHeader modifyHeader) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.error("Request attributes is null, cannot process ModifyHeader annotation");
                throw new ServiceException("请求上下文不存在");
            }
            HttpServletRequest request = attributes.getRequest();
            String headerName = modifyHeader.value();
            String headerValue = request.getHeader(headerName);
            // 获取对应的处理器
            String handlerType = modifyHeader.handlerType();
            HeaderHandler handler = handlerMap.get(handlerType);
            if (handler == null) {
                log.warn("No handler found for type: {}, using default USER_TOKEN handler", handlerType);
                handlerType = "USER_TOKEN";
                handler = handlerMap.get(handlerType);
            }
            if (handler != null) {
                // 存储当前处理器类型到ThreadLocal
                CURRENT_HANDLER_TYPE.set(handlerType);
                handler.handleHeader(request, modifyHeader, headerName, headerValue);
            } else {
                log.error("No suitable handler found for processing header");
                throw new ServiceException("无法处理请求头");
            }
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("Failed to process header", e);
                throw new ServiceException("处理请求头时发生错误");
            }
            throw e;
        }
    }

    /**
     * 无论方法是否正常结束，都确保清理ThreadLocal
     */
    @After(value = "permissionPointCut(modifyHeader)", argNames = "modifyHeader")
    public void clearContext(ModifyHeader modifyHeader) {
        try {
            String handlerType = null;
            // 优先从ThreadLocal获取处理器类型
            if (CURRENT_HANDLER_TYPE.get() != null) {
                handlerType = CURRENT_HANDLER_TYPE.get();
            } else if (modifyHeader != null) {
                handlerType = modifyHeader.handlerType();
            }
            if (handlerType != null) {
                HeaderHandler handler = handlerMap.get(handlerType);
                if (handler != null) {
                    handler.clearContext();
                    log.debug("Cleared context for handler type: {}", handlerType);
                } else {
                    log.warn("No handler found for type: {}, clearing all contexts", handlerType);
                    clearAllContexts();
                }
            } else {
                log.warn("No handler type found, clearing all contexts");
                clearAllContexts();
            }
        } catch (Exception e) {
            log.error("Error clearing context", e);
            // 发生异常时，确保清理所有上下文
            clearAllContexts();
        } finally {
            // 清理ThreadLocal
            CURRENT_HANDLER_TYPE.remove();
        }
    }

    /**
     * 清理所有上下文
     */
    private void clearAllContexts() {
        try {
            handlerMap.values().forEach(HeaderHandler::clearContext);
            log.debug("Cleared all contexts");
        } catch (Exception ex) {
            log.error("Error clearing all contexts", ex);
        }
    }
}
