package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:25
 * @description: 消息通道枚举
 */
@Getter
@AllArgsConstructor
public enum MessageChannel {

    /**
     * 消息通道
     */
    SMS("SMS", "短信"),
    EMAIL("EMAIL", "邮件"),
    APP_PUSH("APP_PUSH", "应用推送"),
    WEB_SOCKET("WEB_SOCKET", "网页推送"),
    WECHAT("WECHAT", "微信通知");

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

    public static MessageChannel of(String code) {
        if (code == null) {
            return null;
        }
        for (MessageChannel channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }
}