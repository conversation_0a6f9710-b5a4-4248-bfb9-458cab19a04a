package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:15
 * @description: 语言枚举
 */
@Getter
@AllArgsConstructor
public enum LanguageEnum {
    /**
     * 中文
     */
    ZH_CN(1, "ZH_CN"),
    /**
     * 英文
     */
    EN_US(2, "EN_US");

    /**
     * 语言代码
     */
    private Integer code;

    /**
     * 语言名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
