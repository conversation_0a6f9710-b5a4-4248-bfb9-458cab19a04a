package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/20 18:05
 * @description: 操作结果枚举
 */
@Getter
@AllArgsConstructor
public enum OperationResult {
    /**
     * 成功
     */
    SUCCESS(1, "SUCCESS", "成功"),
    /**
     * 失败
     */
    FAILURE(2, "FAILURE", "失败"),
    /**
     * 处理中
     */
    PROCESSING(3, "PROCESSING", "处理中"),
    /**
     * 已取消
     */
    CANCELLED(4, "CANCELLED", "已取消");

    /**
     * 结果代码
     */
    private final Integer code;

    /**
     * 结果名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    /**
     * 结果描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static OperationResult fromCode(int code) {
        for (OperationResult result : OperationResult.values()) {
            if (result.getCode() == code) {
                return result;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }

    public static OperationResult fromName(String name) {
        if (name == null) {
            return null;
        }
        for (OperationResult result : OperationResult.values()) {
            if (result.getName().equals(name)) {
                return result;
            }
        }
        throw new IllegalArgumentException("Unknown enum name " + name);
    }
}
