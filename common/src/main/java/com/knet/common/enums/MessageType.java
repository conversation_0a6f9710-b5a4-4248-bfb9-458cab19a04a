package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:25
 * @description: 消息类型枚举
 */
@Getter
@AllArgsConstructor
public enum MessageType {

    /**
     * 消息类型
     */
    TEXT(1, "文本消息"),
    IMAGE(2, "图片消息"),
    VOICE(3, "语音消息"),
    SYSTEM_NOTICE(4, "系统通知"),
    ORDER_NOTICE(5, "订单通知"),
    PAYMENT_NOTICE(6, "支付通知");

    @EnumValue
    private final Integer code;

    private final String desc;

    public static MessageType of(Integer code) {
        if (code == null) {
            return null;
        }
        for (MessageType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}