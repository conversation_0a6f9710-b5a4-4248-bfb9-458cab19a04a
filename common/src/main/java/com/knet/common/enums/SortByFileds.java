package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/17 10:16
 * @description: 排序枚举
 */
@Getter
@AllArgsConstructor
public enum SortByFileds {
    /**
     * 销量降序
     */
    UNIT_DESC(1, "UNIT_DESC"),
    /**
     * 销量升序
     */
    UNIT_ASC(2, "UNIT_ASC"),
    /**
     * 价格降序
     */
    PRICE_DESC(3, "PRICE_DESC"),
    /**
     * 价格升序
     */
    PRICE_ASC(4, "PRICE_ASC"),
    /**
     * SKU降序
     */
    SKU_DESC(5, "SKU_DESC"),
    /**
     * SKU升序
     */
    SKU_ASC(6, "SKU_ASC");

    /**
     * 排序代码
     */
    private final Integer code;

    /**
     * 排序名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static SortByFileds fromCode(int code) {
        for (SortByFileds status : SortByFileds.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
