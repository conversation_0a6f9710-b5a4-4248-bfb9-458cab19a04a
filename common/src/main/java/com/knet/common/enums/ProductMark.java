package com.knet.common.enums;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/4/30 11:51
 * @description: 商品标识
 */
@Getter
@AllArgsConstructor
public enum ProductMark {
    /**
     * 新品
     */
    NEW(1, "NEW"),
    /**
     * 热销
     */
    HOT_SALE(2, "HOT_SALE"),
    /**
     * 普通(默认)
     */
    COMMON(3, "COMMON"),
    /**
     * 全部
     */
    ALL(4, "ALL"),
    ;

    /**
     * 标识代码
     */
    private final Integer code;

    /**
     * 标识名称，用于数据库存储和JSON序列化
     */
    @EnumValue
    @JSONField
    @JsonValue
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据名称获取枚举值（支持大小写不敏感）
     */
    @JsonCreator
    public static ProductMark fromName(String name) {
        if (name == null) {
            return null;
        }
        for (ProductMark mark : ProductMark.values()) {
            if (mark.getName().equalsIgnoreCase(name)) {
                return mark;
            }
        }
        throw new IllegalArgumentException("Unknown ProductMark name: " + name);
    }

    /**
     * 根据代码获取枚举值
     */
    public static ProductMark fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductMark mark : ProductMark.values()) {
            if (mark.getCode().equals(code)) {
                return mark;
            }
        }
        throw new IllegalArgumentException("Unknown ProductMark code: " + code);
    }

    @Override
    public String toString() {
        return this.name;
    }
}
