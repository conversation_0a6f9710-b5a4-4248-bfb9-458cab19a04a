package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:01
 * @description: 订单支付状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetPaymentGroupStatus {

    /**
     * 未完成
     */
    UNFINISHED(0, "UNFINISHED", "Unfinished"),
    /**
     * 已完成
     */
    COMPLETED(1, "COMPLETED", "Completed"),
    /**
     * 已关闭
     */
    CLOSED(2, "CLOSED", "Closed");

    /**
     * 状态代码
     */
    @EnumValue
    private final Integer code;

    /**
     * 状态名称，用于数据库存储
     */
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public static KnetPaymentGroupStatus fromCode(int code) {
        for (KnetPaymentGroupStatus status : KnetPaymentGroupStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
