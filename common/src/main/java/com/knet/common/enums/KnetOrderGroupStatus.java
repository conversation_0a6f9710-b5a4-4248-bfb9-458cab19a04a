package com.knet.common.enums;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/12 10:33
 * @description: knet订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum KnetOrderGroupStatus {
    /**
     * 基础状态（用户侧）对用户侧隐藏，用户理论上只能看到已付款的订单
     */
    PENDING_PAYMENT(0, "PENDING_PAYMENT", "待支付"),
    PAY_FAILED(1, "PAY_FAILED", "支付失败"),
    PAID(2, "PAID", "已支付"),
    /**
     * 物流状态（用户侧）
     */
    PENDING_SHIPMENT(3, "PENDING_SHIPMENT", "待发货"),
    /**
     * 订单推送至kg 订单处于在途中的状态
     */
    IN_TRANSIT(4, "IN_TRANSIT", "在途中"),
    COMPLETED(5, "COMPLETED", "已完成"),
    CANCELLED(6, "CANCELLED", "已取消"),
    /**
     * 多个状态，复合态
     */
    MULTIPLE_STATES(7, "MULTIPLE_STATES", "多个状态"),
    /**
     * 售后状态 对用户隐藏
     */
    REFUNDING(8, "REFUNDING", "退款中"),
    RETURNING(9, "RETURNING", "退货中"),
    /**
     * 特殊状态，订单被KG拉走，不能取消
     */
    FROZEN(10, "FROZEN", "已冻结");

    /**
     * 状态代码
     */
    @EnumValue
    private final Integer code;

    /**
     * 状态名称
     */
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String desc;

    public static KnetOrderGroupStatus fromCode(int code) {
        for (KnetOrderGroupStatus status : KnetOrderGroupStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }

    /**
     * 已支付 冻结转换成为待发货
     *
     * @param status status
     * @return 状态
     */
    public static KnetOrderGroupStatus displayStatus(KnetOrderGroupStatus status) {
        if (BeanUtil.isEmpty(status)) {
            return null;
        }
        if (status == KnetOrderGroupStatus.PAID ||
                status == KnetOrderGroupStatus.FROZEN) {
            return KnetOrderGroupStatus.PENDING_SHIPMENT;
        }
        return status;
    }

    /**
     * 判断是否为可释放库存的状态
     */
    public static boolean isReleasableState(KnetOrderGroupStatus status) {
        return switch (status) {
            case PAID, PENDING_SHIPMENT, IN_TRANSIT, COMPLETED -> true;
            default -> false;
        };
    }
}
