package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:25
 * @description: 消息状态枚举
 */
@Getter
@AllArgsConstructor
public enum MessageStatus {

    /**
     * 待发送
     */
    PENDING(0, "待发送"),
    SENDING(1, "发送中"),
    SUCCESS(2, "发送成功"),
    FAILED(3, "发送失败"),
    READ(4, "已读");

    @EnumValue
    private final Integer code;
    private final String desc;

    public static MessageStatus of(Integer code) {
        if (code == null) {
            return null;
        }
        for (MessageStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}