package com.knet.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StockXCurrencyCode {
    AUD("AUD"),
    CAD("CAD"),
    CHF("CHF"),
    EUR("EUR"),
    GBP("GBP"),
    HKD("HKD"),
    JPY("JPY"),
    KRW("KRW"),
    MXN("MXN"),
    NZD("NZD"),
    SGD("SGD"),
    USD("USD");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
