package com.knet.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum KnetCurrencyCode {

    /**
     * 人民币
     */
    CNY("CNY"),
    AUD("AUD"),
    CAD("CAD"),
    CHF("CHF"),
    EUR("EUR"),
    GBP("GBP"),
    HKD("HKD"),
    JPY("JPY"),
    KRW("KRW"),
    MXN("MXN"),
    NZD("NZD"),
    SGD("SGD"),
    USD("USD");

    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }
}
