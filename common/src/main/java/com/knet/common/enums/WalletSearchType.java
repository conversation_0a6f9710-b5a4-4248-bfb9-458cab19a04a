package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/5 17:57
 * @description: 钱包记录搜索类型枚举
 */
@Getter
@AllArgsConstructor
public enum WalletSearchType {

    /**
     * 支出
     */
    EXPENSES(1, "EXPENSES", "支出"),

    /**
     * 收入
     */
    INCOME(2, "INCOME", "收入"),

    /**
     * 充值
     */
    ADD_FUNDS(3, "ADD_FUNDS", "充值");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 交易类型名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    public static WalletSearchType fromCode(int code) {
        for (WalletSearchType walletRecordType : WalletSearchType.values()) {
            if (walletRecordType.getCode() == code) {
                return walletRecordType;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
