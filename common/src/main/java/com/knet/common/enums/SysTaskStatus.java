package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/12/26 14:56
 * @description: 系统任务-状态
 */
@Getter
@AllArgsConstructor
public enum SysTaskStatus {

    /**
     * 初始状态 代办
     */
    PENDING("PENDING"),
    /**
     * 处理中
     */
    PROCESSING("PROCESSING"),
    /**
     * 终态 成功
     */
    SUCCESS("SUCCESS"),
    /**
     * 终态 失败
     */
    FAILURE("FAILURE");

    @EnumValue
    @JsonValue
    public final String value;

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return this.value;
    }

}
