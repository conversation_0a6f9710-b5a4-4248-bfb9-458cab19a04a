package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/2/17 10:16
 * @description: 用户状态
 */
@Getter
@AllArgsConstructor
public enum UserStatus {
    /**
     * 启用
     */
    ENABLE(1, "ENABLE"),
    /**
     * 禁用
     */
    DISABLE(0, "DISABLE");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称，用于数据库存储
     */
    @EnumValue
    @JsonValue
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static UserStatus fromCode(int code) {
        for (UserStatus status : UserStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
