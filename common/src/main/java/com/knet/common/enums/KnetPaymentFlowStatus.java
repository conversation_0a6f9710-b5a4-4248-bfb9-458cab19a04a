package com.knet.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:10
 * @description: 支付流水支付状态
 */
@Getter
@AllArgsConstructor
public enum KnetPaymentFlowStatus {

    /**
     * 待支付
     */
    PENDING(0, "PENDING", "pending"),
    /**
     * 支付中
     */
    IN_PROGRESS(1, "IN_PROGRESS", "inProgress"),
    /**
     * 支付成功
     */
    SUCCESS(2, "SUCCESS", "success"),
    /**
     * 支付失败
     */
    FAILED(3, "FAILED", "failed");

    /**
     * 状态代码
     */
    @EnumValue
    private final Integer code;

    /**
     * 状态名称，用于数据库存储
     */
    @JsonValue
    private final String name;

    /**
     * 描述
     */
    private final String description;

    public static KnetPaymentFlowStatus fromCode(int code) {
        for (KnetPaymentFlowStatus status : KnetPaymentFlowStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown enum code " + code);
    }
}
