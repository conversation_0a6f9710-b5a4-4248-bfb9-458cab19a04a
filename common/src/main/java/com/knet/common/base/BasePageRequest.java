package com.knet.common.base;

import lombok.Data;

import javax.validation.constraints.Min;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:05
 * @description: 分页查询基础抽象类
 */
@Data
public class BasePageRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 页码数,默认第一页
     */
    @Min(value = 1, message = "页码数必须大于0")
    private Integer pageNo = 1;
    /**
     * 每页条数，默认每页10条
     */
    private Integer pageSize = 10;

    /**
     * 获取查询开始页
     *
     * @return 查询开始页
     */
    public Integer getQueryStartPage() {
        return (pageNo == null || pageNo <= 0) ? 0 : (pageNo - 1);
    }
}
