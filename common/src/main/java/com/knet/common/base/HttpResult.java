package com.knet.common.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/2/10 13:29
 * @description: 统一返回实体
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class HttpResult<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -7331388445019110786L;

    private int code = 200;

    private String msg = "";

    private T data;

    public HttpResult() {
    }

    public HttpResult(T data) {
        this.data = data;
    }

    public HttpResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public HttpResult(int code, T data) {
        this.code = code;
        this.data = data;
    }

    public static <T> HttpResult<T> ok() {
        return new HttpResult<T>();
    }

    public static <T> HttpResult<T> ok(T data) {
        return new HttpResult<T>(data);
    }

    public static <T> HttpResult<T> error() {
        return new HttpResult<T>(500, "未知异常，请联系管理员");
    }

    public static <T> HttpResult<T> error(String msg) {
        return new HttpResult<T>(500, msg);
    }

    public static <T> HttpResult<T> error(int code, String msg) {
        return new HttpResult<T>(code, msg);
    }

    public static <T> HttpResult<T> error(int code, String msg, T data) {
        return new HttpResult<T>(code, msg, data);
    }
}
