package com.knet.common.config;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/4/25 13:34
 * @description: String 类型 价格自定义序列化器（分转元）-保留2位小数
 */
public class PriceRemainderSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StrUtil.isBlank(value)) {
            gen.writeNull();
            return;
        }
        // 将数值除以100并保留2位小数
        Double price = NumberUtil.parseDouble(StrUtil.trim(value), 0.00);
        double b2bSalePriceFinal = NumberUtil.div(price, Double.valueOf(100), 2);
        // 使用String.format确保始终保留2位小数
        String formattedPrice = String.format("%.2f", b2bSalePriceFinal);
        gen.writeString(formattedPrice);
    }
}
