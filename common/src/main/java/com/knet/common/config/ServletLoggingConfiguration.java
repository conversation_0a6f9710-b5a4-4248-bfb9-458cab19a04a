package com.knet.common.config;

import com.knet.common.aspect.OptimizedLoggableAspect;
import com.knet.common.filter.RequestContextFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * @description: Servlet环境下的日志组件配置类
 */
@Slf4j
@Configuration
@ConditionalOnClass(Filter.class)
@ConditionalOnProperty(name = "knet.logging.enabled", havingValue = "true", matchIfMissing = true)
public class ServletLoggingConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public OptimizedLoggableAspect optimizedLoggableAspect() {
        log.info("Initializing OptimizedLoggableAspect for Servlet environment");
        return new OptimizedLoggableAspect();
    }

    @Bean(name = "knetRequestContextFilter")
    @ConditionalOnMissingBean(name = "knetRequestContextFilter")
    public RequestContextFilter knetRequestContextFilter() {
        log.info("Initializing KNet RequestContextFilter for Servlet environment");
        return new RequestContextFilter();
    }
}
