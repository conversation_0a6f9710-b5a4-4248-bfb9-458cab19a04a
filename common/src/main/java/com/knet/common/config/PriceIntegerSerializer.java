package com.knet.common.config;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2025/4/25 13:34
 * @description: Integer类型 价格自定义序列化器（分转元）-保留整数部分
 */
public class PriceIntegerSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (StrUtil.isBlank(value)) {
            gen.writeNull();
            return;
        }
        // 将数值除以100并保留2位小数
        Integer price = NumberUtil.parseInt(StrUtil.trim(value), 0);
        double priceInYuan = price / 100.0;
        // 格式化为保留2位小数的字符串
        String formattedPrice = NumberUtil.decimalFormat("0.00", priceInYuan);
        gen.writeString(formattedPrice);
    }
}
