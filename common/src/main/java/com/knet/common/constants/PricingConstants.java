package com.knet.common.constants;

/**
 * <AUTHOR>
 * @date 2025/7/31
 * @description: 价格策略常量
 */
public class PricingConstants {
    
    /**
     * 价格策略阈值：100美元（以美分为单位）
     */
    public static final Long PRICE_THRESHOLD_CENTS = 10000L;
    
    /**
     * 低于阈值时的固定加价：10美元（以美分为单位）
     */
    public static final Long FIXED_MARKUP_CENTS = 1000L;
    
    /**
     * 高于阈值时的百分比加价：10%
     */
    public static final Double PERCENTAGE_MARKUP = 0.10;
    
    /**
     * 原始价格缓存key前缀
     */
    public static final String ORIGINAL_PRICE_CACHE_PREFIX = "original_price:";
    
    /**
     * 策略价格缓存key前缀
     */
    public static final String STRATEGY_PRICE_CACHE_PREFIX = "strategy_price:";
}
