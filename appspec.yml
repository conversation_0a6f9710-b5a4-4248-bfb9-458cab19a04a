version: 0.0
os: linux
files:
  - source: ./jars.tar.gz
    destination: /app/knet/java_server/
hooks:
  # BeforeInstall:
  #   - location:
  #     timeout: 60
  AfterInstall:
    - location: unpack.sh
      timeout: 120
  ApplicationStart:
    - location: restart-all.sh
      timeout: 300
  ApplicationStop:
    - location: stop-all.sh
      timeout: 300
  ValidateService:
    - location: health-check-all.sh
      timeout: 400
