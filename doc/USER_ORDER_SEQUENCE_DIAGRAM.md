# 用户下单流程时序图

## 概述

本文档描述了KNET微服务电商平台中用户从购物车下单的完整时序流程。该流程采用了分布式事务、事件驱动架构和消息队列等技术来保证数据一致性和系统可靠性。

## 核心架构特点

- **微服务架构**: 订单服务、用户服务、支付服务、商品服务、通知服务、延迟服务
- **分布式锁**: 基于Redis实现，防止重复下单
- **事件驱动**: 订单创建后通过事件机制触发后续处理
- **消息队列**: 使用RabbitMQ实现异步处理和最终一致性
- **SAGA模式**: 分布式事务处理，支持补偿机制

## 用户下单时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Gateway as API网关<br/>
    participant OrderSvc as 订单服务<br/>
    participant UserSvc as 用户服务<br/>
    participant GoodsSvc as 商品服务<br/>
    participant PaymentSvc as 支付服务<br/>
    participant Redis as Redis缓存
    participant DB as 数据库
    participant RabbitMQ as 消息队列<br/>
    participant DelayedSvc as 延迟服务<br/>
    participant NotificationSvc as 通知服务<br/>

    Note over User,NotificationSvc: 用户下单完整流程

    %% 1. 用户提交订单
    User->>Gateway: 1. 用户提交订单 POST /order/create<br/>
    Note right of User:POST /order/create<br/>
    
    %% 2. 网关权限验证
    Gateway->>Gateway: 2.1 JWT令牌验证
    Gateway->>OrderSvc: 2.2 转发请求到订单服务

    %% 3. 订单服务处理（加分布式锁）
    OrderSvc->>Redis: 3.1  订单服务处理（加分布式锁）
    Note right of OrderSvc:@DistributedLock获取分布式锁
    Redis-->>OrderSvc: 3.2 获取锁成功

    %% 4. 验证和分组订单数据
    OrderSvc->>OrderSvc: 4.1 验证和分组订单数据
    Note right of OrderSvc: 按SKU分组商品<br/>验证数量和价格

    %% 5. 创建母订单
    OrderSvc->>DB: 5.1 创建SysOrderGroup记录
    Note right of OrderSvc: 生成parentOrderId<br/>状态：PENDING

    %% 6. 创建子订单和订单明细
    loop 每个SKU商品
        OrderSvc->>DB: 6.1 创建SysOrder子订单
        OrderSvc->>DB: 6.2 创建SysOrderItem订单明细
    end

    %% 7. 本地事务提交后发送事件
    OrderSvc->>OrderSvc: 7.1 @Transactional提交本地事务
    OrderSvc->>OrderSvc: 7.2 发布OrderCreatedEvent
    Note right of OrderSvc: Spring Events机制

    %% 8. 事务提交后异步处理
    OrderSvc->>RabbitMQ: 8.1 发送order.created消息
    Note right of OrderSvc: @TransactionalEventListener<br/>AFTER_COMMIT阶段

    OrderSvc->>DelayedSvc: 8.2 发送延迟消息
    Note right of OrderSvc: 订单超时取消机制<br/>默认30分钟

    %% 9. 清理用户数据
    OrderSvc->>Redis: 9.1 清除订单列表缓存
    OrderSvc->>UserSvc: 9.2 清空购物车
    UserSvc->>DB: 9.3 删除购物车数据

    %% 10. 返回响应
    OrderSvc->>Redis: 10.1 释放分布式锁
    OrderSvc-->>Gateway: 10.2 返回CreateOrderResponse
    Gateway-->>User: 10.3 返回订单创建结果

    %% 11. 异步消息处理
    RabbitMQ->>GoodsSvc: 11.1 处理库存锁定
    Note right of GoodsSvc: 锁定商品库存<br/>预留商品

    RabbitMQ->>PaymentSvc: 11.2 创建支付订单
    Note right of PaymentSvc: 生成支付单<br/>等待用户支付

    RabbitMQ->>NotificationSvc: 11.3 发送订单通知
    NotificationSvc->>User: 11.4 邮件通知

    %% 12. 延迟消息处理
    DelayedSvc->>DelayedSvc: 12.1 延迟队列监控

    alt 订单超时未支付
        DelayedSvc->>RabbitMQ: 12.2a 发送order.timeout消息
        RabbitMQ->>OrderSvc: 12.3a 自动取消订单
        OrderSvc->>DB: 12.4a 更新订单状态为CANCELLED
        OrderSvc->>RabbitMQ: 12.5a 发送order.cancelled消息
        RabbitMQ->>GoodsSvc: 12.6a 释放库存锁定
        RabbitMQ->>NotificationSvc: 12.7a 发送取消通知
    else 用户完成支付
        PaymentSvc->>RabbitMQ: 12.2b 发送payment.success消息
        RabbitMQ->>OrderSvc: 12.3b 更新订单状态为PAID
        OrderSvc->>DB: 12.4b 更新订单状态
        Note right of OrderSvc: 状态流转：<br/>PENDING → PAID → SHIPPED → COMPLETED
    end

    %% 异常处理流程
    Note over OrderSvc,NotificationSvc: 异常处理和补偿机制

    alt 订单创建失败
        OrderSvc->>OrderSvc: 13.1 发布OrderFailedEvent
        OrderSvc->>RabbitMQ: 13.2 发送补偿消息
        RabbitMQ->>GoodsSvc: 13.3 释放已锁定资源
        RabbitMQ->>NotificationSvc: 13.4 发送失败通知
    end
```

## 关键技术实现

### 1. 分布式锁实现

```java
@DistributedLock(key = "'createOrder:' + #request.hashCode()", expire = 2)
public HttpResult<CreateOrderResponse> createOrder(@RequestBody CreateOrderRequest request)
```

- 基于Redis setnx实现
- 防止用户重复提交订单
- 锁过期时间2秒

### 2. 事务事件监听

```java
@TransactionalEventListener(
    classes = OrderCreatedEvent.class,
    phase = TransactionPhase.AFTER_COMMIT
)
public void handleOrderCreatedEvent(OrderCreatedEvent event)
```

- 本地事务提交后才发送MQ消息
- 保证数据一致性

### 3. SAGA分布式事务

- **正常流程**: 订单创建 → 库存锁定 → 支付处理 → 订单完成
- **补偿流程**: 任何环节失败都会触发补偿事件，回滚已执行的操作

### 4. 订单状态流转

```
PENDING（待支付） → PAID（已支付） → SHIPPED（已发货） → COMPLETED（已完成）
                ↓
            CANCELLED（已取消）
```

### 5. 消息队列设计

| 队列名称 | 用途 | 生产者 | 消费者 |
|---------|------|--------|--------|
| order.created | 订单创建成功 | order-services | goods/payment/notification |
| order.cancelled | 订单取消 | order-services | goods/notification |
| order.timeout | 订单超时 | delayed-services | order-services |
| payment.success | 支付成功 | payment-services | order-services |

## 核心服务职责

### 订单服务 (order-services:7004)
- 订单创建和状态管理
- 子母订单处理
- 分布式事务协调
- 事件发布

### 用户服务 (user-services:7002)
- 购物车管理
- 用户地址验证
- 用户信息查询

### 商品服务 (goods-services:7001)
- 库存锁定和释放
- 商品信息验证
- 价格策略处理

### 支付服务 (payment-services:7005)
- 支付订单创建
- 支付状态通知
- 用户钱包管理

### 延迟服务 (delayed-services:7007)
- 订单超时处理
- 延迟消息调度
- 基于Redis Zset + XXL-Job实现

### 通知服务 (notification-services:7006)
- 订单状态通知
- 邮件/短信发送




