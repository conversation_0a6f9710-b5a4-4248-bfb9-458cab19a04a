# 订单服务缓存配置说明

## 概述

本文档说明了order-services模块中@Cacheable注解的配置和使用方法。

## 配置完成的内容

### 1. 主启动类配置
- 在`OrderServicesApplication.java`中添加了`@EnableCaching`注解
- 启用了Spring Cache功能

### 2. 缓存管理器配置
- 创建了`CacheConfig.java`配置类
- 配置了基于Redis的CacheManager
- 设置了默认缓存过期时间为30分钟
- 配置了JSON序列化方式

### 3. 缓存常量定义
- 在`OrderServicesConstants.java`中添加了缓存相关常量
- `ORDER_LIST_CACHE_NAME = "orderList"`
- `ORDER_LIST_CACHE_EXPIRED_TIME = 30 * 60L`

### 4. 控制器缓存注解
- 在`OrderController.queryOrderList`方法上添加了`@Cacheable`注解
- 缓存key包含用户ID、分页参数和查询条件
- 配置了缓存条件，避免缓存空结果

### 5. 缓存清除机制
- 在订单创建成功后自动清除相关缓存
- 使用`RedisCacheUtil.deleteByPattern`方法进行精确的模糊匹配删除
- 在`SysOrderProcessServiceImpl.createOrderFromCart`方法中实现

### 6. 测试控制器
- 创建了`CacheTestController.java`用于测试缓存功能
- 提供了缓存测试和清除接口

## 缓存配置详情

### 缓存Key格式
```
orderList:{userId}:{pageNo}:{pageSize}:{orderId|all}
```

### 缓存过期时间
- 默认：30分钟
- 订单列表：30分钟

### 序列化配置
- Key序列化：StringRedisSerializer
- Value序列化：GenericJackson2JsonRedisSerializer

## 使用方法

### 1. 查询订单列表（自动缓存）
```bash
POST /orderService/order/list
{
    "userId": 123,
    "pageNo": 1,
    "pageSize": 10,
    "orderId": null
}
```

### 2. 测试缓存功能
```bash
GET /orderService/cache/test
```

### 3. 清除所有缓存
```bash
GET /orderService/cache/clear
```

### 4. 清除指定用户的订单缓存
```bash
GET /orderService/cache/clear/user?userId=123
```

### 5. 测试模糊删除功能
```bash
GET /orderService/cache/test/pattern?pattern=order-service:orderList:*
```

## 缓存策略

### 缓存条件
- 只缓存非空结果
- 只缓存有数据的分页结果

### 缓存清除
- 订单创建成功后自动清除该用户的所有订单列表缓存
- 使用`deleteByPattern`方法精确匹配用户相关的缓存key
- 支持手动清除所有缓存或指定用户的缓存

### 缓存穿透保护
- 配置了`unless`条件避免缓存空结果
- 使用`disableCachingNullValues()`避免缓存null值

## 注意事项

1. **缓存Key设计**：包含了用户ID、分页参数和查询条件，确保不同查询条件的结果被正确缓存

2. **缓存清除**：使用`RedisCacheUtil.deleteByPattern`方法实现精确的模糊匹配删除，只清除相关用户的缓存

3. **性能考虑**：缓存过期时间设置为30分钟，平衡了数据一致性和性能

4. **错误处理**：缓存操作失败不会影响业务逻辑的正常执行

## 监控和调试

### 查看Redis中的缓存数据
```bash
# 连接Redis
redis-cli

# 查看所有缓存key
KEYS "order-service:*"

# 查看特定缓存内容
GET "order-service:orderList:orderList:123:1:10:all"
```

### 日志监控
- 缓存命中和未命中会在日志中体现
- 缓存清除操作会记录日志

## 扩展说明

如需添加更多缓存功能，可以：

1. 在`CacheConfig.java`中添加新的缓存配置
2. 在常量类中定义新的缓存名称
3. 在相应的方法上添加`@Cacheable`、`@CacheEvict`或`@CachePut`注解

## 相关文件

- `OrderServicesApplication.java` - 主启动类，启用缓存
- `CacheConfig.java` - 缓存配置类
- `RedisConfig.java` - Redis配置类
- `OrderController.java` - 订单控制器，使用缓存
- `SysOrderProcessServiceImpl.java` - 订单服务实现，缓存清除
- `CacheTestController.java` - 缓存测试控制器
- `OrderServicesConstants.java` - 缓存常量定义
