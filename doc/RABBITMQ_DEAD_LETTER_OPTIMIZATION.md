# RabbitMQ死信队列优化方案

## 📋 问题分析

### 原有配置问题

1. **统一死信交换机问题**
   - 所有服务使用同一个死信交换机 `"DLX"`
   - 死信消息混合在一起，难以区分和管理
   - 路由键冲突，可能导致死信消息路由错误

2. **缺少死信消费者**
   - 配置了死信队列但没有消费者处理
   - 死信消息会一直堆积，无法及时发现和处理问题

3. **死信队列命名不规范**
   - 各服务死信队列命名不一致
   - 缺乏统一的命名规范

4. **缺少死信监控和告警**
   - 没有死信消息的记录和统计
   - 无法及时发现系统问题

## 🎯 优化方案

### 1. 按服务分离死信交换机

#### 商品服务 (goods-services)
```
死信交换机: goods-service.dlx
死信队列:
  - goods-service.dlx.order.queue (订单相关死信)
  - goods-service.dlx.price-change.queue (价格变动死信)
路由键规范:
  - goods.order.*
  - goods.price.change.*
```

#### 订单服务 (order-services)
```
死信交换机: order-service.dlx
死信队列:
  - order-service.dlx.order.queue (订单死信)
  - order-service.dlx.payment-result.queue (支付结果死信)
  - order-service.dlx.inventory-compensation.queue (库存补偿死信)
  - order-service.dlx.inventory-lock-success.queue (库存锁定成功死信)
路由键规范:
  - order.order.*
  - order.payment.result.*
  - order.inventory.failed.*
  - order.inventory.lock.success.*
```

#### 支付服务 (payment-services)
```
死信交换机: payment-service.dlx
死信队列:
  - payment-service.dlx.user-operation.queue (用户操作死信)
  - payment-service.dlx.order.queue (订单死信)
  - payment-service.dlx.payment-result.queue (支付结果死信)
  - payment-service.dlx.inventory-compensation.queue (库存补偿死信)
  - payment-service.dlx.notification.queue (通知死信)
路由键规范:
  - payment.user.operation.*
  - payment.order.*
  - payment.payment.result.*
  - payment.inventory.failed.*
  - payment.notification.*
```

### 2. 死信消费者实现

每个服务都实现了专门的死信消费者：
- `GoodsDeadLetterConsumer` - 商品服务死信消费者
- `OrderDeadLetterConsumer` - 订单服务死信消费者
- `PaymentDeadLetterConsumer` - 支付服务死信消费者

### 3. 死信消息管理

#### 死信消息实体 (`SysDeadLetterMessage`)
包含以下关键字段：
- 服务信息：serviceName, messageType
- 消息信息：messageId, messageBody
- 路由信息：originalExchange, originalRoutingKey, deadLetterExchange
- 死信信息：deathReason, deathInfo
- 处理状态：status, retryCount, resolvedTime
- 告警信息：alertSent, alertTime, priority
- 业务信息：businessKey, remark

#### 死信消息服务 (`DeadLetterMessageService`)
提供以下功能：
- 记录死信消息
- 更新处理状态
- 发送告警通知
- 统计分析

### 4. 配置变更总结

#### 队列死信配置更新
所有业务队列的死信配置都已更新：

**原配置：**
```java
.withArgument("x-dead-letter-exchange", "DLX")
.withArgument("x-dead-letter-routing-key", "order.*")
```

**新配置：**
```java
.withArgument("x-dead-letter-exchange", "goods-service.dlx")
.withArgument("x-dead-letter-routing-key", "goods.order.*")
```

## 🚀 优化效果

### 1. 服务隔离
- 每个服务有独立的死信交换机和队列
- 死信消息不会相互干扰
- 便于服务独立部署和维护

### 2. 问题定位
- 死信消息按服务和业务类型分类
- 快速定位问题所在的服务和业务模块
- 支持按优先级处理死信消息

### 3. 监控告警
- 完整的死信消息记录
- 支持统计分析和趋势监控
- 可配置告警规则和通知方式

### 4. 运维管理
- 规范化的命名规则
- 统一的处理流程
- 支持人工介入处理

## 📊 监控指标

### 死信消息统计
- 总死信数量
- 待处理死信数量
- 已解决死信数量
- 处理失败死信数量
- 今日新增死信数量
- 高优先级死信数量

### 告警规则建议
1. **死信数量告警**：单个服务死信数量超过阈值
2. **处理时间告警**：死信消息超过指定时间未处理
3. **高优先级告警**：高优先级死信消息立即告警
4. **趋势告警**：死信数量增长趋势异常

## 🔧 部署建议

### 1. 数据库表创建
需要创建 `sys_dead_letter_message` 表来存储死信消息记录。

### 2. 配置更新
- 更新各服务的RabbitMQ配置
- 部署死信消费者
- 配置死信消息服务

### 3. 监控配置
- 配置死信队列监控
- 设置告警规则
- 建立处理流程

### 4. 测试验证
- 验证死信消息正确路由
- 测试死信消费者功能
- 验证告警机制

## 📝 注意事项

1. **向后兼容**：新配置部署前需要确保旧的死信消息已处理完毕
2. **监控覆盖**：确保所有死信队列都有对应的监控
3. **告警配置**：根据业务重要性配置不同的告警级别
4. **处理流程**：建立死信消息的处理标准操作程序(SOP)
5. **定期清理**：建立死信消息的定期清理机制

## 🔄 后续优化

1. **自动重试机制**：对于某些类型的死信消息，可以实现自动重试
2. **业务补偿**：针对业务死信消息，实现自动补偿机制
3. **智能告警**：基于历史数据实现智能告警阈值调整
4. **可视化监控**：开发死信消息监控面板
5. **性能优化**：优化死信消息处理性能
