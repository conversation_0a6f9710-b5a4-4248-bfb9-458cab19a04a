# 支付接口幂等性实现说明

## 概述

支付服务的 `createPayment` 方法已经通过 `@DistributedLock` 注解实现了完善的幂等性控制，确保同一用户的同一订单在同一时间只能有一个支付请求被处理。

## 实现原理

### 1. 分布式锁机制

```java
@DistributedLock(key = "'payment:create:'+#request.userId+':'+#request.orderId", expire = 10)
@Override
@Transactional(rollbackFor = Exception.class)
public CreatePaymentResponse createPayment(CreatePaymentRequest request) {
    // 支付逻辑
}
```

### 2. 锁键设计

- **锁键格式**: `distributedLock:payment:create:{userId}:{orderId}`
- **唯一性保证**: 基于用户ID和订单ID生成，确保同一用户的同一订单具有唯一锁
- **SpEL表达式**: 支持动态参数解析

### 3. 核心特性

1. **原子性**: 使用 Redis `setIfAbsent` 确保原子操作
2. **自动释放**: 方法执行完成后自动释放锁
3. **异常安全**: 使用 try-finally 确保锁一定会被释放
4. **超时控制**: 10秒过期时间，防止死锁

## 幂等性效果

### 场景1: 重复提交
```
用户A对订单ORD-123发起支付请求
├── 第1次请求: 获取锁成功 → 正常处理
├── 第2次请求: 获取锁失败 → 返回"支付请求正在处理中，请勿重复提交"
└── 第3次请求: 获取锁失败 → 返回"支付请求正在处理中，请勿重复提交"
```

### 场景2: 并发请求
```
用户A对订单ORD-123同时发起多个支付请求
├── 请求1: 获取锁成功 → 正常处理
├── 请求2: 获取锁失败 → 幂等性拦截
├── 请求3: 获取锁失败 → 幂等性拦截
└── 请求4: 获取锁失败 → 幂等性拦截
```

### 场景3: 不同订单
```
用户A对不同订单发起支付请求
├── 订单ORD-123: 获取锁成功 → 正常处理
├── 订单ORD-124: 获取锁成功 → 正常处理 (不同锁键)
└── 订单ORD-125: 获取锁成功 → 正常处理 (不同锁键)
```

## 客户端处理建议

### 1. 错误处理

```javascript
async function createPayment(paymentData) {
    try {
        const response = await fetch('/api/payment/create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(paymentData)
        });
        
        if (!response.ok) {
            const error = await response.json();
            if (error.message.includes('重复提交')) {
                // 幂等性冲突，提示用户
                showMessage('支付请求正在处理中，请稍候...');
                return;
            }
            throw new Error(error.message);
        }
        
        return await response.json();
    } catch (error) {
        console.error('支付请求失败:', error);
        throw error;
    }
}
```

### 2. 重试机制

```javascript
async function createPaymentWithRetry(paymentData, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            return await createPayment(paymentData);
        } catch (error) {
            if (error.message.includes('重复提交')) {
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 2000));
                continue;
            }
            throw error; // 其他错误直接抛出
        }
    }
    throw new Error('支付请求失败，请稍后重试');
}
```

### 3. 前端防重复提交

```javascript
let isSubmitting = false;

async function handlePaymentSubmit(paymentData) {
    if (isSubmitting) {
        showMessage('请勿重复提交');
        return;
    }
    
    isSubmitting = true;
    try {
        const result = await createPayment(paymentData);
        showMessage('支付请求提交成功');
        return result;
    } catch (error) {
        showMessage('支付请求失败: ' + error.message);
    } finally {
        isSubmitting = false;
    }
}
```

## 监控和日志

### 1. 分布式锁日志

系统会自动记录分布式锁相关的日志：

```
2025-06-03 16:00:00.123 INFO  [payment-service] 尝试获取分布式锁: distributedLock:payment:create:1001:ORD-123
2025-06-03 16:00:00.125 INFO  [payment-service] 分布式锁获取成功，开始执行支付逻辑
2025-06-03 16:00:02.234 INFO  [payment-service] 支付逻辑执行完成，释放分布式锁
```

### 2. 幂等性冲突日志

```
2025-06-03 16:00:00.126 WARN  [payment-service] 分布式锁获取失败，请求被拦截: distributedLock:payment:create:1001:ORD-123
```

### 3. 监控指标建议

- 分布式锁获取成功率
- 分布式锁冲突频率
- 支付请求处理时间
- 幂等性拦截次数

## 配置说明

### 1. 锁过期时间

当前设置为10秒，可根据业务需要调整：

```java
@DistributedLock(key = "'payment:create:'+#request.userId+':'+#request.orderId", expire = 10)
```

### 2. Redis配置

确保Redis的高可用性，建议使用Redis集群或主从配置。

## 注意事项

1. **锁键唯一性**: 确保锁键在业务范围内的唯一性
2. **过期时间**: 根据业务处理时间合理设置过期时间
3. **错误处理**: 正确处理幂等性冲突的错误信息
4. **性能影响**: 分布式锁会增加Redis访问，注意性能影响
5. **Redis可用性**: 确保Redis服务的高可用性

## 总结

通过 `@DistributedLock` 注解，支付服务已经实现了完善的幂等性控制：

- ✅ 防止重复提交
- ✅ 支持并发控制
- ✅ 自动锁释放
- ✅ 异常安全
- ✅ 用户友好的错误提示

这个实现简洁、高效，完全满足支付场景的幂等性需求。
