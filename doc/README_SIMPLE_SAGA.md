# 简化版SAGA模式实现

## 设计理念

这是一个**极简、低侵入性**的SAGA模式实现，遵循以下原则：

1. **最小侵入性**：业务代码几乎不需要修改
2. **事件驱动**：通过消息事件实现服务间协调
3. **简单可靠**：成功发送成功消息，失败发送失败消息
4. **自动补偿**：其他服务订阅失败消息自动补偿

## 核心思路

### 业务流程

```
订单创建 → 成功 → 发送 order.created 消息 → 其他服务处理后续逻辑
         ↓
        失败 → 发送 order.failed 消息 → 其他服务执行补偿操作
```

### 消息流转

```
1. 订单服务创建订单
   ├── 成功：发送 order.created 到 order-exchange
   └── 失败：发送 order.failed 到 order-exchange

2. 库存服务订阅 order.created
   ├── 扣减库存成功：继续后续流程
   └── 扣减库存失败：取消订单（补偿）

3. 其他服务订阅 order.failed
   └── 执行各自的补偿逻辑
```

## 实现特点

### 1. 消息发送策略

- **成功消息**：纳入事务，发送失败则事务回滚
- **失败消息**：异步发送，不影响事务回滚
- **超时控制**：3秒超时，快速失败

### 2. 零侵入性设计

```java

@Transactional
public String createOrder(Map<String, Object> orderRequest) {
    try {
        // 1. 执行业务逻辑
        String orderId = executeOrderCreation(orderRequest);

        // 2. 发送成功消息（纳入事务）
        orderProducer.sendOrderCreateSuccessEvent(message, orderId);

        return orderId;
    } catch (Exception e) {
        // 3. 发送失败消息（异步，不影响回滚）
        orderProducer.sendOrderCreateFailedEvent(message, orderId, e.getMessage());
        throw e;
    }
}
```

### 3. 自动补偿机制

```java

@RabbitListener(queues = "order.created.queue")
public void handleOrderCreated(String messageBody) {
    // 处理订单创建成功事件
    boolean success = inventoryService.deductInventory(...);
    if (!success) {
        // 库存不足，自动补偿订单
        orderService.cancelOrder(orderId, "库存不足");
    }
}

@RabbitListener(queues = "order.failed.queue")
public void handleOrderFailed(String messageBody) {
    // 处理订单创建失败事件，执行补偿
    inventoryService.restoreInventory(...);
}
```

## 使用方式

### 1. 创建成功的测试订单

```bash
curl -X POST "http://localhost:8080/orderService/simple/test/order" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "USER_001",
    "productId": "PRODUCT_001", 
    "quantity": 1,
    "amount": 100.00
  }'
```

### 2. 创建失败的测试订单

```bash
curl -X POST "http://localhost:8080/orderService/simple/test/order/fail"
```

### 3. 查询订单

```bash
curl -X GET "http://localhost:8080/orderService/simple/order/{orderId}"
```

### 4. 查询库存

```bash
curl -X GET "http://localhost:8080/orderService/simple/inventory/PRODUCT_001"
```

### 5. 查看所有库存（调试）

```bash
curl -X GET "http://localhost:8080/orderService/simple/inventory/all"
```

### 6. 查看库存操作记录（调试）

```bash
curl -X GET "http://localhost:8080/orderService/simple/inventory/records"
```

## 消息队列配置

### 交换机和队列

```
order-exchange
├── order.created → order.created.queue
└── order.failed → order.failed.queue

inventory-exchange
└── inventory.failed → inventory.failed.queue
```

### 消息格式

#### 订单创建成功消息

```json
{
  "orderId": "ORDER_1642234567890",
  "orderData": {
    "orderId": "ORDER_1642234567890",
    "userId": "USER_001",
    "productId": "PRODUCT_001",
    "quantity": 1,
    "amount": 100.00,
    "status": "CREATED",
    "createTime": 1642234567890
  },
  "timestamp": 1642234567890,
  "eventType": "ORDER_CREATED"
}
```

#### 订单创建失败消息

```json
{
  "orderId": "ORDER_1642234567890",
  "orderRequest": {
    "userId": "ERROR_USER",
    "productId": "PRODUCT_001",
    "quantity": 1,
    "amount": 100.00
  },
  "timestamp": 1642234567890,
  "eventType": "ORDER_FAILED",
  "errorMessage": "用户不存在"
}
```

## 扩展其他服务

### 1. 支付服务示例

```java

@Component
public class PaymentEventConsumer {

    @RabbitListener(queues = "order.created.queue")
    public void handleOrderCreated(String messageBody) {
        // 解析订单信息
        Map<String, Object> eventData = JSON.parseObject(messageBody, Map.class);
        String orderId = (String) eventData.get("orderId");

        try {
            // 执行支付逻辑
            boolean paymentSuccess = paymentService.processPayment(orderId);

            if (paymentSuccess) {
                // 支付成功，发送支付成功事件
                paymentProducer.sendPaymentSuccessEvent(orderId);
            } else {
                // 支付失败，发送支付失败事件
                paymentProducer.sendPaymentFailedEvent(orderId, "支付失败");
            }
        } catch (Exception e) {
            // 支付异常，发送支付失败事件
            paymentProducer.sendPaymentFailedEvent(orderId, e.getMessage());
        }
    }

    @RabbitListener(queues = "order.failed.queue")
    public void handleOrderFailed(String messageBody) {
        // 订单失败，取消预授权等补偿操作
        Map<String, Object> eventData = JSON.parseObject(messageBody, Map.class);
        String orderId = (String) eventData.get("orderId");

        paymentService.cancelPreAuth(orderId);
    }
}
```

### 2. 通知服务示例

```java

@Component
public class NotificationEventConsumer {

    @RabbitListener(queues = "order.created.queue")
    public void handleOrderCreated(String messageBody) {
        // 发送订单创建成功通知
        notificationService.sendOrderCreatedNotification(orderId, userId);
    }

    @RabbitListener(queues = "order.failed.queue")
    public void handleOrderFailed(String messageBody) {
        // 发送订单创建失败通知
        notificationService.sendOrderFailedNotification(orderId, userId, reason);
    }
}
```

## 优势对比

### 与复杂SAGA方案对比

| 特性   | 复杂SAGA       | 简化SAGA      |
|------|--------------|-------------|
| 侵入性  | 高（需要大量注解和配置） | 极低（只需要消息发送） |
| 学习成本 | 高            | 低           |
| 维护成本 | 高            | 低           |
| 状态管理 | 复杂（需要状态表）    | 简单（事件驱动）    |
| 补偿机制 | 自动化程度高       | 需要手动实现      |
| 适用场景 | 复杂业务流程       | 简单业务流程      |

### 与传统方案对比

| 特性  | 传统同步调用  | 简化SAGA  |
|-----|---------|---------|
| 可靠性 | 低（单点故障） | 高（异步解耦） |
| 性能  | 差（同步等待） | 好（异步处理） |
| 扩展性 | 差（紧耦合）  | 好（松耦合）  |
| 复杂度 | 低       | 中等      |

## 监控和调试

### 1. 日志监控

- 订单创建成功/失败日志
- 消息发送成功/失败日志
- 库存操作日志
- 补偿操作日志

### 2. 调试接口

- `/simple/inventory/all` - 查看所有库存
- `/simple/inventory/records` - 查看库存操作记录
- `/simple/order/{orderId}` - 查看订单详情

### 3. 消息队列监控

- 队列消息数量
- 消息处理速度
- 失败消息统计

## 最佳实践

### 1. 消息幂等性

```java

@RabbitListener(queues = "order.created.queue")
public void handleOrderCreated(String messageBody, Message message) {
    String messageId = (String) message.getMessageProperties().getHeaders().get("messageId");

    // 检查消息是否已处理
    if (isMessageProcessed(messageId)) {
        return;
    }

    // 处理消息
    processMessage(messageBody);

    // 标记消息已处理
    markMessageProcessed(messageId);
}
```

### 2. 错误处理

```java

@RabbitListener(queues = "order.created.queue")
public void handleOrderCreated(String messageBody, Channel channel, long deliveryTag) {
    try {
        // 处理消息
        processMessage(messageBody);

        // 手动确认
        channel.basicAck(deliveryTag, false);
    } catch (Exception e) {
        // 处理失败，重新入队（可设置重试次数限制）
        channel.basicNack(deliveryTag, false, true);
    }
}
```

### 3. 补偿操作设计

- **可重复执行**：补偿操作要支持重复执行
- **向后兼容**：考虑数据状态变化
- **记录日志**：详细记录补偿过程

## 总结

这个简化版SAGA实现具有以下特点：

1. **极简设计**：只需要消息发送，无需复杂的状态管理
2. **低侵入性**：对现有业务代码改动最小
3. **事件驱动**：通过消息事件实现服务解耦
4. **自动补偿**：失败事件触发自动补偿
5. **易于扩展**：新服务只需订阅相应事件

适合大多数简单到中等复杂度的分布式事务场景，是一个实用的SAGA模式实现方案。
