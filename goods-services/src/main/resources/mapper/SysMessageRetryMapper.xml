<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.SysMessageRetryMapper">

    <!-- 创建消息重试表 -->
    <update id="createMessageRetryTable">
        CREATE TABLE IF NOT EXISTS `sys_message_retry`
        (
            `id`              BIGINT       NOT NULL AUTO_INCREMENT,
            `message_id`      VARCHAR(100) NOT NULL,
            `exchange_name`   VARCHAR(100) NOT NULL,
            `routing_key`     VARCHAR(100) NOT NULL,
            `message_body`    TEXT         NOT NULL,
            `retry_count`     INT          NOT NULL DEFAULT 0,
            `max_retry_count` INT          NOT NULL DEFAULT 3,
            `next_retry_time` TIMESTAMP    NULL,
            `status`          VARCHAR(20)  NOT NULL DEFAULT 'PENDING',
            `error_message`   TEXT,
            `last_retry_time` TIMESTAMP    NULL,
            `create_time`     TIMESTAMP             DEFAULT CURRENT_TIMESTAMP,
            `update_time`     TIMESTAMP             DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_message_id` (`message_id`),
            INDEX `idx_status_retry_time` (`status`, `next_retry_time`),
            INDEX `idx_create_time` (`create_time`)
        ) ENGINE = InnoDB
          DEFAULT CHARSET = utf8mb4 COMMENT ='消息重试表';
    </update>

    <!-- 查询需要重试的消息 -->
    <select id="selectRetryMessages" resultType="com.knet.goods.model.entity.SysMessageRetry">
        SELECT id,
               message_id      as messageId,
               exchange_name   as exchangeName,
               routing_key     as routingKey,
               message_body    as messageBody,
               retry_count     as retryCount,
               max_retry_count as maxRetryCount,
               next_retry_time as nextRetryTime,
               status,
               error_message   as errorMessage,
               last_retry_time as lastRetryTime,
               create_time     as createTime,
               update_time     as updateTime
        FROM sys_message_retry
        WHERE status = 'PENDING'
          AND next_retry_time &lt;= #{currentTime}
          AND retry_count &lt; max_retry_count
        ORDER BY next_retry_time ASC
        LIMIT #{limit}
    </select>

    <!-- 更新重试状态 -->
    <update id="updateRetryStatus">
        UPDATE sys_message_retry
        SET retry_count     = #{retryCount},
            next_retry_time = #{nextRetryTime},
            last_retry_time = #{lastRetryTime},
            status          = #{status},
            error_message   = #{errorMessage},
            update_time     = CURRENT_TIMESTAMP
        WHERE message_id = #{messageId}
    </update>
</mapper>
