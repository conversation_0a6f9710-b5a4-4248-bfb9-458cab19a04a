<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.SysSkuMapper">
    <select id="selectAllDistinctSkus" resultType="java.lang.String">
        SELECT sku_indexed,
               sku
        FROM sys_sku
        WHERE del_flag = 0
          AND sku_indexed IS NOT NULL
          AND sku_indexed != ''
        GROUP BY sku_indexed
    </select>

    <select id="selectAllDistinctSkuIndexedMapping" resultType="java.util.Map">
        SELECT sku_indexed,
               sku
        FROM sys_sku
        WHERE del_flag = 0
          AND sku_indexed IS NOT NULL
          AND sku_indexed != ''
          AND sku IS NOT NULL
          AND sku != ''
        GROUP BY sku_indexed, sku
    </select>

    <select id="selectAllDistinctRemarks" resultType="java.lang.String">
        SELECT remarks
        FROM knet_product
        WHERE del_flag = 0
          AND remarks IS NOT NULL
          AND remarks != ''
        GROUP BY remarks
    </select>
</mapper>
