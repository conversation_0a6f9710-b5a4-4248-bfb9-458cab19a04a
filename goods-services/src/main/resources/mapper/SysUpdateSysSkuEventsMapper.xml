<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.goods.mapper.SysUpdateSysSkuEventsMapper">
    <!--需要处理的sysSku变化事件-->
    <select id="findNeedToUpdateEvents" resultType="com.knet.goods.model.entity.SysUpdateSysSkuEvents">
        SELECT e.*
        FROM sys_update_sys_sku_events e
                 INNER JOIN(SELECT sku,
                                   MAX(create_time) AS latest_time
                            FROM sys_update_sys_sku_events
                            WHERE STATUS = 'PENDING'
                              AND create_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
                            GROUP BY sku) t
                           ON
                               e.sku = t.sku AND e.create_time = t.latest_time
        WHERE e.status = 'PENDING'
          AND e.create_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
        ORDER BY e.create_time DESC
        LIMIT #{total}
    </select>
</mapper>
