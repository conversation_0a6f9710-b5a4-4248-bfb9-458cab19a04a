package com.knet.goods.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.third.resp.KnetUserInfoDtoResp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025/6/23 18:00
 * @description: 用户服务
 */
@FeignClient(name = "user-services", path = "/userServices/api")
public interface ApiUserServiceProvider {

    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取用户信息", description = "供其他服务调用，获取用户详细信息")
    HttpResult<KnetUserInfoDtoResp> getUserById(@RequestParam("userId") Long userId);
}
