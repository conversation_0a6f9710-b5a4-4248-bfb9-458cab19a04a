package com.knet.goods.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 09:50
 * @description: SysUpdateSysSkuEvents服务接口
 */
public interface ISysUpdateSysSkuEventsService extends IService<SysUpdateSysSkuEvents> {
    /**
     * 查询需要更新sku的事件
     *
     * @param minutes 时间间隔（分钟）
     * @param total   最大返回条数
     * @return 需要更新sku的事件列表
     */
    List<SysUpdateSysSkuEvents> findNeedToUpdateEvents(Integer minutes, Integer total);

    /**
     * 成功-更新sku事件
     *
     * @param priceEvents sku事件
     */
    void successUpdatePriceTasks(List<SysUpdateSysSkuEvents> priceEvents);

    /**
     * 失败-更新sku事件
     *
     * @param priceEvents sku事件
     */
    void failUpdatePriceTasks(List<SysUpdateSysSkuEvents> priceEvents);
}
