package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.ProductMark;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.*;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/2/10 13:23
 * @description: Product service 服务接口
 */
public interface IKnetProductService extends IService<KnetProduct> {
    /**
     * 查询商品列表
     *
     * @param request 查询请求
     * @return 商品列表
     */
    IPage<ProductDtoResp> listProducts(ProductQueryRequest request);


    /**
     * 查询商品列表 - 按sku分组
     *
     * @param request 查询请求
     * @return 商品列表
     */
    IPage<ProductBySkuDtoResp> queryProductGroupBySku(ProductQueryRequest request);

    /**
     * 查询商品详情
     *
     * @param request 查询请求
     * @return 商品详情
     * @see com.knet.goods.model.dto.req.ProductDetailsQueryRequest
     */
    List<ProductSkuSpecPriceDtoResp> queryProductDetails(ProductDetailsQueryRequest request);

    /**
     * 从knet 创建商品
     *
     * @param request request
     * @return 商品
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest.ProductDto
     */
    KnetProduct createByKnet(CreateKnetProductRequest.ProductDto request);

    /**
     * 允许部分失败 批量插入 商品
     *
     * @param list 商品列表
     */
    void insertIgnoreBatch(List<KnetProduct> list);

    /**
     * 根据listingIds查询商品
     *
     * @param listingIds listingIds
     * @return 商品列表
     */
    List<String> queryByListingIds(List<String> listingIds);

    /**
     * 更新商品为下架状态
     *
     * @param products listingID 列表
     * @return 下架操作listingIds
     */
    List<String> updateKnetProductForOffSale(List<OffSaleKnetProductRequest.ProductDto> products);

    /**
     * 获取下架成功商品
     *
     * @param listingIds listingIds
     * @return 下架商品
     */
    List<OffSaleKnetProductResp.ProductDto> getOffSaleListingIds(List<String> listingIds);

    /**
     * 更新商品价格
     *
     * @param productDto 商品价格参数
     * @return 操作结果
     */
    OffSaleKnetProductResp.ProductDto processKnetProductPrice(UpdatePriceKnetProductRequest.ProductDto productDto);

    /**
     * 对外提供-查询商品列表
     *
     * @param request 查询请求
     * @return 商品列表
     */
    List<QueryKnetProductResp> queryKnetProductForApi(QueryKnetProductRequest request);

    /**
     * 处理 变更的商品信息
     *
     * @param updateEvents 变更事件列表
     */
    void updateKnetProductForSysSkuInfo(List<SysUpdateSysSkuEvents> updateEvents);

    /**
     * 根据listingIds获取商品列表
     *
     * @param listingIds 商品listing ID列表
     * @return 商品列表
     */
    List<KnetProduct> getKnetProductsByListingIds(List<String> listingIds);

    /**
     * 批量更新 商品标识
     *
     * @param skus        商品sku列表
     * @param productMark 商品标识
     */
    void setProductModifyMark(List<String> skus, ProductMark productMark);

    /**
     * 重置商品标识为普通商品
     *
     * @param productMark 商品标识
     */
    void resetProductMarkToCommon(ProductMark productMark);

    /**
     * 将已存在且状态为ON_SALE的oneId对应的商品设置为OFF_SALE
     * 确保相同的oneId在数据库中只能存在一条记录且status为ON_SALE
     *
     * @param oneIds 商品oneId列表
     * @return 更新的记录数
     */
    int updateExistingProductsToOffSale(List<String> oneIds);

    /**
     * 获取本地所有上架状态商品的oneId
     *
     * @return 本地上架状态商品的oneId集合
     */
    Set<String> getLocalOnSaleOneIds();

    /**
     * 锁定库存
     *
     * @param request 检查锁定库存请求体
     * @return 锁定结果
     */
    boolean lockInventory(CheckAndLockInvRequest request);

    /**
     * 检查库存
     *
     * @param request 检查锁定库存请求体
     * @return 锁定结果
     */
    boolean checkInventory(CheckAndLockInvRequest request);

    /**
     * 查询商品
     *
     * @param request 查询商品请求体
     * @return 返回查询结果
     */
    List<QueryKnetProductResp> queryKnetProduct(InnerKnetProductRequest request);
}
