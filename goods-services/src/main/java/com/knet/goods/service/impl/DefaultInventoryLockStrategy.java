package com.knet.goods.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.knet.common.base.HttpResult;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.service.PricingStrategyService;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.model.dto.req.CheckAndLockInvRequest;
import com.knet.goods.model.dto.third.resp.KnetUserInfoDtoResp;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.openfeign.ApiUserServiceProvider;
import com.knet.goods.service.IInventoryLockStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/12/19 15:40
 * @description: 默认库存锁定策略实现
 */
@Slf4j
@Service
public class DefaultInventoryLockStrategy implements IInventoryLockStrategy {

    @Resource
    private KnetProductMapper knetProductMapper;
    @Resource
    private ApiUserServiceProvider userServiceProvider;
    @Resource
    private PricingStrategyService pricingStrategyService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InventoryLockSuccessMessage lockInventoryWithDetails(CheckAndLockInvRequest request, String orderId, Long userId) {
        if (CollUtil.isEmpty(request.getItems())) {
            throw new ServiceException("锁定请求商品列表为空");
        }
        HttpResult<KnetUserInfoDtoResp> userInfo = userServiceProvider.getUserById(userId);
        KnetUserInfoDtoResp user = userInfo.getData();
        if (BeanUtil.isEmpty(user)) {
            log.error("创建订单的用户信息不存在 userId:{}", userId);
        }
        List<InventoryLockSuccessMessage.LockedProductInfo> lockedProducts = new ArrayList<>();
        for (SubOrderItemResp item : request.getItems()) {
            InventoryLockSuccessMessage.LockedProductInfo lockedProductInfo = lockSingleItem(item, user.getAccount());
            lockedProducts.add(lockedProductInfo);
        }
        return InventoryLockSuccessMessage.builder()
                .orderId(orderId)
                .userId(userId)
                .lockedProducts(lockedProducts)
                .build();
    }

    /**
     * 锁定单个商品项
     *
     * @param item    商品项
     * @param account 用户账号
     * @return 锁定的商品信息
     */
    private InventoryLockSuccessMessage.LockedProductInfo lockSingleItem(SubOrderItemResp item, String account) {
        // 将策略价格转换为原始价格进行库存匹配
        Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        Long originalPriceCents = PriceFormatUtil.formatYuanToCents(item.getPrice());
        log.info("库存锁定价格转换: SKU={}, 策略价格={}美分, 原始价格={}美分",
                item.getSku(), strategyPriceCents, originalPriceCents);
        // 查询可锁定的商品,排除用户自己的商品
        LambdaQueryWrapper<KnetProduct> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(KnetProduct::getSku, item.getSku())
                .eq(KnetProduct::getPrice, originalPriceCents)
                .eq(KnetProduct::getSpec, item.getSize())
                .eq(KnetProduct::getStatus, ProductStatus.ON_SALE)
                .ne(StrUtil.isNotBlank(account), KnetProduct::getSource, account)
                .orderByDesc(KnetProduct::getCreateTime)
                .last("LIMIT " + item.getCount());
        List<KnetProduct> productsToLock = knetProductMapper.selectList(queryWrapper);
        if (productsToLock.size() < item.getCount()) {
            log.error("商品: {} 尺码: {} 原始价格: {} 库存不足，需要{}个，实际只有{}个",
                    item.getSku(), item.getSize(), originalPriceCents, item.getCount(), productsToLock.size());
            throw new ServiceException("商品 " + item.getSku() + " 尺码 " + item.getSize() + " 库存不足");
        }
        List<Long> idsToLock = productsToLock.stream().map(KnetProduct::getId).toList();
        // 执行锁定操作
        LambdaUpdateWrapper<KnetProduct> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(KnetProduct::getId, idsToLock)
                .set(KnetProduct::getStatus, ProductStatus.LOCKED);
        int updatedCount = knetProductMapper.update(null, updateWrapper);
        if (updatedCount != idsToLock.size()) {
            log.error("商品锁定失败，期望锁定{}个，实际锁定{}个", idsToLock.size(), updatedCount);
            throw new ServiceException("商品锁定失败");
        }
        log.info("商品锁定成功 - SKU: {}, 尺码: {}, 原始价格: {}, 锁定数量: {}, 商品ID列表: {}",
                item.getSku(), item.getSize(), originalPriceCents, updatedCount, idsToLock);
        // 构建锁定成功的商品详情
        List<InventoryLockSuccessMessage.ProductDetail> productDetails = productsToLock
                .stream()
                .map(KnetProduct::createProductDetail)
                .toList();
        return InventoryLockSuccessMessage.LockedProductInfo
                .builder()
                .sku(item.getSku())
                .size(item.getSize())
                .price(item.getPrice())
                .productDetails(productDetails)
                .build();
    }

    @Override
    public String getStrategyName() {
        return "DEFAULT";
    }
}
