package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.service.ISkuCacheService;
import com.knet.goods.service.ISysSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.knet.common.constants.GoodsServicesConstants.*;

/**
 * SKU缓存服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@Service
public class SkuCacheServiceImpl implements ISkuCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private ISysSkuService sysSkuService;

    @Override
    public void initSkuCache() {
        try {
            RedisCacheUtil.hmdel(SKU_CACHE_KEY);
            // 从数据库加载所有SKU_INDEXED和SKU的映射数据
            Map<String, Map<String, Object>> skuIndexedMapping = sysSkuService.selectAllDistinctSkuIndexedMapping();
            if (MapUtil.isNotEmpty(skuIndexedMapping)) {
                // 只构建一个缓存：键为sku_indexed（已经是大写且去空格），值为sku
                Map<String, Object> skuMap = new HashMap<>();
                skuIndexedMapping.forEach((skuIndexed, value) -> {
                    String sku = (String) value.get("sku");
                    skuMap.put(skuIndexed, sku);
                });
                // 批量写入Redis Hash
                RedisCacheUtil.hmset(SKU_CACHE_KEY, skuMap, SKU_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条SKU数据到缓存", skuMap.size());
            }
        } catch (Exception e) {
            log.error("初始化SKU缓存失败", e);
            throw new RuntimeException("初始化SKU缓存失败", e);
        }
    }

    @Override
    public void refreshSkuCache() {
        log.info("开始刷新SKU缓存...");
        initSkuCache();
        log.info("SKU缓存刷新完成");
    }

    @Override
    public Set<String> matchSkus(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        //清洗数据 去掉空格，去掉-符号，转换为大写用于Redis pattern匹配
        final String formattedKeyword = keyword.replaceAll(" ", "").replaceAll("-", "").toUpperCase();
        try {
            // 检查缓存是否存在
            if (!RedisCacheUtil.hasKey(SKU_CACHE_KEY)) {
                log.warn("SKU缓存为空，尝试重新初始化缓存");
                initSkuCache();
                if (!RedisCacheUtil.hasKey(SKU_CACHE_KEY)) {
                    log.error("初始化SKU缓存失败，返回空结果");
                    return new HashSet<>();
                }
            }
            // 使用线程安全的Set来收集匹配结果，设置最大容量为1000
            Set<String> matchedSkus = Collections.synchronizedSet(new LinkedHashSet<>());
            final int maxResults = 1000;
            // 构建Redis模式匹配字符串，使用大写进行精确匹配
            String pattern = "*" + formattedKeyword + "*";
            // 使用hscanWithPattern直接在Redis层面进行模式匹配，每批1000条
            RedisCacheUtil.hscanWithPattern(SKU_CACHE_KEY, pattern, 1000, batch -> {
                // 如果已经找到足够的结果，则停止处理
                if (matchedSkus.size() >= maxResults) {
                    return;
                }
                // 获取匹配的key（sku_indexed的大写）而不是值
                Set<String> batchMatched = batch.keySet().parallelStream()
                        .map(Object::toString)
                        .filter(StrUtil::isNotBlank)
                        .limit(maxResults - matchedSkus.size()) // 限制结果数量
                        .sorted(String.CASE_INSENSITIVE_ORDER)
                        .collect(Collectors.toCollection(LinkedHashSet::new));

                // 将匹配结果添加到总结果集中
                synchronized (matchedSkus) {
                    if (matchedSkus.size() < maxResults) {
                        int remainingSpace = maxResults - matchedSkus.size();
                        batchMatched.stream()
                                .limit(remainingSpace)
                                .forEach(matchedSkus::add);
                    }
                }
            });
            log.debug("关键词'{}'匹配到{}个SKU", keyword, matchedSkus.size());
            return matchedSkus;
        } catch (Exception e) {
            log.error("SKU模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }

    @Override
    public void addSkuToCache(String sku, String skuIndexed) {
        if (StrUtil.isBlank(sku) || StrUtil.isBlank(skuIndexed)) {
            log.warn("SKU或SKU检索索引为空，无法添加到缓存: sku={}, skuIndexed={}", sku, skuIndexed);
            return;
        }
        try {
            // 直接使用skuIndexed作为键（已经是大写且去空格），sku作为值
            redisTemplate.opsForHash().put(SKU_CACHE_KEY, skuIndexed, sku);
            log.debug("成功添加SKU到缓存: skuIndexed={}, sku={}", skuIndexed, sku);
        } catch (Exception e) {
            log.error("添加SKU到缓存失败: skuIndexed={}, sku={}", skuIndexed, sku, e);
        }
    }

    @Override
    public void removeSkuFromCache(String sku) {
        if (StrUtil.isBlank(sku)) {
            log.warn("SKU为空，无法从缓存中删除");
            return;
        }
        try {
            // 需要根据sku找到对应的skuIndexed，然后删除
            // 这里简化处理，遍历所有缓存找到匹配的key
            Map<Object, Object> allEntries = redisTemplate.opsForHash().entries(SKU_CACHE_KEY);
            for (Map.Entry<Object, Object> entry : allEntries.entrySet()) {
                if (sku.equals(entry.getValue())) {
                    redisTemplate.opsForHash().delete(SKU_CACHE_KEY, entry.getKey());
                    log.debug("成功从缓存中删除SKU: {}", sku);
                    break;
                }
            }
        } catch (Exception e) {
            log.error("从缓存中删除SKU失败: {}", sku, e);
        }
    }

    @Override
    public void initKnetProductRemarkCache() {
        try {
            RedisCacheUtil.hmdel(KNET_PRODUCT_REMARK_CACHE_KEY);
            // 从数据库加载所有条knet_product和remark数据
            List<String> remarks = sysSkuService.selectAllDistinctRemarks();
            if (CollUtil.isNotEmpty(remarks)) {
                // 批量写入Redis Hash，键转换为大写用于匹配，值保持原始大小写
                Map<String, Object> remarksMap = remarks.stream()
                        .collect(Collectors.toMap(
                                String::toUpperCase,
                                remark -> remark,
                                (existing, replacement) -> existing
                        ));
                RedisCacheUtil.hmset(KNET_PRODUCT_REMARK_CACHE_KEY, remarksMap, KNET_PRODUCT_REMARK_CACHE_KEY_EXPIRED_TIME);
                log.info("成功加载{}条knet_product remark数据到缓存", remarksMap.size());
            }
        } catch (Exception e) {
            log.error("初始化条knet_product remark缓存失败", e);
            throw new RuntimeException("初始化条knet_product remark缓存失败", e);
        }
    }

    @Override
    public void refreshProductRemarkCache() {
        log.info("开始刷新knet_product remark缓存...");
        initKnetProductRemarkCache();
        log.info("knet_product remark缓存刷新完成");
    }

    @Override
    public Set<String> matchProductsByRemark(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return new HashSet<>();
        }
        //清洗数据 去掉空格，转换为大写用于Redis pattern匹配
        final String formattedKeyword = keyword.replaceAll(" ", "").toUpperCase();
        try {
            // 检查缓存是否存在
            if (!RedisCacheUtil.hasKey(KNET_PRODUCT_REMARK_CACHE_KEY)) {
                log.warn("Knet Product remark缓存为空，尝试重新初始化缓存");
                initKnetProductRemarkCache();
                if (!RedisCacheUtil.hasKey(KNET_PRODUCT_REMARK_CACHE_KEY)) {
                    log.error("初始化knet product remark缓存失败，返回空结果");
                    return new HashSet<>();
                }
            }
            // 使用线程安全的Set来收集匹配结果，设置最大容量为1000
            Set<String> matchedRemarks = Collections.synchronizedSet(new LinkedHashSet<>());
            final int maxResults = 1000;
            // 构建Redis模式匹配字符串，使用大写进行精确匹配
            String pattern = "*" + formattedKeyword + "*";
            // 使用hscanWithPattern直接在Redis层面进行模式匹配，每批1000条
            RedisCacheUtil.hscanWithPattern(KNET_PRODUCT_REMARK_CACHE_KEY, pattern, 1000, batch -> {
                // 如果已经找到足够的结果，则停止处理
                if (matchedRemarks.size() >= maxResults) {
                    return;
                }
                // 获取匹配的remark值（原始大小写）
                Set<String> batchMatched = batch.values().parallelStream()
                        .map(Object::toString)
                        .filter(StrUtil::isNotBlank)
                        .limit(maxResults - matchedRemarks.size()) // 限制结果数量
                        .sorted(String.CASE_INSENSITIVE_ORDER)
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                // 将匹配结果添加到总结果集中
                synchronized (matchedRemarks) {
                    if (matchedRemarks.size() < maxResults) {
                        int remainingSpace = maxResults - matchedRemarks.size();
                        batchMatched.stream()
                                .limit(remainingSpace)
                                .forEach(matchedRemarks::add);
                    }
                }
            });
            log.debug("关键词'{}'在remark中匹配到{}个", keyword, matchedRemarks.size());
            return matchedRemarks;
        } catch (Exception e) {
            log.error("knet product remark模糊匹配失败，关键词: {}", keyword, e);
            return new HashSet<>();
        }
    }
}
