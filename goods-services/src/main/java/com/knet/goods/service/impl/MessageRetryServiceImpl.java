package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.goods.mapper.SysMessageRetryMapper;
import com.knet.goods.model.entity.SysMessageRetry;
import com.knet.goods.service.IMessageRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;

import static com.knet.common.constants.GoodsServicesConstants.MAX_RETRY_COUNT;
import static com.knet.common.constants.GoodsServicesConstants.RETRY_DELAY_MINUTES;

/**
 * <AUTHOR>
 * @date 2025/7/9 17:00
 * @description: 消息重试服务实现
 */
@Slf4j
@Service
public class MessageRetryServiceImpl implements IMessageRetryService {
    @Resource
    private SysMessageRetryMapper messageRetryMapper;
    @Resource
    private RabbitTemplate rabbitTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFailedMessage(String messageId, String exchangeName, String routingKey,
                                  String messageBody, String errorMessage) {
        try {
            SysMessageRetry retryMessage = SysMessageRetry.builder()
                    .messageId(messageId)
                    .exchangeName(exchangeName)
                    .routingKey(routingKey)
                    .messageBody(messageBody)
                    .retryCount(0)
                    .maxRetryCount(MAX_RETRY_COUNT)
                    .nextRetryTime(LocalDateTime.now().plusMinutes(RETRY_DELAY_MINUTES))
                    .status("PENDING")
                    .errorMessage(errorMessage)
                    .build();
            messageRetryMapper.insert(retryMessage);
            log.info("保存失败消息到重试表: messageId={}, exchangeName={}, routingKey={}",
                    messageId, exchangeName, routingKey);
        } catch (Exception e) {
            log.error("保存失败消息到重试表失败: messageId={}, error={}", messageId, e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processMessageRetry() {
        try {
            // 查询需要重试的消息
            List<SysMessageRetry> retryMessages = messageRetryMapper.selectRetryMessages(LocalDateTime.now(), 100);
            if (CollUtil.isEmpty(retryMessages)) {
                return;
            }
            log.info("开始处理消息重试，待重试消息数量: {}", retryMessages.size());
            for (SysMessageRetry retryMessage : retryMessages) {
                try {
                    boolean success = retryMessage(retryMessage);
                    updateRetryStatus(retryMessage.getMessageId(), success,
                            success ? null : "重试发送失败");
                } catch (Exception e) {
                    log.error("重试消息失败: messageId={}, error={}", retryMessage.getMessageId(), e.getMessage(), e);
                    updateRetryStatus(retryMessage.getMessageId(), false, e.getMessage());
                }
            }
            log.info("消息重试处理完成");
        } catch (Exception e) {
            log.error("处理消息重试异常", e);
        }
    }

    @Override
    public boolean retryMessage(SysMessageRetry retryMessage) {
        try {
            log.info("重试发送消息: messageId={}, retryCount={}",
                    retryMessage.getMessageId(), retryMessage.getRetryCount());
            MessageProperties properties = new MessageProperties();
            properties.setHeader("routingKey", retryMessage.getRoutingKey());
            properties.setHeader("messageId", retryMessage.getMessageId());
            properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            Message message = new Message(retryMessage.getMessageBody().getBytes(StandardCharsets.UTF_8), properties);
            CorrelationData correlationData = new CorrelationData(retryMessage.getMessageId());
            rabbitTemplate.convertAndSend(
                    retryMessage.getExchangeName(),
                    retryMessage.getRoutingKey(),
                    message,
                    correlationData
            );
            // 等待确认结果
            try {
                CorrelationData.Confirm confirm = correlationData.getFuture().get();
                if (confirm != null && confirm.isAck()) {
                    log.info("消息重试发送成功: messageId={}", retryMessage.getMessageId());
                    return true;
                } else {
                    log.warn("消息重试发送失败，未收到确认: messageId={}", retryMessage.getMessageId());
                    return false;
                }
            } catch (Exception e) {
                log.error("等待消息确认异常: messageId={}, error={}", retryMessage.getMessageId(), e.getMessage());
                return false;
            }
        } catch (Exception e) {
            log.error("重试发送消息异常: messageId={}, error={}", retryMessage.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRetryStatus(String messageId, boolean success, String errorMessage) {
        try {
            // 先查询当前状态
            LambdaQueryWrapper<SysMessageRetry> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysMessageRetry::getMessageId, messageId);
            SysMessageRetry retryMessage = messageRetryMapper.selectOne(queryWrapper);
            if (retryMessage == null) {
                log.warn("未找到重试消息记录: messageId={}", messageId);
                return;
            }
            int newRetryCount = retryMessage.getRetryCount() + 1;
            String newStatus;
            LocalDateTime nextRetryTime = null;
            if (success) {
                newStatus = "SUCCESS";
            } else if (newRetryCount >= retryMessage.getMaxRetryCount()) {
                newStatus = "FAILED";
            } else {
                newStatus = "PENDING";
                // 递增延迟
                nextRetryTime = LocalDateTime.now().plusMinutes((long) RETRY_DELAY_MINUTES * newRetryCount);
            }
            int updated = messageRetryMapper.updateRetryStatus(
                    messageId, newRetryCount, nextRetryTime, LocalDateTime.now(), newStatus, errorMessage);
            log.info("更新消息重试状态: messageId={}, retryCount={}, status={}, updated={}",
                    messageId, newRetryCount, newStatus, updated);
        } catch (Exception e) {
            log.error("更新消息重试状态失败: messageId={}, error={}", messageId, e.getMessage(), e);
        }
    }
}
