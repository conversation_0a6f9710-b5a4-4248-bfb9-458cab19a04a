package com.knet.goods.service;

import com.knet.common.dto.message.PriceChangeMessage;
import com.knet.goods.model.dto.req.PriceQueryRequest;
import com.knet.goods.model.dto.resp.PriceAggregationResp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:35
 * @description: 价格聚合服务接口
 */
public interface IPriceAggregationService {

    /**
     * 处理价格变动事件
     *
     * @param event 价格变动事件
     */
    void handlePriceChangeEvent(PriceChangeMessage event);

    /**
     * 查询单个SKU的价格聚合信息
     *
     * @param sku  商品SKU
     * @param spec 商品规格（可选）
     * @return 价格聚合信息
     */
    PriceAggregationResp queryPrice(String sku, String spec);

    /**
     * 查询单个SKU的价格聚合信息列表
     *
     * @param sku  商品SKU
     * @param spec 商品规格（可选）
     * @return 价格聚合信息列表
     */
    List<PriceAggregationResp> queryPriceAggregation(String sku, String spec);

    /**
     * 批量查询价格聚合信息
     *
     * @param products 商品查询列表
     * @return 价格聚合信息列表
     */
    List<PriceAggregationResp> batchQueryPrice(List<PriceQueryRequest.ProductQuery> products);


    /**
     * 刷新价格聚合缓存
     *
     * @param sku  商品SKU
     * @param spec 商品规格
     */
    void refreshPriceCache(String sku, String spec);

    /**
     * 删除价格聚合缓存
     *
     * @param sku  商品SKU
     * @param spec 商品规格
     */
    void deletePriceCache(String sku, String spec);

    /**
     * 重新计算并更新价格聚合数据
     *
     * @param sku  商品SKU
     * @param spec 商品规格
     */
    void recalculateAndUpdatePriceAggregation(String sku, String spec);

    /**
     * 删除价格聚合数据
     *
     * @param sku  商品SKU
     * @param spec 商品规格
     * @return 是否删除成功
     */
    boolean deletePriceAggregation(String sku, String spec);

    /**
     * 清理过期的价格聚合数据
     * 删除没有对应在售商品的价格聚合记录
     *
     * @return 清理的记录数
     */
    int cleanupExpiredPriceAggregation();

    /**
     * 初始化价格聚合表
     * 从knet_product表中获取所有在售商品数据，按sku+spec分组计算最高最低价格
     *
     * @return 初始化的记录数
     */
    int initializePriceAggregation();

    /**
     * 批量初始化价格聚合数据
     * 分批处理大量数据，避免内存溢出
     *
     * @param batchSize 批次大小
     * @return 初始化的记录数
     */
    int batchInitializePriceAggregation(int batchSize);
}
