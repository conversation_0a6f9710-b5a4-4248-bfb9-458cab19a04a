package com.knet.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.dto.message.PriceChangeMessage;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.CreateKnetProductResp;
import com.knet.goods.model.dto.resp.OffSaleKnetProductResp;
import com.knet.goods.model.dto.resp.PriceAggregationResp;
import com.knet.goods.model.dto.resp.QueryKnetProductResp;
import com.knet.goods.model.entity.KnetProduct;
import com.knet.goods.service.IApiGoodsService;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.IPriceAggregationService;
import com.knet.goods.system.event.ProductPriceChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2025/2/25 16:38
 * @description:
 */
@Slf4j
@Service
public class ApiGoodsServiceImpl implements IApiGoodsService {
    @Resource
    private IKnetProductService iKnetProductService;
    @Resource
    @Qualifier("goodsThreadPoolExecutor")
    private ThreadPoolExecutor goodsThreadPoolExecutor;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private IPriceAggregationService priceAggregationService;

    @DistributedLock(key = "'createProducts:' + #request.hashCode()", expire = 2)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateKnetProductResp createProducts(CreateKnetProductRequest request) {
        CreateKnetProductResp result = new CreateKnetProductResp();
        try {
            log.info("开始创建商品，请求参数: {}", request);
            List<String> requestOneIds = request.getProducts().stream()
                    .map(CreateKnetProductRequest.ProductDto::getOneId)
                    .filter(oneId -> oneId != null && !oneId.isEmpty())
                    .distinct()
                    .toList();
            if (!requestOneIds.isEmpty()) {
                log.info("将已存在的oneId对应商品设置为下架状态，oneIds: {}", requestOneIds);
                iKnetProductService.updateExistingProductsToOffSale(requestOneIds);
            }
            List<KnetProduct> saveProducts = request.getProducts().stream()
                    .map(productDto -> iKnetProductService.createByKnet(productDto))
                    .toList();
            // 记录新生成的listingIds，用于后续查询
            List<String> preListingIds = saveProducts.stream()
                    .map(KnetProduct::getListingId)
                    .toList();
            log.info("批量插入新商品，数量: {}", saveProducts.size());
            iKnetProductService.insertIgnoreBatch(saveProducts);
            List<String> insertOneIds = iKnetProductService.queryByListingIds(preListingIds);
            log.info("成功插入的商品oneIds: {}", insertOneIds);
            // 筛选出已经插入成功的商品，构建响应
            List<CreateKnetProductResp.ProductDto> createdProducts = saveProducts.stream()
                    .filter(productDto -> insertOneIds.contains(productDto.getOneId()))
                    .map(KnetProduct::mapRespProductDto)
                    .toList();
            result.setProducts(createdProducts);
            log.info("创建商品完成，成功创建数量: {}", createdProducts.size());
            // 发送价格变动事件
            sendPriceChangeEvent(saveProducts, PriceChangeMessage.EventType.CREATE);
            return result;
        } catch (Exception e) {
            log.error("创建商品失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @DistributedLock(key = "'offSale:' + #request.hashCode()", expire = 2)
    public OffSaleKnetProductResp offSale(OffSaleKnetProductRequest request) {
        List<String> listingIds = iKnetProductService.updateKnetProductForOffSale(request.getProducts());
        List<OffSaleKnetProductResp.ProductDto> products = iKnetProductService.getOffSaleListingIds(listingIds);
        // 发送价格变动事件（下架商品）
        List<KnetProduct> offSaleProducts = iKnetProductService.getKnetProductsByListingIds(listingIds);
        sendPriceChangeEvent(offSaleProducts, PriceChangeMessage.EventType.OFF_SALE);
        return new OffSaleKnetProductResp(products);
    }

    @Override
    @DistributedLock(key = "'updatePrice:' + #request.hashCode()", expire = 2)
    public OffSaleKnetProductResp updatePrice(UpdatePriceKnetProductRequest request) {
        List<CompletableFuture<OffSaleKnetProductResp.ProductDto>> completableFutures = request.getProducts().parallelStream()
                .map(productDto ->
                        CompletableFuture.supplyAsync(
                                () -> iKnetProductService.processKnetProductPrice(productDto), goodsThreadPoolExecutor)
                )
                .toList();
        List<OffSaleKnetProductResp.ProductDto> results = completableFutures.stream()
                .map(CompletableFuture::join)
                .toList();
        // 发送价格变动事件（价格更新）
        List<String> listingIds = request.getProducts().stream()
                .map(UpdatePriceKnetProductRequest.ProductDto::getListingId)
                .toList();
        List<KnetProduct> updatedProducts = iKnetProductService.getKnetProductsByListingIds(listingIds);
        sendPriceChangeEvent(updatedProducts, PriceChangeMessage.EventType.UPDATE_PRICE);
        return new OffSaleKnetProductResp(results);
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProduct(QueryKnetProductRequest request) {
        return iKnetProductService.queryKnetProductForApi(request);
    }

    @Override
    public List<QueryKnetProductResp> queryKnetProduct(InnerKnetProductRequest request) {
        return iKnetProductService.queryKnetProduct(request);
    }

    @Override
    public List<PriceAggregationResp> batchQueryPrice(List<PriceQueryRequest.ProductQuery> products) {
        return priceAggregationService.batchQueryPrice(products);
    }

    /**
     * 发送价格变动事件
     *
     * @param products  商品列表
     * @param eventType 事件类型
     */
    private void sendPriceChangeEvent(List<KnetProduct> products, PriceChangeMessage.EventType eventType) {
        if (CollUtil.isEmpty(products)) {
            return;
        }
        List<PriceChangeMessage.ProductPriceInfo> productPriceInfos = products.stream()
                .map(product -> PriceChangeMessage.ProductPriceInfo.builder()
                        .sku(product.getSku())
                        .spec(product.getSpec())
                        .price(product.getPrice())
                        .status(product.getStatus().name())
                        .build())
                .toList();
        PriceChangeMessage message = PriceChangeMessage.builder()
                .eventType(eventType)
                .operator("API_SYSTEM")
                .eventTime(LocalDateTime.now())
                .products(productPriceInfos)
                .build();
        ProductPriceChangeEvent event = new ProductPriceChangeEvent(this, message);
        // 异步发送消息，避免影响主业务流程
        CompletableFuture.runAsync(() -> {
            try {
                applicationEventPublisher.publishEvent(event);
            } catch (Exception e) {
                log.error("发送价格变动事件失败: eventType={}, products={}, error={}",
                        eventType, products.size(), e.getMessage(), e);
            }
        }, goodsThreadPoolExecutor);
    }
}
