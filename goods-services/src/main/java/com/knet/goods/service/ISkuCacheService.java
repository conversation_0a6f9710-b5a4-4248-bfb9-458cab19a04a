package com.knet.goods.service;

import java.util.Set;

/**
 * SKU缓存服务接口
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface ISkuCacheService {

    /**
     * 初始化SKU缓存
     * 从数据库加载所有SKU数据到Redis Hash结构中
     */
    void initSkuCache();

    /**
     * 刷新SKU缓存
     * 重新加载数据库中的SKU数据到缓存
     */
    void refreshSkuCache();

    /**
     * 根据关键词模糊匹配SKU
     *
     * @param keyword 匹配关键词
     * @return 匹配的SKU集合
     */
    Set<String> matchSkus(String keyword);

    /**
     * 添加单个SKU到缓存
     *
     * @param sku        SKU编码
     * @param skuIndexed SKU检索索引
     */
    void addSkuToCache(String sku, String skuIndexed);

    /**
     * 从缓存中删除SKU
     *
     * @param sku SKU编码
     */
    void removeSkuFromCache(String sku);

    /**
     * 初始化SKU remark缓存
     * 从数据库加载所有SKU的remark数据到Redis Hash结构中
     */
    void initKnetProductRemarkCache();

    /**
     * 刷新SKU remark缓存
     * 重新加载数据库中的SKU remark数据到缓存
     */
    void refreshProductRemarkCache();

    /**
     * 根据关键词模糊匹配SKU remark
     *
     * @param keyword 匹配关键词
     * @return 匹配的SKU集合
     */
    Set<String> matchProductsByRemark(String keyword);
}
