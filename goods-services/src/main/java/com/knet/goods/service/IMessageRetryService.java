package com.knet.goods.service;

import com.knet.goods.model.entity.SysMessageRetry;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:40
 * @description: 消息重试服务接口
 */
public interface IMessageRetryService {

    /**
     * 保存失败消息到重试表
     *
     * @param messageId    消息ID
     * @param exchangeName 交换机名称
     * @param routingKey   路由键
     * @param messageBody  消息内容
     * @param errorMessage 错误信息
     */
    void saveFailedMessage(String messageId, String exchangeName, String routingKey, 
                          String messageBody, String errorMessage);

    /**
     * 处理消息重试
     */
    void processMessageRetry();

    /**
     * 重试单个消息
     *
     * @param retryMessage 重试消息
     * @return 是否重试成功
     */
    boolean retryMessage(SysMessageRetry retryMessage);

    /**
     * 更新消息重试状态
     *
     * @param messageId    消息ID
     * @param success      是否成功
     * @param errorMessage 错误信息
     */
    void updateRetryStatus(String messageId, boolean success, String errorMessage);
}
