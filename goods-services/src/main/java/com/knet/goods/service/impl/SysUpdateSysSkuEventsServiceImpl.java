package com.knet.goods.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.SysTaskStatus;
import com.knet.goods.mapper.SysUpdateSysSkuEventsMapper;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;
import com.knet.goods.service.ISysUpdateSysSkuEventsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 09:51
 * @description: SysUpdateSysSkuEvents服务实现类
 */
@Slf4j
@Service
public class SysUpdateSysSkuEventsServiceImpl extends ServiceImpl<SysUpdateSysSkuEventsMapper, SysUpdateSysSkuEvents> implements ISysUpdateSysSkuEventsService {
    @Override
    public List<SysUpdateSysSkuEvents> findNeedToUpdateEvents(Integer minutes, Integer total) {
        return baseMapper.findNeedToUpdateEvents(minutes, total);
    }

    @Override
    public void successUpdatePriceTasks(List<SysUpdateSysSkuEvents> priceEvents) {
        LambdaUpdateWrapper<SysUpdateSysSkuEvents> successWrapper = Wrappers.lambdaUpdate();
        successWrapper
                .in(SysUpdateSysSkuEvents::getId, priceEvents.stream().map(SysUpdateSysSkuEvents::getId).toArray())
                .set(SysUpdateSysSkuEvents::getStatus, SysTaskStatus.SUCCESS);
        this.update(successWrapper);
    }

    @Override
    public void failUpdatePriceTasks(List<SysUpdateSysSkuEvents> priceEvents) {
        LambdaUpdateWrapper<SysUpdateSysSkuEvents> failWrapper = Wrappers.lambdaUpdate();
        failWrapper
                .in(SysUpdateSysSkuEvents::getId, priceEvents.stream().map(SysUpdateSysSkuEvents::getId).toArray())
                .set(SysUpdateSysSkuEvents::getStatus, SysTaskStatus.FAILURE);
        this.update(failWrapper);
    }
}
