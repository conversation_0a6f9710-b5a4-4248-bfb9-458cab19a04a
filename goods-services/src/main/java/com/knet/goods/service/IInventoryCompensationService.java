package com.knet.goods.service;

/**
 * <AUTHOR>
 * @date 2025/12/19 10:20
 * @description: 库存补偿服务接口
 */
public interface IInventoryCompensationService {

    /**
     * 处理订单超时补偿
     * 当订单超时时，释放已锁定的库存
     *
     * @param messageBody 订单超时消息体
     */
    void processOrderTimeout(String messageBody);


    /**
     * 处理订单退款补偿
     * 订单退款，释放已经锁定的库存
     *
     * @param messageBody 订单退款消息体
     */
    void processOrderRefund(String messageBody);
}
