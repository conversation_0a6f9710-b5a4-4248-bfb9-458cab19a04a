package com.knet.goods.service;

/**
 * <AUTHOR>
 * @date 2025/6/10 15:04
 * @description: 库存接口定义
 */
public interface IInventoryService {
    /**
     * 检查并锁定库存
     *
     * @param prentOrderId prentOrderId
     * @return 锁定结果
     */
    boolean checkAndLockInventory(String prentOrderId);

    /**
     * 检查并锁定库存（带用户ID）
     *
     * @param parentOrderId 父订单ID
     * @param userId 用户ID
     * @return 锁定结果
     */
    boolean checkAndLockInventoryWithUserId(String parentOrderId, Long userId);
}
