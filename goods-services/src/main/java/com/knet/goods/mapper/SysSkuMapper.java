package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.entity.SysSku;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * sku Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Mapper
public interface SysSkuMapper extends BaseMapper<SysSku> {

    /**
     * 查询所有不重复的sku_indexed
     *
     * @return 所有不重复的sku_indexed列表
     */
    List<String> selectAllDistinctSkus();

    /**
     * 查询所有不重复的sku_indexed和对应的sku映射
     *
     * @return sku_indexed和sku的映射Map，key为sku_indexed，value为包含sku信息的Map
     */
    @MapKey("sku_indexed")
    Map<String, Map<String, Object>> selectAllDistinctSkuIndexedMapping();

    /**
     * 查询所有不重复的sku_indexed
     *
     * @return 所有不重复的sku_indexed列表
     */
    List<String> selectAllDistinctRemarks();
}
