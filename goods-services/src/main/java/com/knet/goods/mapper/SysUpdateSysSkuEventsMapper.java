package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:10
 * @description: sysSku变化事件表 mapper
 */
@Mapper
public interface SysUpdateSysSkuEventsMapper extends BaseMapper<SysUpdateSysSkuEvents> {
    /**
     * 查询需要更新sku的事件
     *
     * @param minutes 时间间隔（分钟）
     * @param total   最大返回条数
     * @return 需要更新sku的事件列表
     */
    List<SysUpdateSysSkuEvents> findNeedToUpdateEvents(@Param("minutes") Integer minutes, @Param("total") Integer total);
}
