package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.entity.SysMessageRetry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:30
 * @description: 消息重试Mapper
 */
@Mapper
public interface SysMessageRetryMapper extends BaseMapper<SysMessageRetry> {

    /**
     * 创建消息重试表
     */
    void createMessageRetryTable();

    /**
     * 查询需要重试的消息
     *
     * @param currentTime 当前时间
     * @param limit       限制数量
     * @return 待重试消息列表
     */
    List<SysMessageRetry> selectRetryMessages(@Param("currentTime") LocalDateTime currentTime, @Param("limit") int limit);

    /**
     * 更新重试状态
     *
     * @param messageId      消息ID
     * @param retryCount     重试次数
     * @param nextRetryTime  下次重试时间
     * @param lastRetryTime  最后重试时间
     * @param status         状态
     * @param errorMessage   错误信息
     * @return 影响行数
     */
    int updateRetryStatus(@Param("messageId") String messageId,
                          @Param("retryCount") Integer retryCount,
                          @Param("nextRetryTime") LocalDateTime nextRetryTime,
                          @Param("lastRetryTime") LocalDateTime lastRetryTime,
                          @Param("status") String status,
                          @Param("errorMessage") String errorMessage);
}
