package com.knet.goods.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.InventoryFailedMessage;
import com.knet.common.dto.message.OrderMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.service.IInventoryCompensationService;
import com.knet.goods.service.IInventoryService;
import com.knet.goods.system.event.InventoryFailedEvent;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.OrderServicesConstants.ORDER_CREATED;
import static com.knet.common.constants.OrderServicesConstants.ORDER_REFUND;
import static com.knet.common.constants.SystemConstant.GOODS_ORDER_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/6/6 15:14
 * @description: 订单消息消费者
 */
@Slf4j
@Component
public class OrderMessageConsumer {

    @Resource
    private IInventoryService iInventoryService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private IInventoryCompensationService inventoryCompensationService;

    @RabbitListener(
            queues = "order-queue.goods-services",
            ackMode = "MANUAL" // 必须显式指定
    )
    public void handleOrderNotification(
            @Payload String messageBody,
            @Header("routingKey") String orderType,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            if (ORDER_CREATED.equals(orderType)) {
                if (!RedisCacheUtil.setIfAbsent(GOODS_ORDER_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("goods服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                // 业务处理
                processOrder(messageBody);
                channel.basicAck(deliveryTag, false);
            }
            if (ORDER_REFUND.equals(orderType)) {
                if (!RedisCacheUtil.setIfAbsent(GOODS_ORDER_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                    log.warn("goods服务重复消息: {}", messageId);
                    channel.basicAck(deliveryTag, false);
                    return;
                }
                // 业务处理
                inventoryCompensationService.processOrderRefund(messageBody);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("goods服务 订单处理失败: {}", messageBody, e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ex) {
                log.error("goods服务 消息拒绝失败: {}", ex.getMessage());
            }
        }
    }

    /**
     * 库存检查锁定
     *
     * @param messageBody 消息体
     */
    private void processOrder(String messageBody) {
        OrderMessage orderMessage = JSON.parseObject(messageBody, OrderMessage.class);
        log.info("库存服务处理订单创建消息: {}", messageBody);
        try {
            boolean inventory = iInventoryService.checkAndLockInventoryWithUserId(orderMessage.getOrderId(), orderMessage.getUserId());
            if (!inventory) {
                sendInventoryFailedEvent(orderMessage, "库存不足，锁定失败");
                log.error("库存不足，锁定失败");
            }
            log.info("库存锁定成功: orderId={}", orderMessage.getOrderId());
        } catch (Exception e) {
            log.error("库存锁定失败: orderId={}, error={}", orderMessage.getOrderId(), e.getMessage());
            sendInventoryFailedEvent(orderMessage, e.getMessage());
        }
    }

    /**
     * 发送库存扣减失败补偿事件 通知支付、订单服务做补偿操作
     *
     * @param orderMessage  订单消息
     * @param failureReason 失败原因
     */
    private void sendInventoryFailedEvent(OrderMessage orderMessage, String failureReason) {
        try {
            InventoryFailedMessage message = InventoryFailedMessage.create(orderMessage.getOrderId(), orderMessage.getUserId(), failureReason);
            InventoryFailedEvent event = new InventoryFailedEvent(this, message);
            applicationEventPublisher.publishEvent(event);
            log.info("库存扣减失败补偿事件发送成功: orderId={}", orderMessage.getOrderId());
        } catch (Exception e) {
            log.error("发送库存扣减失败补偿事件异常: orderId={}, error={}", orderMessage.getOrderId(), e.getMessage());
        }
    }
}
