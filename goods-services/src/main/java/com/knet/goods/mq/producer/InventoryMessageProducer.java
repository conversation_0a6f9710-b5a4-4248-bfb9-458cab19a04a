package com.knet.goods.mq.producer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.InventoryFailedMessage;
import com.knet.common.dto.message.InventoryLockSuccessMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:05
 * @description: 库存消息生产者
 */
@Slf4j
@Component
public class InventoryMessageProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送库存扣减失败补偿事件
     */
    public void sendInventoryFailedEvent(String messageBody) {
        String messageId = String.format("INVENTORY_FAILED_%s", RandomUtil.randomString(16));
        InventoryFailedMessage inventoryFailedMessage = JSON.parseObject(messageBody, InventoryFailedMessage.class);
        log.info("发送库存扣减失败补偿事件: messageId={}, orderId={}, userId={}",
                messageId, inventoryFailedMessage.getOrderId(), inventoryFailedMessage.getUserId());
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "inventory.failed");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "inventory-compensation-exchange",
                "inventory.failed",
                message,
                correlationData
        );
        // 设置回调
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("库存扣减失败补偿事件到达Broker: {}", messageId);
                    } else {
                        log.error("库存扣减失败补偿事件未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    log.error("库存扣减失败补偿事件发送异常: messageId={}, error={}", messageId, ex.getMessage());
                }
        );
    }

    /**
     * 发送库存锁定成功事件
     */
    public void sendInventoryLockSuccessEvent(String messageBody) {
        String messageId = String.format("INVENTORY_LOCK_SUCCESS_%s", RandomUtil.randomString(16));
        InventoryLockSuccessMessage inventoryLockSuccessMessage = JSON.parseObject(messageBody, InventoryLockSuccessMessage.class);
        log.info("发送库存锁定成功事件: messageId={}, orderId={}, userId={}",
                messageId, inventoryLockSuccessMessage.getOrderId(), inventoryLockSuccessMessage.getUserId());
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "inventory.lock.success");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "inventory-lock-success-exchange",
                "inventory.lock.success",
                message,
                correlationData
        );
        // 设置回调
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("库存锁定成功事件到达Broker: {}", messageId);
                    } else {
                        log.error("库存锁定成功事件未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    log.error("库存锁定成功事件发送异常: messageId={}, error={}", messageId, ex.getMessage());
                }
        );
    }
}
