package com.knet.goods.mq.producer;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.PriceChangeMessage;
import com.knet.goods.service.IMessageRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/7/9 17:10
 * @description: 价格变动消息生产者
 */
@Slf4j
@Component
public class PriceChangeMessageProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private IMessageRetryService messageRetryService;

    private static final String PRICE_CHANGE_EXCHANGE = "price-change-exchange";
    private static final String PRICE_CHANGE_ROUTING_KEY = "price.change";

    /**
     * 发送价格变动事件
     *
     * @param priceChangeMessage 价格变动事件消息
     */
    public void sendPriceChangeEvent(PriceChangeMessage priceChangeMessage) {
        String messageId = String.format("PRICE_CHANGE%s", RandomUtil.randomString(16));
        priceChangeMessage.setEventId(messageId);
        String messageBody = JSON.toJSONString(priceChangeMessage);
        log.info("发送价格变动事件: messageId={}, eventType={}, products={}",
                messageId, priceChangeMessage.getEventType(), priceChangeMessage.getProducts().size());
        try {
            MessageProperties properties = new MessageProperties();
            properties.setHeader("routingKey", PRICE_CHANGE_ROUTING_KEY);
            properties.setHeader("messageId", messageId);
            properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
            CorrelationData correlationData = new CorrelationData(messageId);
            rabbitTemplate.convertAndSend(
                    PRICE_CHANGE_EXCHANGE,
                    PRICE_CHANGE_ROUTING_KEY,
                    message,
                    correlationData
            );
            // 设置回调
            correlationData.getFuture().addCallback(
                    result -> {
                        if (result != null && result.isAck()) {
                            log.info("价格变动事件到达Broker: {}", messageId);
                        } else {
                            log.error("价格变动事件未到达Broker: {}", messageId);
                            // 保存到重试表
                            messageRetryService.saveFailedMessage(messageId, PRICE_CHANGE_EXCHANGE,
                                    PRICE_CHANGE_ROUTING_KEY, messageBody, "消息未到达Broker");
                        }
                    },
                    ex -> {
                        log.error("价格变动事件发送异常: messageId={}, error={}", messageId, ex.getMessage());
                        // 保存到重试表
                        messageRetryService.saveFailedMessage(messageId, PRICE_CHANGE_EXCHANGE,
                                PRICE_CHANGE_ROUTING_KEY, messageBody, ex.getMessage());
                    }
            );
        } catch (Exception e) {
            log.error("发送价格变动事件异常: messageId={}, error={}", messageId, e.getMessage(), e);
            // 保存到重试表
            messageRetryService.saveFailedMessage(messageId, PRICE_CHANGE_EXCHANGE,
                    PRICE_CHANGE_ROUTING_KEY, messageBody, e.getMessage());
        }
    }
}
