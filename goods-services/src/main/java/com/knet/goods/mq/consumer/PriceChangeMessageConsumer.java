package com.knet.goods.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.PriceChangeMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.goods.service.IPriceAggregationService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.knet.common.constants.SystemConstant.PRICE_CHANGE_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/7/9 17:15
 * @description: 价格变动消息消费者
 */
@Slf4j
@Component
public class PriceChangeMessageConsumer {
    @Resource
    private IPriceAggregationService priceAggregationService;

    /**
     * 处理价格变动事件
     */
    @RabbitListener(queues = "price-change-queue.goods-services")
    public void handlePriceChangeEvent(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("接收到价格变动事件: messageId={}, routingKey={}", messageId, routingKey);
            // 消息幂等性检查
            if (!RedisCacheUtil.setIfAbsent(PRICE_CHANGE_PROCESSED.formatted(messageId), PROCESSED, 60)) {
                log.warn("价格变动事件重复消息: messageId={}", messageId);
                channel.basicAck(deliveryTag, false);
                return;
            }
            PriceChangeMessage event = JSON.parseObject(messageBody, PriceChangeMessage.class);
            if (event == null || event.getProducts() == null || event.getProducts().isEmpty()) {
                log.warn("价格变动事件数据为空: messageId={}", messageId);
                channel.basicAck(deliveryTag, false);
                return;
            }
            // 处理价格变动事件
            priceAggregationService.handlePriceChangeEvent(event);
            // 确认消息
            channel.basicAck(deliveryTag, false);
            log.info("价格变动事件处理完成: messageId={}, eventType={}, products={}",
                    messageId, event.getEventType(), event.getProducts().size());
        } catch (Exception e) {
            log.error("处理价格变动事件失败: messageId={}, error={}", messageId, e.getMessage(), e);
            try {
                // 拒绝消息，不重新入队（避免无限重试）
                channel.basicNack(deliveryTag, false, false);
            } catch (Exception nackException) {
                log.error("拒绝消息失败: messageId={}, error={}", messageId, nackException.getMessage());
            }
        }
    }
}
