package com.knet.goods.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/30 09:54
 * @description: KG 平台寄售订单
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KnetPlatformListingQueryVo extends BaseResponse {

    @Schema(description = "one Id")
    private String oneId;

    @Schema(description = "对应平台上的真实的寄售单Id")
    private String platformListingId;
}
