package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.KnetCurrencyCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:55
 * @description: knet 商品 价格变更 请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdatePriceKnetProductRequest extends BaseRequest {

    @NotEmpty(message = "商品不能为空")
    @Size(max = 100, message = "最大支持，100个商品 价格变更")
    @Valid
    @Schema(description = "products", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ProductDto> products;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProductDto extends BaseRequest {
        @NotBlank(message = "listingId不能为空")
        @Schema(description = "listingId", requiredMode = Schema.RequiredMode.REQUIRED)
        private String listingId;

        @Min(value = 0, message = "价格不能小于0")
        @NotNull(message = "价格不能为空")
        @Schema(description = "价格（单位：美分）", example = "1075", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long price;

        @NotNull(message = "货币单位不能为空")
        @Schema(description = "货币单位", requiredMode = Schema.RequiredMode.REQUIRED)
        private KnetCurrencyCode currency;
    }
}
