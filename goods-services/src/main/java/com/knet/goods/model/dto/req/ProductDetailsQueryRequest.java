package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:04
 * @description: 商品查询请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductDetailsQueryRequest extends BaseRequest {
    @NotBlank(message = "sku is not blank")
    @Schema(description = "sku", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private String account;
}
