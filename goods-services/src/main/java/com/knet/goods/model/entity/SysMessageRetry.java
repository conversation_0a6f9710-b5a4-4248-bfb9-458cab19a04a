package com.knet.goods.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:20
 * @description: 消息重试实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_message_retry")
@Schema(description = "消息重试实体")
public class SysMessageRetry extends BaseEntity {

    @TableField("message_id")
    @Schema(description = "消息ID")
    private String messageId;

    @TableField("exchange_name")
    @Schema(description = "交换机名称")
    private String exchangeName;

    @TableField("routing_key")
    @Schema(description = "路由键")
    private String routingKey;

    @TableField("message_body")
    @Schema(description = "消息内容")
    private String messageBody;

    @TableField("retry_count")
    @Schema(description = "重试次数")
    private Integer retryCount;

    @TableField("max_retry_count")
    @Schema(description = "最大重试次数")
    private Integer maxRetryCount;

    @TableField("next_retry_time")
    @Schema(description = "下次重试时间")
    private LocalDateTime nextRetryTime;

    @TableField("status")
    @Schema(description = "状态：PENDING-待重试，SUCCESS-成功，FAILED-失败")
    private String status;

    @TableField("error_message")
    @Schema(description = "错误信息")
    private String errorMessage;

    @TableField("last_retry_time")
    @Schema(description = "最后重试时间")
    private LocalDateTime lastRetryTime;
}
