package com.knet.goods.model.dto.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/25 10:52
 * @description: kj 获取sku size 平台最低价 响应体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetProductMarketDataVo extends BaseResponse {
    String sku;
    String size;
    String oneId;
    String img;
    String productName;
    PlatformProductMarketDataVo stockX;
    PlatformProductMarketDataVo goat;
    PlatformProductMarketDataVo goatIs;
    PlatformProductMarketDataVo kc;
    PlatformProductMarketDataVo ebay;
    PlatformProductMarketDataVo poizon;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class PlatformProductMarketDataVo extends BaseResponse {
        String productTemplateId;
        String variantId;
        KnetProductPrice lowestAskPrice;
        KnetProductPrice flexLowestPrice;
        KnetProductPrice localLowestAskPrice;
        KnetProductPrice defectLowestAskPrice;
        KnetProductPrice highestOfferPrice;
        KnetProductPrice lastSoldPrice;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        Date lastUpdateTime;
    }
}
