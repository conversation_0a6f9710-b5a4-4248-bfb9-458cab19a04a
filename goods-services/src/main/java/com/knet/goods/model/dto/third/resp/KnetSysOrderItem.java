package com.knet.goods.model.dto.third.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.knet.common.enums.KnetOrderItemStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/25 10:58
 * @description: SysOrderItem
 */
@Data
public class KnetSysOrderItem implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 明细项ID
     */
    private Long itemId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String itemNo;

    /**
     * 关联子订单
     */
    @Schema(description = "关联子订单")
    private String orderId;

    /**
     * 关联父订单
     */
    @Schema(description = "关联父订单ID")
    private String parentOrderId;

    /**
     * sku
     */
    @Schema(description = "sku")
    private String sku;

    /**
     * 尺码
     */
    @Schema(description = "尺码")
    private String size;

    /**
     * 品名
     */
    @Schema(description = "品名")
    private String name;

    /**
     * 商品图片URL
     */
    @Schema(description = "商品图片URL")
    private String imageUrl;

    /**
     * 单价（含税，单位：美元）
     */
    @Schema(description = " 单价（含税，单位：美元）")
    private BigDecimal price;

    /**
     * KG oneId
     */
    @Schema(description = "KG oneId")
    private String oneId;

    /**
     * b2b 商品唯一id
     */
    @Schema(description = "b2b 商品唯一id")
    private String knetListingId;
    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer count;

    @TableField("warehouse")
    @Schema(description = "存储仓库")
    private String warehouse;
    /**
     * 状态：
     *
     * @see com.knet.common.enums.KnetOrderItemStatus
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderItemStatus status;

    @Schema(description = "支付时间")
    private Date paidTime;

    @Schema(description = "KG发货时间")
    private Date shippingTime;

    @Schema(description = "发货时间")
    private Date shippedTime;

    @Schema(description = "完成时间")
    private Date completedTime;

    @Schema(description = "取消时间")
    private Date cancelledTime;

    @Schema(description = "销售渠道")
    private String sellChannel;

    private Date createTime;

    private Date updateTime;

    @Schema(description = "是否删除，0 表示未删除， 1 表示已删除。")
    private Integer delFlag;

    private Integer version;
}
