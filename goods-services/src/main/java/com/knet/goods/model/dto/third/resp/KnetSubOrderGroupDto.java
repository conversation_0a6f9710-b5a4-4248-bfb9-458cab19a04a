package com.knet.goods.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/25 13:26
 * @description: SubOrderGroupDto
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class KnetSubOrderGroupDto extends BaseResponse {
    /**
     * 父订单ID
     */
    private String parentOrderId;
    /**
     * 总价格（BigDecimal类型，用于接收SQL查询结果）
     */
    private BigDecimal totalPrice;
    /**
     * 状态：
     *
     * @see KnetOrderGroupStatus
     */
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private KnetOrderGroupStatus status;
}
