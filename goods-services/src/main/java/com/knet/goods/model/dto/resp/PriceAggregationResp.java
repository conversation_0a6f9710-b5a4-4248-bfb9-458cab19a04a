package com.knet.goods.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:05
 * @description: 价格聚合响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "价格聚合响应")
public class PriceAggregationResp {

    @Schema(description = "商品SKU")
    private String sku;

    @Schema(description = "商品规格")
    private String spec;

    @Schema(description = "最低价格（美分）")
    private String minPrice;

    @Schema(description = "最高价格（美分）")
    private String maxPrice;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
