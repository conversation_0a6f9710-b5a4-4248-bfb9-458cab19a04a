package com.knet.goods.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/5/19 14:30
 * @description: 查询平台寄售单列表请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnetGroupGetPlatformListingReq extends BaseRequest {
    @Schema(description = "销售状态", example = "ACTIVE,INACTIVE")
    private String saleStatus;

    @Schema(description = "平台", example = "B2B_SHOP")
    private String platform;

    @Schema(description = "页码，从1开始")
    private Integer current = 1;

    @Schema(description = "每页数据量")
    private Integer pageSize = 10;


    public static KnetGroupGetPlatformListingReq crateB2bShopReq(Integer current, Integer pageSize) {
        KnetGroupGetPlatformListingReq req = new KnetGroupGetPlatformListingReq();
        req.setPlatform("b2b");
        req.setSaleStatus("ACTIVE");
        req.setCurrent(current);
        req.setPageSize(pageSize);
        return req;
    }
}
