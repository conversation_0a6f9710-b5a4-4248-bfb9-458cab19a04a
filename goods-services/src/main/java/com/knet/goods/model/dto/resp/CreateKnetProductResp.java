package com.knet.goods.model.dto.resp;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:55
 * @description: 创建knet 商品 返回体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateKnetProductResp extends BaseRequest {

    @Schema(description = "上架成功记录", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ProductDto> products;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class ProductDto extends BaseRequest {
        @Schema(description = "oneId")
        private String oneId;

        @Schema(description = "listingId")
        private String listingId;
    }
}
