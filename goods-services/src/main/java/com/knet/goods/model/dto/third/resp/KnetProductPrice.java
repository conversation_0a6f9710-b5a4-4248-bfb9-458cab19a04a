package com.knet.goods.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetCurrencyCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/2/25 10:52
 * @description: kj 获取sku size 平台最低价 价格体系 响应体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KnetProductPrice extends BaseResponse {

    @Schema(description = "货币单位价格")
    KnetCurrencyCode currencyCode;

    @Schema(description = "价格")
    String amount;

    @Schema(description = "美分价格，例如一双鞋售卖 214.50 美刀， 那么它的值应该为 21450。")
    int amountUsdCents;
}
