package com.knet.goods.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.SysTaskStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/4/28 17:59
 * @description: sysSku变化事件表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_update_sys_sku_events", description = "sysSku变化事件表")
@TableName("sys_update_sys_sku_events")
public class SysUpdateSysSkuEvents extends BaseEntity {

    @Schema(description = "sku")
    @TableField(value = "sku")
    public String sku;

    @TableField(value = "img")
    @Schema(description = "商品图片")
    public String img;

    @TableField(value = "brand")
    @Schema(description = "品牌")
    public String brand;

    @TableField(value = "remarks")
    @Schema(description = "品名")
    public String remarks;

    @Schema(description = "状态")
    public SysTaskStatus status;
}
