package com.knet.goods.model.dto.third.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:31
 * @description: 订单包含具体商品数量价格信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubOrderItemResp extends BaseResponse {
    @Schema(description = "订单项ID")
    private Long itemId;
    @Schema(description = "商品SKU")
    private String sku;
    @Schema(description = "尺码")
    private String size;
    @Schema(description = "单价（美元）")
    private String price;
    @Schema(description = "数量")
    private Integer count;
}
