package com.knet.goods.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * sku池
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(name = "SysSku", description = "sku池")
public class SysSku implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    public Integer id;

    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    public Date gmtCreate;

    @TableField(value = "gmt_modify", fill = FieldFill.INSERT_UPDATE)
    public Date gmtModify;

    @Schema(description = "是否删除，0为非")
    @TableLogic
    public Integer delFlag;

    @Schema(description = "商品图片")
    public String img;

    @Schema(description = "品牌")
    public String brand;

    @Schema(description = "品名")
    public String remarks;

    @Schema(description = "尺码")
    public String spec;

    @Schema(description = "pku")
    public String pku;

    @Schema(description = "sku")
    public String sku;

    @Schema(description = "sku 检索索引")
    public String skuIndexed;

    @Schema(description = "平台回收价(预报）")
    private BigDecimal prePrice;

    @Schema(description = "创建人id")
    public Integer createById;

    @Schema(description = "热榜排名")
    public Integer hotRankNum;

    @Schema(description = "告警库存")
    public Integer warnNum;

    @Schema(description = "gender")
    public String gender;

    @Schema(description = "颜色")
    public String color;

    @Schema(description = "型号")
    public String productType;

    @Schema(description = "类别")
    public String productCategory;
}
