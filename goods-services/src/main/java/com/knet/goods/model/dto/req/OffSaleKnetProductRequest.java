package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:55
 * @description: 下架knet 商品 请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OffSaleKnetProductRequest extends BaseRequest {

    @NotEmpty(message = "商品不能为空")
    @Size(max = 100, message = "最大支持，100个商品 下架")
    @Valid
    @Schema(description = "products", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ProductDto> products;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProductDto extends BaseRequest {
        @NotBlank(message = "listingId不能为空")
        @Schema(description = "listingId", requiredMode = Schema.RequiredMode.REQUIRED)
        private String listingId;
    }
}
