package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.goods.model.dto.third.resp.SubOrderItemResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6 14:26
 * @description: 检查锁定库存请求体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "检查锁定库存请求体")
public class CheckAndLockInvRequest extends BaseRequest {
    @Schema(description = "订单项列表")
    private List<SubOrderItemResp> items;
}
