package com.knet.goods.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 10:52
 * @description: kj 获取sku size 平台最低价请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KnetGroupGetSkuSizePlatformPriceReq extends BaseRequest {
    /**
     * sku
     */
    private String sku;
    /**
     * 尺码范围 [
     * "8",
     * "8.5",
     * "9"
     * ]
     */
    private List<String> sizeRange;
}
