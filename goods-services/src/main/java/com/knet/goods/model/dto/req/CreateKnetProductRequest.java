package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.KnetCurrencyCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:55
 * @description: 创建knet 商品 请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateKnetProductRequest extends BaseRequest {

    @NotEmpty(message = "商品不能为空")
    @Size(max = 100, message = "最大支持，100个商品创建")
    @Schema(description = "创建商品请求dto", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    List<ProductDto> products;

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ProductDto extends BaseRequest {
        @NotBlank(message = "oneId不能为空")
        @Schema(description = "oneId", requiredMode = Schema.RequiredMode.REQUIRED)
        private String oneId;

        @NotBlank(message = "sku不能为空")
        @Schema(description = "sku", requiredMode = Schema.RequiredMode.REQUIRED)
        private String sku;

        @NotBlank(message = "尺码不能为空")
        @Schema(description = "尺码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String spec;

        @NotNull(message = "品牌")
        @Schema(description = "品牌", requiredMode = Schema.RequiredMode.REQUIRED)
        public String brand;

        @NotNull(message = "品名")
        @Schema(description = "品名", requiredMode = Schema.RequiredMode.REQUIRED)
        public String remarks;

        @NotNull(message = "价格不能为空")
        @Schema(description = "价格（单位：美分）", example = "1075", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long price;

        @NotNull(message = "货币单位不能为空")
        @Schema(description = "货币单位", example = "USD", requiredMode = Schema.RequiredMode.REQUIRED)
        private KnetCurrencyCode currency;

        @NotBlank(message = "仓库不能为空")
        @Schema(description = "存储仓库", requiredMode = Schema.RequiredMode.REQUIRED)
        private String warehouse;

        @NotBlank(message = "商品来源为空")
        @Schema(description = "商品来源", requiredMode = Schema.RequiredMode.REQUIRED)
        private String source;
    }
}
