package com.knet.goods.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.ProductStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:23
 * @description: 商品返回dto
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductDtoResp extends BaseResponse {
    @Schema(description = "商品id")
    private Long id;

    @Schema(description = "knet_product唯一标识符")
    private String productId;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date updateTime;

    @Schema(description = "knet唯一标识符")
    private String oneId;

    @Schema(description = "sku")
    private String sku;

    @Schema(description = "尺码")
    private String spec;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "价格（单位：美元）", example = "10.75")
    private String price;

    @Schema(description = "原始价格（单位：美元）", example = "10.75")
    private String originalPrice;

    @Schema(description = "存储仓库")
    private String store;
    /**
     * @see ProductStatus
     */
    @Schema(description = "商品状态 上架/下架")
    private String status;
}
