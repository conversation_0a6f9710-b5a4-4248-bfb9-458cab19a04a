package com.knet.goods.model.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:10
 * @description: 价格查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "价格查询请求")
public class PriceQueryRequest {

    @Schema(description = "单个SKU查询")
    private SingleSkuQuery singleQuery;

    @Schema(description = "批量skus")
    private List<ProductQuery> batchSkus;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "单个SKU查询")
    public static class SingleSkuQuery {
        @NotBlank(message = "SKU不能为空")
        @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
        private String sku;

        @Schema(description = "尺码")
        private String spec;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "批量skus")
    public static class ProductQuery {
        @NotBlank(message = "SKU不能为空")
        @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
        private String sku;

        @Schema(description = "尺码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String spec;
    }
}
