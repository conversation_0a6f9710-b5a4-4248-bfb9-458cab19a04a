package com.knet.goods.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2025/7/9 16:00
 * @description: 价格聚合实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_price_aggregation")
@Schema(description = "价格聚合实体")
public class SysPriceAggregation extends BaseEntity {

    @TableField("sku")
    @Schema(description = "商品SKU")
    private String sku;

    @TableField("spec")
    @Schema(description = "商品规格")
    private String spec;

    @TableField("min_price")
    @Schema(description = "最低价格（美分）")
    private Long minPrice;

    @TableField("max_price")
    @Schema(description = "最高价格（美分）")
    private Long maxPrice;
}
