package com.knet.goods;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:10
 * @description: 商品服务 主启动类
 */
@EnableScheduling
@ComponentScan(basePackages = {"com.knet.goods", "com.knet.common"})
@EnableAsync
@MapperScan("com.knet.goods.mapper")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class GoodsServicesApplication {
    public static void main(String[] args) {
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(GoodsServicesApplication.class);
        System.out.println(" 🚀Goods Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
