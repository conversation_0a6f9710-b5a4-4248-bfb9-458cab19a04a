package com.knet.goods.system.schedule;

import com.knet.goods.service.IMessageRetryService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/9 17:35
 * @description: 消息重试定时任务
 */
@Slf4j
@Component
public class MessageRetryJob {

    @Resource
    private IMessageRetryService messageRetryService;

    /**
     * 消息重试任务
     * 每5分钟执行一次，处理失败的消息重试
     */
    @XxlJob("messageRetryJobHandler")
    public void messageRetryJobHandler() {
        try {
            log.info("开始执行消息重试任务");
            String param = XxlJobHelper.getJobParam();
            log.info("任务参数: {}", param);
            messageRetryService.processMessageRetry();
            log.info("消息重试任务执行完成");
            XxlJobHelper.handleSuccess("消息重试任务执行成功");
        } catch (Exception e) {
            log.error("消息重试任务执行失败", e);
            XxlJobHelper.handleFail("消息重试任务执行失败: " + e.getMessage());
        }
    }
}
