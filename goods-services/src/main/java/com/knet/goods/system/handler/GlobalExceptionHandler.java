package com.knet.goods.system.handler;

import com.knet.common.base.HttpResult;
import com.knet.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Arrays;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2025/3/3 11:43
 * @description: 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * @RequestBody 上校验失败后抛出的异常是 MethodArgumentNotValidException 异常。
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public HttpResult<String> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String messages = bindingResult.getAllErrors()
                .stream()
                .map(ObjectError::getDefaultMessage)
                .collect(Collectors.joining("；"));
        return HttpResult.error(HttpStatus.BAD_REQUEST.value(), messages);
    }

    /**
     * 不加 @RequestBody注解，校验失败抛出的则是 BindException
     */
    @ExceptionHandler(value = BindException.class)
    public HttpResult<String> exceptionHandler(BindException e) {
        String messages = e.getBindingResult().getAllErrors()
                .stream()
                .map(ObjectError::getDefaultMessage)
                .collect(Collectors.joining("；"));
        return HttpResult.error(HttpStatus.BAD_REQUEST.value(), messages);
    }

    /**
     * @RequestParam 上校验失败后抛出的异常是 ConstraintViolationException
     */
    @ExceptionHandler({ConstraintViolationException.class})
    public HttpResult<String> methodArgumentNotValid(ConstraintViolationException exception) {
        String message = exception.getConstraintViolations().stream().map(ConstraintViolation::getMessage).collect(Collectors.joining("；"));
        return HttpResult.error(HttpStatus.BAD_REQUEST.value(), message);
    }

    @ExceptionHandler(ServiceException.class)
    public HttpResult<String> handleRuntimeException(ServiceException e) {
        log.error("ServiceException: ", e);
        HttpResult<String> httpResult = HttpResult.error(e.getStatusCode(), e.getMessage());
        return httpResult;
    }

    @ExceptionHandler(Exception.class)
    public HttpResult<String> handleException(Exception e) {
        log.error("Exception:", e);
        String detailedMessage = String.format("异常类型: %s; 异常信息: %s; 堆栈跟踪: %s",
                e.getClass().getName(), e.getMessage(), Arrays.toString(e.getStackTrace()));
        log.error(detailedMessage);
        // 返回给前端的是通用错误提示，不包含具体异常细节
        return HttpResult.error(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An exception occurred in the system, please contact the administrator");
    }
}