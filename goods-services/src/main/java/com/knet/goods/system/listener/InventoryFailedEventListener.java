package com.knet.goods.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.goods.mq.producer.InventoryMessageProducer;
import com.knet.goods.system.event.InventoryFailedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/11 17:15
 * @description: 库存扣减失败事件监听器
 */
@Component
public class InventoryFailedEventListener {

    @Resource
    private InventoryMessageProducer inventoryMessageProducer;

    /**
     * 处理库存扣减失败事件
     *
     * @param event 库存扣减失败事件
     */
    @EventListener(classes = InventoryFailedEvent.class)
    public void handlePaymentEventListener(InventoryFailedEvent event) {
        inventoryMessageProducer.sendInventoryFailedEvent(JSON.toJSONString(event.getMessage()));
    }
}
