package com.knet.goods.system.schedule;

import com.knet.goods.service.IPriceAggregationService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/8/5 16:00
 * @description: 价格聚合数据清理定时任务
 */
@Slf4j
@Component
public class PriceAggregationCleanupJob {

    @Resource
    private IPriceAggregationService priceAggregationService;

    /**
     * todo 清理过期的价格聚合数据
     * 删除没有对应在售商品的价格聚合记录
     * <p>
     * 建议执行频率：每天凌晨2点执行一次
     * Cron表达式：0 0 2 * * ?
     */
    @XxlJob("priceAggregationCleanupJob")
    public void cleanupExpiredPriceAggregation() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            String message = "开始执行价格聚合数据清理任务";
            log.info(message);
            XxlJobHelper.log(message);
            // 执行清理操作
            int deletedCount = priceAggregationService.cleanupExpiredPriceAggregation();
            stopWatch.stop();
            String successMessage = String.format(
                    "价格聚合数据清理任务执行完成，耗时: %d ms，清理记录数: %d",
                    stopWatch.getTotalTimeMillis(),
                    deletedCount
            );
            log.info(successMessage);
            XxlJobHelper.log(successMessage);
        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format(
                    "价格聚合数据清理任务执行失败，耗时: %d ms，错误信息: %s",
                    stopWatch.getTotalTimeMillis(),
                    e.getMessage()
            );
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            throw e;
        }
    }

    /**
     * todo 手动触发价格聚合数据清理
     * 可用于紧急情况下的手动清理
     */
    @XxlJob("manualPriceAggregationCleanup")
    public void manualCleanupExpiredPriceAggregation() {
        try {
            String jobParam = XxlJobHelper.getJobParam();
            String message = String.format("手动触发价格聚合数据清理任务，参数: %s", jobParam);
            log.info(message);
            XxlJobHelper.log(message);
            // 执行清理操作
            cleanupExpiredPriceAggregation();
        } catch (Exception e) {
            String errorMessage = String.format("手动价格聚合数据清理任务执行失败: %s", e.getMessage());
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            throw e;
        }
    }

    /**
     * 初始化价格聚合表
     * 从knet_product表中获取所有在售商品数据，初始化价格聚合表
     * <p>
     * 建议执行频率：手动执行或系统启动时执行一次
     */
    @XxlJob("initializePriceAggregationJob")
    public void initializePriceAggregation() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            String message = "开始执行价格聚合表初始化任务";
            log.info(message);
            XxlJobHelper.log(message);
            // 执行初始化操作
            int initializedCount = priceAggregationService.initializePriceAggregation();
            stopWatch.stop();
            String successMessage = String.format(
                    "价格聚合表初始化任务执行完成，耗时: %d ms，初始化记录数: %d",
                    stopWatch.getTotalTimeMillis(),
                    initializedCount
            );
            log.info(successMessage);
            XxlJobHelper.log(successMessage);
        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format(
                    "价格聚合表初始化任务执行失败，耗时: %d ms，错误信息: %s",
                    stopWatch.getTotalTimeMillis(),
                    e.getMessage()
            );
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            throw e;
        }
    }

    /**
     * 批量初始化价格聚合表
     * 分批处理大量数据，避免内存溢出
     * <p>
     * 任务参数：batchSize（批次大小，默认1000）
     */
    @XxlJob("batchInitializePriceAggregationJob")
    public void batchInitializePriceAggregation() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            String jobParam = XxlJobHelper.getJobParam();
            int batchSize = 1000; // 默认批次大小

            if (jobParam != null && !jobParam.trim().isEmpty()) {
                try {
                    batchSize = Integer.parseInt(jobParam.trim());
                } catch (NumberFormatException e) {
                    log.warn("任务参数格式错误，使用默认批次大小: {}", batchSize);
                    XxlJobHelper.log("任务参数格式错误，使用默认批次大小: {}", batchSize);
                }
            }

            String message = String.format("开始执行批量价格聚合表初始化任务，批次大小: %d", batchSize);
            log.info(message);
            XxlJobHelper.log(message);

            // 执行批量初始化操作
            int initializedCount = priceAggregationService.batchInitializePriceAggregation(batchSize);

            stopWatch.stop();
            String successMessage = String.format(
                    "批量价格聚合表初始化任务执行完成，耗时: %d ms，批次大小: %d，初始化记录数: %d",
                    stopWatch.getTotalTimeMillis(),
                    batchSize,
                    initializedCount
            );
            log.info(successMessage);
            XxlJobHelper.log(successMessage);

        } catch (Exception e) {
            stopWatch.stop();
            String errorMessage = String.format(
                    "批量价格聚合表初始化任务执行失败，耗时: %d ms，错误信息: %s",
                    stopWatch.getTotalTimeMillis(),
                    e.getMessage()
            );
            log.error(errorMessage, e);
            XxlJobHelper.log(errorMessage);
            throw e;
        }
    }
}
