package com.knet.goods.system.interceptor;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.PermissionCheck;
import com.knet.common.constants.SystemConstant;
import com.knet.common.exception.ServiceException;
import com.knet.goods.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/2/18 09:56
 * @description: 权限拦截器
 */
@Slf4j
@Component
@Aspect
public class PermissionAspect {

    @Resource
    private JwtUtil goodsJwtUtil;

    @Pointcut("@annotation(permissionCheck)")
    public void permissionPointCut(PermissionCheck permissionCheck) {
    }

    @Before(value = "permissionPointCut(permissionCheck)", argNames = "joinPoint,permissionCheck")
    public void checkRoleAndPermission(JoinPoint joinPoint, PermissionCheck permissionCheck) throws Throwable {
        // 从请求中获取自定义请求头token
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(attributes).getRequest();
        log.info("===============系统权限操作日志===============");
        Signature signature = joinPoint.getSignature();
        log.info("请求方式：{}", request.getMethod());
        log.info("请求ip：{}", request.getRemoteAddr());
        log.info("请求类方法：{}", signature);
        log.info("请求参数：{}", Arrays.toString(joinPoint.getArgs()));
        String token = request.getHeader(SystemConstant.TOKEN);
        // 获取当前用户的角色和权限信息
        String rolerCode = getRolerCodeFromToken(token);
        if (StrUtil.isBlank(rolerCode)) {
            return;
        }
        // 验证角色
        String requiredRole = permissionCheck.role();
        if (!rolerCode.equals(requiredRole)) {
            throw new ServiceException("You do not have the required role to access this resource.");
        }
        // 验证具体权限
        String[] requiredPermissions = permissionCheck.permission();
        List<String> authorities = getUserPermissions(token);
        if (authorities.isEmpty()) {
            return;
        }
        boolean permissionMatch = Arrays.stream(requiredPermissions).anyMatch(authorities::contains);
        if (!permissionMatch && requiredPermissions.length > 0) {
            throw new ServiceException("You do not have the required permission to access this resource.");
        }
    }

    /**
     * 获取用户角色
     *
     * @param token token
     * @return 角色代码（code）
     */
    private String getRolerCodeFromToken(String token) throws RuntimeException {
        if (!goodsJwtUtil.validateToken(token)) {
            throw new RuntimeException("Invalid token");
        }
        return goodsJwtUtil.getRolerFromToken(token);
    }

    /**
     * 获取用户权限
     *
     * @param token token
     * @return 权限列表
     */
    private List<String> getUserPermissions(String token) {
        return Collections.emptyList();
    }
}
