package com.knet.goods.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 商品服务RabbitMQ配置
 **/

@Configuration
public class RabbitConfig {

    /**
     * 订单交换机（与order-service共享）
     */
    @Bean
    public TopicExchange orderExchange() {
        return new TopicExchange("order-exchange", true, false);
    }

    /**
     * 商品服务专用的订单队列
     */
    @Bean
    public Queue goodsOrderQueue() {
        return QueueBuilder
                .durable("order-queue.goods-services")
                .withArgument("x-dead-letter-exchange", "goods-service.dlx")
                .withArgument("x-dead-letter-routing-key", "goods.order.*")
                .build();
    }

    /**
     * 商品服务订单队列绑定
     */
    @Bean
    public Binding goodsOrderBinding() {
        return BindingBuilder
                .bind(goodsOrderQueue())
                .to(orderExchange())
                .with("order.*");
    }

    /**
     * 商品服务专用死信交换机
     */
    @Bean
    public DirectExchange goodsDlxExchange() {
        return new DirectExchange("goods-service.dlx", true, false);
    }

    /**
     * 商品服务订单死信队列
     */
    @Bean
    public Queue goodsOrderDlxQueue() {
        return QueueBuilder
                .durable("goods-service.dlx.order.queue")
                .build();
    }

    /**
     * 商品服务订单死信绑定
     */
    @Bean
    public Binding goodsOrderDlxBinding() {
        return BindingBuilder
                .bind(goodsOrderDlxQueue())
                .to(goodsDlxExchange())
                .with("goods.order.*");
    }

    /**
     * 商品服务价格变动死信队列
     */
    @Bean
    public Queue goodsPriceChangeDlxQueue() {
        return QueueBuilder
                .durable("goods-service.dlx.price-change.queue")
                .build();
    }

    /**
     * 商品服务价格变动死信绑定
     */
    @Bean
    public Binding goodsPriceChangeDlxBinding() {
        return BindingBuilder
                .bind(goodsPriceChangeDlxQueue())
                .to(goodsDlxExchange())
                .with("goods.price.change.*");
    }

    /**
     * 库存补偿交换机
     */
    @Bean
    public TopicExchange inventoryCompensationExchange() {
        return new TopicExchange("inventory-compensation-exchange", true, false);
    }

    /**
     * 订单延迟交换机（与order-service共享）
     */
    @Bean
    public TopicExchange delayedExchange() {
        // 订单业务交换机
        return new TopicExchange("order.delayed.exchange", true, false);
    }


    /**
     * 商品服务订单超时队列
     */
    @Bean
    public Queue goodsOrderTimeoutQueue() {
        return new Queue("timeout.order.queue.goods-services", true);
    }

    /**
     * 商品服务订单超时队列绑定
     */
    @Bean
    public Binding goodsOrderTimeoutBinding() {
        return BindingBuilder.bind(goodsOrderTimeoutQueue())
                .to(delayedExchange())
                .with("timeout.order");
    }

    /**
     * 库存锁定成功交换机
     */
    @Bean
    public TopicExchange inventoryLockSuccessExchange() {
        return new TopicExchange("inventory-lock-success-exchange", true, false);
    }

    /**
     * 价格变动交换机
     */
    @Bean
    public TopicExchange priceChangeExchange() {
        return new TopicExchange("price-change-exchange", true, false);
    }

    /**
     * 价格变动队列
     */
    @Bean
    public Queue priceChangeQueue() {
        return QueueBuilder
                .durable("price-change-queue.goods-services")
                .withArgument("x-dead-letter-exchange", "goods-service.dlx")
                .withArgument("x-dead-letter-routing-key", "goods.price.change.*")
                .build();
    }

    /**
     * 价格变动队列绑定
     */
    @Bean
    public Binding priceChangeBinding() {
        return BindingBuilder
                .bind(priceChangeQueue())
                .to(priceChangeExchange())
                .with("price.change");
    }
}
