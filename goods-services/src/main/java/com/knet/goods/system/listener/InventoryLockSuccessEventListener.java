package com.knet.goods.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.goods.mq.producer.InventoryMessageProducer;
import com.knet.goods.system.event.InventoryLockSuccessEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:05
 * @description: 库存锁定成功事件监听器
 */
@Component
public class InventoryLockSuccessEventListener {

    @Resource
    private InventoryMessageProducer inventoryMessageProducer;

    /**
     * 处理库存锁定成功事件
     *
     * @param event 库存锁定成功事件
     */
    @EventListener(classes = InventoryLockSuccessEvent.class)
    public void handleInventoryLockSuccessEvent(InventoryLockSuccessEvent event) {
        inventoryMessageProducer.sendInventoryLockSuccessEvent(JSON.toJSONString(event.getMessage()));
    }
}
