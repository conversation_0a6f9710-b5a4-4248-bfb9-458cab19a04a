package com.knet.goods.system.handler;

import cn.hutool.core.collection.CollUtil;
import com.knet.goods.mapper.KnetProductMapper;
import com.knet.goods.system.config.TempTableConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/7/8 16:00
 * @description: 临时表管理工具类
 */
@Slf4j
@Component
public class TempTableManager {
    @Resource
    private KnetProductMapper knetProductMapper;
    @Resource
    private TempTableConfig tempTableConfig;

    /**
     * 创建并填充临时表
     *
     * @param skus    SKU集合
     * @param remarks 商品名称集合
     * @return 是否成功创建并填充
     */
    public boolean createAndPopulateTempTable(Set<String> skus, Set<String> remarks) {
        return createAndPopulateSeparateTables(skus, remarks);
    }

    /**
     * 创建并填充临时表
     */
    private boolean createAndPopulateSeparateTables(Set<String> skus, Set<String> remarks) {
        try {
            knetProductMapper.createTempSkuListTable();
            log.debug("SKU临时表创建成功");
            knetProductMapper.createTempRemarkListTable();
            log.debug("商品名称临时表创建成功");
            if (CollUtil.isNotEmpty(skus)) {
                insertSkusToSkuTableInBatches(new ArrayList<>(skus));
                log.debug("SKU数据插入完成，数量: {}", skus.size());
            }
            if (CollUtil.isNotEmpty(remarks)) {
                insertRemarksToRemarkTableInBatches(new ArrayList<>(remarks));
                log.debug("商品名称数据插入完成，数量: {}", remarks.size());
            }
            return true;
        } catch (Exception e) {
            log.error("创建或填充分表临时表失败", e);
            cleanupTempTable();
            return false;
        }
    }


    /**
     * 分批插入SKU数据到SKU表
     *
     * @param skuList SKU列表
     */
    private void insertSkusToSkuTableInBatches(List<String> skuList) {
        int batchSize = tempTableConfig.getBatchSize();
        for (int i = 0; i < skuList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skuList.size());
            List<String> batch = skuList.subList(i, endIndex);
            knetProductMapper.insertSkusToSkuTable(batch);
            log.debug("插入SKU到SKU表批次: {}-{}", i, endIndex - 1);
        }
    }

    /**
     * 分批插入商品名称数据到商品名称表
     *
     * @param remarkList 商品名称列表
     */
    private void insertRemarksToRemarkTableInBatches(List<String> remarkList) {
        int batchSize = tempTableConfig.getBatchSize();
        for (int i = 0; i < remarkList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, remarkList.size());
            List<String> batch = remarkList.subList(i, endIndex);
            knetProductMapper.insertRemarksToRemarkTable(batch);
            log.debug("插入商品名称到商品名称表批次: {}-{}", i, endIndex - 1);
        }
    }

    /**
     * 清理临时表
     */
    public void cleanupTempTable() {
        if (tempTableConfig.isAutoCleanup()) {
            try {
                knetProductMapper.dropTempSkuListTable();
                log.debug("SKU临时表清理成功");
            } catch (Exception e) {
                log.warn("清理SKU临时表失败", e);
            }
            try {
                knetProductMapper.dropTempRemarkListTable();
                log.debug("商品名称临时表清理成功");
            } catch (Exception e) {
                log.warn("清理商品名称临时表失败", e);
            }
        }
    }


    /**
     * 判断是否应该使用临时表优化
     *
     * @param skuCount    SKU数量
     * @param remarkCount 商品名称数量
     * @return 是否使用临时表
     */
    public boolean shouldUseTempTable(int skuCount, int remarkCount) {
        return tempTableConfig.isEnabled() &&
                (skuCount + remarkCount) > tempTableConfig.getSkuThreshold();
    }

    /**
     * 获取临时表配置信息
     *
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format("临时表配置 - 分表策略, 启用: %s, 阈值: %d, 批次大小: %d, 引擎: %s, 自动清理: %s",
                tempTableConfig.isEnabled(),
                tempTableConfig.getSkuThreshold(),
                tempTableConfig.getBatchSize(),
                tempTableConfig.getEngine(),
                tempTableConfig.isAutoCleanup());
    }
}
