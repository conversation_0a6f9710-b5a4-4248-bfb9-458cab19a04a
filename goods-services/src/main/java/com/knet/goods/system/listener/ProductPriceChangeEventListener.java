package com.knet.goods.system.listener;

import com.knet.goods.mq.producer.PriceChangeMessageProducer;
import com.knet.goods.system.event.ProductPriceChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/10 13:27
 * @description: 商品信息变更事件监听器
 */
@Component
public class ProductPriceChangeEventListener {

    @Resource
    private PriceChangeMessageProducer priceChangeMessageProducer;

    /**
     * 处理商品价格变更事件
     *
     * @param event 商品价格变更事件
     */
    @EventListener(classes = ProductPriceChangeEvent.class)
    public void handleProductPriceChangeEvent(ProductPriceChangeEvent event) {
        priceChangeMessageProducer.sendPriceChangeEvent(event.getMessage());
    }
}
