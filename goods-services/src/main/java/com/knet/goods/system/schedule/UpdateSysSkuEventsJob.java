package com.knet.goods.system.schedule;

import cn.hutool.core.collection.CollUtil;
import com.knet.goods.model.entity.SysUpdateSysSkuEvents;
import com.knet.goods.service.IKnetProductService;
import com.knet.goods.service.ISysUpdateSysSkuEventsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 10:22
 * @description: 处理sku数据变更事件
 */
@Slf4j
@Component
public class UpdateSysSkuEventsJob {
    @Resource
    private IKnetProductService knetProductService;
    @Resource
    private ISysUpdateSysSkuEventsService sysUpdateSysSkuEventsService;

    /**
     * 每5分钟执行一次，sku变动导致的商品数据变更
     */
    @XxlJob("updateKnetProductDataBySysSkuChange")
    public ReturnT<String> updateKnetProductDataBySysSkuChange(String param) {
        log.info("任务 updateKnetProductDataBySysSkuChange 开始执行 ");
        String time = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        XxlJobHelper.log("XXL-JOB 任务触发时间: {}", time);
        StopWatch stopWatch = new StopWatch("updateKnetProductDataBySysSkuChange");
        stopWatch.start();
        List<SysUpdateSysSkuEvents> updateEvents = sysUpdateSysSkuEventsService.findNeedToUpdateEvents(5, 100);
        if (CollUtil.isEmpty(updateEvents)) {
            log.info("没有需要执行的-sku变动导致的商品数据变更-事件");
            XxlJobHelper.log("没有需要执行的-sku变动导致的商品数据变更-事件");
            return ReturnT.SUCCESS;
        }
        try {
            knetProductService.updateKnetProductForSysSkuInfo(updateEvents);
            sysUpdateSysSkuEventsService.successUpdatePriceTasks(updateEvents);
            stopWatch.stop();
            String resultMsg = String.format("任务 updateKnetProductDataBySysSkuChange 成功执行，共处理 %d 条事件，耗时：%d ms", updateEvents.size(), stopWatch.getTotalTimeMillis());
            log.info(resultMsg);
            XxlJobHelper.log(resultMsg);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            sysUpdateSysSkuEventsService.failUpdatePriceTasks(updateEvents);
            log.error("任务 updateKnetProductDataBySysSkuChange 执行失败,总执行数量:[{}],执行任务耗时：[{}] ms", updateEvents.size(), stopWatch.getTotalTimeMillis(), e);
            XxlJobHelper.handleFail("sku变动导致的商品数据变更-事件-执行失败: " + e.getMessage());
            return ReturnT.FAIL;
        }
    }
}
