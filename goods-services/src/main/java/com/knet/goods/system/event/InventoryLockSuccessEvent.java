package com.knet.goods.system.event;

import com.knet.common.dto.message.InventoryLockSuccessMessage;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/12/19 16:00
 * @description: 库存锁定成功事件
 */
@Getter
public class InventoryLockSuccessEvent extends ApplicationEvent {
    private final InventoryLockSuccessMessage message;

    public InventoryLockSuccessEvent(Object source, InventoryLockSuccessMessage message) {
        super(source);
        this.message = message;
    }
}
