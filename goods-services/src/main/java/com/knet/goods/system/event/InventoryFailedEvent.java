package com.knet.goods.system.event;

import com.knet.common.dto.message.InventoryFailedMessage;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/6/11 16:00
 * @description: 库存扣减失败事件
 */
@Getter
public class InventoryFailedEvent extends ApplicationEvent {
    private final InventoryFailedMessage message;

    public InventoryFailedEvent(Object source, InventoryFailedMessage message) {
        super(source);
        this.message = message;
    }
}
