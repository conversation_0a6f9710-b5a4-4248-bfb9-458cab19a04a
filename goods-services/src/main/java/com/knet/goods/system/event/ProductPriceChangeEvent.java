package com.knet.goods.system.event;

import com.knet.common.dto.message.PriceChangeMessage;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/7/10 13:21
 * @description: 商品信息变更事件
 */
@Getter
public class ProductPriceChangeEvent extends ApplicationEvent {
    private final PriceChangeMessage message;

    public ProductPriceChangeEvent(Object source, PriceChangeMessage message) {
        super(source);
        this.message = message;
    }
}
