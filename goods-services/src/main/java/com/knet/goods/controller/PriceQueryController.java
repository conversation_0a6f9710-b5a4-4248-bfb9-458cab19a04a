package com.knet.goods.controller;

import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.req.PriceQueryRequest;
import com.knet.goods.model.dto.resp.PriceAggregationResp;
import com.knet.goods.service.IPriceAggregationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/9 17:30
 * @description: 价格查询控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/price")
@Tag(name = "价格查询接口", description = "提供高性能价格查询服务")
public class PriceQueryController {

    @Resource
    private IPriceAggregationService priceAggregationService;

    /**
     * 单个SKU价格查询
     *
     * @param sku  商品SKU
     * @param spec 商品规格
     * @return 价格信息
     */
    @GetMapping("/query")
    @Operation(summary = "单个SKU价格查询", description = "根据SKU和规格查询商品价格信息")
    public HttpResult<PriceAggregationResp> queryPrice(
            @RequestParam String sku,
            @RequestParam String spec) {
        log.info("查询单个商品价格: sku={}, spec={}", sku, spec);
        PriceAggregationResp result = priceAggregationService.queryPrice(sku, spec);
        return HttpResult.ok(result);
    }

    /**
     * 批量价格查询
     *
     * @param request 批量查询请求
     * @return 价格信息列表
     */
    @PostMapping("/batch-query")
    @Operation(summary = "批量价格查询", description = "批量查询多个商品的价格信息")
    public HttpResult<List<PriceAggregationResp>> batchQueryPrice(
            @RequestBody @Validated PriceQueryRequest request) {
        log.info("批量查询商品价格: products={}", request.getBatchSkus().size());
        List<PriceAggregationResp> results = priceAggregationService.batchQueryPrice(request.getBatchSkus());
        return HttpResult.ok(results);
    }
}
