package com.knet.goods.controller;

import com.knet.common.base.HttpResult;
import com.knet.goods.service.ISkuCacheService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Set;

/**
 * SKU缓存控制器
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Tag(name = "SKU缓存管理", description = "SKU缓存相关接口")
@RestController
@RequestMapping("/sku-cache")
@RequiredArgsConstructor
public class SkuCacheController {
    @Resource
    private ISkuCacheService skuCacheService;

    @Operation(summary = "根据关键词模糊匹配SKU", description = "根据输入的关键词在SKU编码和检索索引中进行模糊匹配")
    @GetMapping("/match")
    public HttpResult<Set<String>> matchSkus(
            @Parameter(description = "匹配关键词", required = true)
            @RequestParam String keyword) {
        return HttpResult.ok(skuCacheService.matchSkus(keyword));
    }

    @Operation(summary = "根据关键词在remark中模糊匹配SKU", description = "根据输入的关键词在SKU备注中进行模糊匹配")
    @GetMapping("/match-by-remark")
    public HttpResult<Set<String>> matchSkusByRemark(
            @Parameter(description = "匹配关键词", required = true)
            @RequestParam String keyword) {
        return HttpResult.ok(skuCacheService.matchProductsByRemark(keyword));
    }
}
