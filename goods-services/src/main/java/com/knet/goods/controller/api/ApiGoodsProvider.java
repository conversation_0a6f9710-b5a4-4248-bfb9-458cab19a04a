package com.knet.goods.controller.api;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.goods.model.dto.req.*;
import com.knet.goods.model.dto.resp.*;
import com.knet.goods.service.IApiGoodsService;
import com.knet.goods.service.IKnetProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/25 14:52
 * @description: 商品服务-对外提供接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "商品服务-对外提供接口", description = "商品服务-对外提供接口")
public class ApiGoodsProvider {

    @Resource
    private IApiGoodsService apiGoodsService;
    @Resource
    private IKnetProductService iKnetProductService;

    /**
     * 创建上架商品
     *
     * @param request 创建商品请求体
     * @return 返回创建结果
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 创建上架商品")
    @Operation(description = "创建上架商品")
    @PostMapping("/product/create")
    public HttpResult<CreateKnetProductResp> createProducts(@Validated @RequestBody CreateKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.createProducts(request));
    }

    /**
     * 商品下架
     *
     * @param request 商品下架请求体
     * @return 返回下架结果
     * @see com.knet.goods.model.dto.req.CreateKnetProductRequest
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 商品下架")
    @Operation(description = "商品下架")
    @PostMapping("/product/offSale")
    public HttpResult<OffSaleKnetProductResp> offSale(@Validated @RequestBody OffSaleKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.offSale(request));
    }

    /**
     * 更新商品价格
     *
     * @param request 更新商品价格请求体
     * @return 返回更新结果
     */
    @ModifyHeader(handlerType = "API_KEY")
    @Loggable(value = "knet 更新商品价格")
    @Operation(description = "更新商品价格")
    @PostMapping("/product/updatePrice")
    public HttpResult<OffSaleKnetProductResp> updatePrice(@Validated @RequestBody UpdatePriceKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.updatePrice(request));
    }

    @Loggable(value = "knet 查询商品")
    @Operation(description = "外部查询商品")
    @PostMapping("/no-auth/product/list")
    public HttpResult<List<QueryKnetProductResp>> queryKnetProductsNoAuth(@Validated @RequestBody InnerKnetProductRequest request) {
        return HttpResult.ok(apiGoodsService.queryKnetProduct(request));
    }

    /**
     * 商品详情
     *
     * @param request 查询条件
     * @return 商品详情
     * @see com.knet.goods.model.dto.req.ProductDetailsQueryRequest
     */
    @Loggable(value = "knet查询-商品详情")
    @Operation(description = "商品详情")
    @PostMapping("/no-auth/product/detail")
    public HttpResult<List<ProductSkuSpecPriceDtoResp>> queryProductDetails(@Validated @RequestBody ProductDetailsQueryRequest request) {
        return HttpResult.ok(iKnetProductService.queryProductDetails(request));
    }

    /**
     * knet批量_sku价格查询
     *
     * @param request 批量查询请求
     * @return 价格信息列表
     */
    @Loggable(value = "knet批量_sku价格查询")
    @PostMapping("/product/price/batch-query")
    @Operation(summary = "knet批量_sku价格查询", description = "批量查询多个商品的价格信息")
    public HttpResult<List<PriceAggregationResp>> batchQueryPrice(@RequestBody @Validated PriceQueryRequest request) {
        log.info("批量查询商品价格: products={}", request.getBatchSkus().size());
        List<PriceAggregationResp> results = apiGoodsService.batchQueryPrice(request.getBatchSkus());
        return HttpResult.ok(results);
    }
}
