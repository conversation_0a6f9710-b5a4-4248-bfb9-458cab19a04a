#!/bin/bash
# 延时180秒
echo "Waiting for 180 seconds before starting the health check..."
sleep 180
services=(
  "http://127.0.0.1:7000/actuator/health"
  "http://127.0.0.1:7001/goodServices/actuator/health"
  "http://127.0.0.1:7002/userServices/actuator/health"
  "http://127.0.0.1:7004/orderService/actuator/health"
  "http://127.0.0.1:7005/paymentService/actuator/health"
  "http://127.0.0.1:7006/notificationServices/actuator/health"
  "http://127.0.0.1:7007/delayedServices/actuator/health"
)
all_services_up=0

for service in "${services[@]}"
do
  echo "Checking $service..."

  http_status=$(curl -s -o /dev/null -w "%{http_code}" "$service")
  response_content=$(curl -s "$service")

  echo "Response Content: $response_content"
  echo "HTTP Status Code: $http_status"

  if [ "$http_status" -eq 200 ]; then
    echo "Service at $service is UP."
  else
    echo "Service at $service is DOWN."
    all_services_up=1
  fi

  echo ""
done

if [ $all_services_up -eq 0 ]; then
  echo "All services are running normally."
  exit 0
else
  echo "One or more services are down."
  exit 1
fi