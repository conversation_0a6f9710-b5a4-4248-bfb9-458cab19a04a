<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.knet.user.mapper.SysShopCartSizeDetailMapper">

    <delete id="removeByCartId">
        DELETE d
        FROM sys_shop_cart_size_detail d
        JOIN sys_shop_cart_item i ON d.cart_item_id = i.id
        JOIN sys_shop_cart c ON i.cart_id = c.id
        <where>
            <if test="cartId != null">
                c.id = #{cartId}
            </if>
        </where>
    </delete>
</mapper>
