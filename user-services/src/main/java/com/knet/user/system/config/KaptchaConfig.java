package com.knet.user.system.config;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Properties;

@Component
public class KaptchaConfig {

    @Bean
    public DefaultKaptcha producer() {
        Properties properties = new Properties();
        properties.put("kaptcha.border", "no");
        properties.put("kaptcha.textproducer.font.color", "black");
        properties.put("kaptcha.textproducer.char.space", "5");
        properties.put("kaptcha.textproducer.char.length", "4");
        properties.put("kaptcha.textproducer.char.string", "123456789");

        // 设置字体，使用系统默认字体，避免Docker环境字体问题
        properties.put("kaptcha.textproducer.font.names", "<PERSON>l,SansSerif");
        properties.put("kaptcha.textproducer.font.size", "40");

        // 图片尺寸设置
        properties.put("kaptcha.image.width", "120");
        properties.put("kaptcha.image.height", "40");

        // 噪声设置
        properties.put("kaptcha.noise.impl", "com.google.code.kaptcha.impl.NoNoise");

        Config config = new Config(properties);
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }

}
