//package com.knet.user.system.config;
//
//import com.knet.user.system.Interceptor.MemberLoginInterceptor;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import javax.annotation.Resource;
//
/// **
// * <AUTHOR>
// * @date 2025/2/17 13:29
// * @description: web配置（拦截器）
// */
//@Deprecated
//@Configuration
//public class WebConfig implements WebMvcConfigurer {
//    @Resource
//    private MemberLoginInterceptor memberLoginInterceptor;
//
//
//    @Override
//    public void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(memberLoginInterceptor).addPathPatterns("/**");
//        WebMvcConfigurer.super.addInterceptors(registry);
//    }
//}
