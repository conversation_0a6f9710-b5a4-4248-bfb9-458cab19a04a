package com.knet.user.system.context;

import com.knet.user.model.entity.SysUser;

/**
 * <AUTHOR> zhangxuan
 * @Description: 会员用户信息上下文
 * @date 2025/02/16 10:41
 */
public class MemberUserContext {
    private static final ThreadLocal<SysUser> MEMBER_USER_INFO_DTO = new ThreadLocal();

    private MemberUserContext() {
        throw new UnsupportedOperationException();
    }

    public static SysUser getMemberUser() {
        return (SysUser) MEMBER_USER_INFO_DTO.get();
    }

    public static void setMemberUser(SysUser memberSysUserParam) {
        MEMBER_USER_INFO_DTO.set(memberSysUserParam);
    }

    public static void removeMemberUser() {
        MEMBER_USER_INFO_DTO.remove();
    }
}
