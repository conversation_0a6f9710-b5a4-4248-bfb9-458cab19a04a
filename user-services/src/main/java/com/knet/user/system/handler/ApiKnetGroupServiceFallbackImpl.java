package com.knet.user.system.handler;

import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.third.req.KnetAddressKeyFormat;
import com.knet.user.openfeign.ApiKnetGroupService;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/19 17:07
 * @description: kg 服务降级处理机制
 */
@Component
public class ApiKnetGroupServiceFallbackImpl implements ApiKnetGroupService {
    @Override
    public HttpResult<Boolean> verifyAddress(KnetAddressKeyFormat shipment) {
        return HttpResult.error("KG 地址验证服务暂时不可用，请稍后重试");
    }
}
