package com.knet.user.system.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.system.config.AuthConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static com.knet.common.constants.UserServicesConstants.KNET_USER_UID_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/2/17 11:00
 * @description: 随机数工具
 */
@Component
public class UserRandomStrUtil {

    @Resource
    private AuthConfig authConfig;


    /**
     * 获取随机数uid
     * (排除数字，只有4位字母)
     *
     * @return uid
     */
    public String getUidStr() {
        String uid = RandomUtil.randomStringWithoutStr(4, RandomUtil.BASE_NUMBER).toUpperCase();
        String redisKey = String.format(KNET_USER_UID_PREFIX, uid);
        if (RedisCacheUtil.hasKey(redisKey)) {
            return getUidStr();
        }
        //无过期时间
        RedisCacheUtil.set(redisKey, uid, -1);
        return uid;
    }

    /**
     * 密码加密
     *
     * @param password 密码加密
     * @return 加密后的密码
     */
    public String getEncryptPassword(String password) {
        String userSecretKey = authConfig.getUserSecretKey();
        return SecureUtil.md5(userSecretKey.concat(password));
    }
}
