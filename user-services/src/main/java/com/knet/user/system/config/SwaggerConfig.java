package com.knet.user.system.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI配置
 *
 * <AUTHOR>
 */
@Configuration
public class SwaggerConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("knet b2b user-services api")
                        .description("user-services api")
                        .version("1.0")
                        .contact(new Contact()
                                .name("knet b2b user-services api")
                                .url("http://knet")
                                .email("<EMAIL>")));
    }
}
