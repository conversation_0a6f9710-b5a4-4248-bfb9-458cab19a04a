package com.knet.user.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/5/28 11:00
 * @description: RabbitMQ配置 - 用户操作记录消费端
 */
@Configuration
public class RabbitConfig {

    /**
     * 用户服务专用死信交换机
     */
    @Bean
    public DirectExchange userDlxExchange() {
        return new DirectExchange("user-service.dlx", true, false);
    }

    /**
     * 用户服务用户操作死信队列
     */
    @Bean
    public Queue userOperationDlxQueue() {
        return QueueBuilder
                .durable("user-service.dlx.user-operation.queue")
                .build();
    }

    /**
     * 用户服务用户操作死信绑定
     */
    @Bean
    public Binding userOperationDlxBinding() {
        return BindingBuilder
                .bind(userOperationDlxQueue())
                .to(userDlxExchange())
                .with("user.user.operation.*");
    }

    /**
     * 用户操作记录交换机
     */
    @Bean
    public TopicExchange userOperationExchange() {
        return new TopicExchange("user-operation-exchange", true, false);
    }

    /**
     * 用户操作记录队列
     */
    @Bean
    public Queue userOperationQueue() {
        return QueueBuilder
                .durable("user-operation-queue.user-services")
                .withArgument("x-dead-letter-exchange", "user-service.dlx")
                .withArgument("x-dead-letter-routing-key", "user.user.operation.*")
                .build();
    }

    /**
     * 用户操作记录队列绑定
     */
    @Bean
    public Binding userOperationBinding() {
        return BindingBuilder
                .bind(userOperationQueue())
                .to(userOperationExchange())
                .with("user.operation.*");
    }
}
