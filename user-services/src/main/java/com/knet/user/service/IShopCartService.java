package com.knet.user.service;

import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.dto.req.RemoveFromCartRequest;
import com.knet.user.model.dto.req.ShopCartItemRequest;
import com.knet.user.model.dto.resp.CartResponse;
import com.knet.user.model.dto.resp.ShopCartEditInfoResponse;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 11:48
 * @description: 购物车服务定义
 */
public interface IShopCartService {
    /**
     * 添加商品到购物车
     *
     * @param request 添加商品请求
     * @return 购物车信息
     */
    CartResponse addToCart(AddToCartRequest request);

    /**
     * 从购物车移除商品
     *
     * @param request 移除商品请求
     */
    void removeFromCart(RemoveFromCartRequest request);

    /**
     * 获取购物车信息
     *
     * @param userId 用户ID
     * @return 购物车信息
     */
    CartResponse getCart(Long userId);


    /**
     * 填充购物车商品库存信息
     *
     * @param cartResponse 购物车信息
     * @return 购物车信息
     */
    CartResponse fillStockInfo(CartResponse cartResponse);

    /**
     * 获取购物车商品项信息-适用于购物车编辑
     *
     * @param request 请求
     * @return 购物车商品项信息
     */
    List<ShopCartEditInfoResponse> getShopCartItemForEdit(ShopCartItemRequest request);
}
