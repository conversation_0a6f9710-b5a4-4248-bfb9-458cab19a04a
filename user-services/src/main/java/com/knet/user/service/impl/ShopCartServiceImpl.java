package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.base.HttpResult;
import com.knet.common.context.UserContext;
import com.knet.common.enums.ProductStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.service.PricingStrategyService;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.dto.req.RemoveFromCartRequest;
import com.knet.user.model.dto.req.ShopCartItemRequest;
import com.knet.user.model.dto.resp.*;
import com.knet.user.model.dto.third.req.InnerKnetProductRequest;
import com.knet.user.model.dto.third.req.InnerProductDetailsQueryRequest;
import com.knet.user.model.dto.third.resp.ProductSkuSpecPriceDtoResp;
import com.knet.user.model.dto.third.resp.QueryKnetProductResp;
import com.knet.user.model.dto.third.resp.SpecPriceDto;
import com.knet.user.model.entity.SysShopCart;
import com.knet.user.model.entity.SysShopCartItem;
import com.knet.user.model.entity.SysShopCartSizeDetail;
import com.knet.user.openfeign.ApiGoodsServiceProvider;
import com.knet.user.service.IShopCartService;
import com.knet.user.service.ISysShopCartItemService;
import com.knet.user.service.ISysShopCartService;
import com.knet.user.service.ISysShopCartSizeDetailService;
import com.knet.user.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.knet.common.constants.UserServicesConstants.USER_SHOP_CART_CACHE_KEY_PREFIX;
import static com.knet.common.constants.UserServicesConstants.USER_SHOP_CART_CACHE_TIME;

/**
 * <AUTHOR>
 * @date 2025/5/21 11:49
 * @description: 购物车服务实现
 */
@Slf4j
@Service
public class ShopCartServiceImpl implements IShopCartService {
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ISysShopCartService iSysShopCartService;
    @Resource
    private ISysShopCartItemService iSysShopCartItemService;
    @Resource
    private ISysShopCartSizeDetailService iSysShopCartSizeDetailService;
    @Resource
    private ApiGoodsServiceProvider apiGoodsServiceProvider;
    @Resource
    private PricingStrategyService pricingStrategyService;

    @DistributedLock(key = "'addToCart:' + #request.hashCode()", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CartResponse addToCart(AddToCartRequest request) {
        log.info("添加商品到购物车, request: {}", request);
        try {
            SysShopCart cart = iSysShopCartService.getOrCreateCart(request.getUserId());
            SysShopCartItem cartItem = iSysShopCartItemService.getOrCreateShopCartItem(request, cart);
            // 3. 处理尺码明细记录
            for (AddToCartRequest.SizeDetailRequest sizeDetail : request.getSizeDetails()) {
                // 当数量为0时，删除该尺码明细记录
                if (0 == sizeDetail.getQuantity()) {
                    if (BeanUtil.isNotEmpty(sizeDetail.getCartItemId()) && BeanUtil.isNotEmpty(sizeDetail.getDetailId())) {
                        iSysShopCartSizeDetailService.removeSizeDetail(sizeDetail.getCartItemId(), sizeDetail.getDetailId());
                    }
                    continue;
                }
                // 判断是更新还是添加操作
                if (BeanUtil.isNotEmpty(sizeDetail.getCartItemId()) && BeanUtil.isNotEmpty(sizeDetail.getDetailId())) {
                    // 更新操作 - 直接根据detailId查询并验证，该明细是否属于指定的购物车商品项
                    SysShopCartSizeDetail existingSizeDetail = iSysShopCartSizeDetailService.getById(sizeDetail.getDetailId());
                    if (BeanUtil.isNotEmpty(existingSizeDetail) && existingSizeDetail.getCartItemId().equals(sizeDetail.getCartItemId())) {
                        iSysShopCartSizeDetailService.updateQuantity(existingSizeDetail.getId(), sizeDetail.getQuantity());
                    } else {
                        iSysShopCartSizeDetailService.createNewSizeDetail(cartItem.getId(), sizeDetail);
                    }
                } else {
                    // 添加操作 - 检查是否已存在相同尺码的明细
                    SysShopCartSizeDetail existingSizeDetail = iSysShopCartSizeDetailService.getSysShopCartSizeDetail(sizeDetail, cartItem.getId());
                    if (existingSizeDetail != null) {
                        iSysShopCartSizeDetailService.updateQuantity(existingSizeDetail.getId(), sizeDetail.getQuantity());
                    } else {
                        iSysShopCartSizeDetailService.createNewSizeDetail(cartItem.getId(), sizeDetail);
                    }
                }
            }
        } catch (Exception e) {
            log.error("添加商品到购物车失败, request: {}, 错误: {}", request, e.getMessage(), e);
            throw new ServiceException("添加商品到购物车失败,稍后再试");
        }
        RedisCacheUtil.del(String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, request.getUserId()));
        return getCart(request.getUserId());
    }

    @DistributedLock(key = "'removeFromCart:' + #request.hashCode()", expire = 1)
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeFromCart(RemoveFromCartRequest request) {
        log.info("从购物车移除商品, request: {}", request);
        SysShopCart cart = iSysShopCartService.getUserShopCart(request.getUserId());
        if (BeanUtil.isEmpty(cart)) {
            log.error("购物车不存在 用户id: {}", request.getUserId());
            return;
        }
        // 清空购物车
        if (Boolean.TRUE.equals(request.getClearAll())) {
            iSysShopCartService.deleteCart(request.getUserId());
        }
        // 从购物车移除指定商品
        if (Boolean.FALSE.equals(request.getClearAll())) {
            if (StrUtil.isNotBlank(request.getSku())) {
                SysShopCartItem cartItem = iSysShopCartItemService.getSysShopCartItem(request.getSku(), cart.getId());
                if (BeanUtil.isNotEmpty(cartItem)) {
                    // 删除 cartItem cartDetail
                    iSysShopCartSizeDetailService.removeSysShopCartSizeDetail(cartItem.getId());
                    iSysShopCartItemService.removeById(cartItem.getId());
                }
            }
            if (BeanUtil.isNotEmpty(request.getCartItemId())) {
                LambdaQueryWrapper<SysShopCartItem> itemQueryWrapper = new LambdaQueryWrapper<>();
                itemQueryWrapper
                        .eq(SysShopCartItem::getCartId, cart.getId())
                        .eq(SysShopCartItem::getId, request.getCartItemId());
                SysShopCartItem cartItem = iSysShopCartItemService.getOne(itemQueryWrapper);
                //操作商品是否存在
                if (BeanUtil.isNotEmpty(cartItem)) {
                    if (StrUtil.isBlank(request.getSize())) {
                        //删除 cartItem cartDetail
                        iSysShopCartSizeDetailService.removeSysShopCartSizeDetail(cartItem.getId());
                        iSysShopCartItemService.removeById(cartItem.getId());
                    } else {
                        iSysShopCartSizeDetailService.removeShopCartSizeDetailByItemIdAndSize(cartItem.getId(), request.getSize());
                    }
                }
                iSysShopCartItemService.remove(itemQueryWrapper);
            }
            String cacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, request.getUserId());
            RedisCacheUtil.del(cacheKey);
        }
    }

    @Override
    public CartResponse getCart(Long userId) {
        log.info("获取购物车信息, userId: {}", userId);
        String cacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, userId);
        Object cachedCart = RedisCacheUtil.get(cacheKey);
        if (cachedCart != null) {
            log.info("从缓存中获取购物车信息, userId: {}", userId);
            return (CartResponse) cachedCart;
        }
        log.info("从数据库中获取购物车信息, userId: {}", userId);
        List<CartQueryResult> cartDetails = iSysShopCartService.queryCartDetails(userId);
        if (CollUtil.isEmpty(cartDetails)) {
            CartResponse emptyCart = CartResponse.init(userId);
            // 缓存空购物车，设置较短的过期时间（5分钟）
            RedisCacheUtil.set(cacheKey, emptyCart, 300);
            return emptyCart;
        }
        //分 转换 美元，并应用价格策略
        cartDetails.forEach(detail -> {
            // 先转换为美元
            String originalPriceStr = PriceFormatUtil.formatCentsToYuan(detail.getUnitPrice());
            // 应用价格策略
            Long originalPriceCents = PriceFormatUtil.formatYuanToCents(originalPriceStr);
            Long strategyPriceCents = pricingStrategyService.applyPricingStrategy(originalPriceCents);
            String strategyPriceStr = PriceFormatUtil.formatCentsToYuan(strategyPriceCents);
            detail.setUnitPrice(strategyPriceStr);
        });
        Long cartId = cartDetails.get(0).getCartId();
        // 按商品项ID分组
        Map<Long, List<CartQueryResult>> itemMap = cartDetails.stream().collect(Collectors.groupingBy(CartQueryResult::getItemId));
        // 构建购物车商品项响应
        List<CartItemResponse> itemResponses = new ArrayList<>();
        int totalQuantity = 0;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal originalTotalAmount = BigDecimal.ZERO;
        // 遍历每个商品项
        for (Map.Entry<Long, List<CartQueryResult>> entry : itemMap.entrySet()) {
            Long itemId = entry.getKey();
            List<CartQueryResult> itemDetails = entry.getValue();
            if (CollUtil.isEmpty(itemDetails)) {
                // 如果商品项为空，跳过
                continue;
            }
            CartQueryResult goodInfos = itemDetails.get(0);
            List<SizeDetailResponse> sizeDetailResponses = new ArrayList<>();
            int itemTotalQuantity = 0;
            BigDecimal itemTotalAmount = BigDecimal.ZERO;
            BigDecimal itemOriginalTotalAmount = BigDecimal.ZERO;
            // 遍历每个尺码明细
            for (CartQueryResult detail : itemDetails) {
                // 如果尺码为空，跳过（可能是LEFT JOIN导致的NULL记录）
                if (detail.getSize() == null) {
                    continue;
                }
                // 计算策略价格小计
                double strategySubtotal = Double.parseDouble(detail.getUnitPrice()) * Long.valueOf(detail.getQuantity());

                // 计算原始价格小计
                Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(detail.getUnitPrice());
                Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
                String originalPriceStr = PriceFormatUtil.formatCentsToYuan(originalPriceCents);
                double originalSubtotal = Double.parseDouble(originalPriceStr) * Long.valueOf(detail.getQuantity());

                itemTotalQuantity += detail.getQuantity();
                itemTotalAmount = itemTotalAmount.add(BigDecimal.valueOf(strategySubtotal));
                itemOriginalTotalAmount = itemOriginalTotalAmount.add(BigDecimal.valueOf(originalSubtotal));

                SizeDetailResponse sizeDetailResponse = SizeDetailResponse.createSizeDetailResponse(detail, itemId);
                // 设置原始价格信息
                sizeDetailResponse.setOriginalUnitPrice(originalPriceStr);
                sizeDetailResponse.setOriginalSubtotal(PriceFormatUtil.formatPrice(originalSubtotal));
                sizeDetailResponses.add(sizeDetailResponse);
            }
            // 构建尺码明细聚合信息
            List<SizeDetailSumResponse> sizeDetailSums = buildSizeDetailSums(itemDetails);
            // 构建商品项响应
            CartItemResponse itemResponse = CartItemResponse.createCartItemResponse(
                    itemId,
                    cartId,
                    goodInfos.getProductName(),
                    goodInfos.getSku(),
                    goodInfos.getImageUrl(),
                    sizeDetailResponses,
                    sizeDetailSums,
                    itemTotalQuantity,
                    PriceFormatUtil.formatPrice(itemTotalAmount),
                    PriceFormatUtil.formatPrice(itemOriginalTotalAmount)
            );
            itemResponses.add(itemResponse);
            totalQuantity += itemTotalQuantity;
            totalAmount = totalAmount.add(itemTotalAmount);
            originalTotalAmount = originalTotalAmount.add(itemOriginalTotalAmount);
        }
        // 计算平均金额并格式化为两位小数
        BigDecimal avgPrice = totalQuantity > 0 ? totalAmount.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
        BigDecimal originalAvgPrice = totalQuantity > 0 ? originalTotalAmount.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;

        CartResponse cartResponse = CartResponse.builder()
                .cartId(cartId)
                .userId(userId)
                .items(itemResponses)
                .totalQuantity(totalQuantity)
                .totalAmount(PriceFormatUtil.formatPrice(totalAmount))
                .avgAmount(PriceFormatUtil.formatPrice(avgPrice))
                .originalTotalAmount(PriceFormatUtil.formatPrice(originalTotalAmount))
                .originalAvgAmount(PriceFormatUtil.formatPrice(originalAvgPrice))
                .build();
        RedisCacheUtil.set(cacheKey, cartResponse, USER_SHOP_CART_CACHE_TIME);
        return cartResponse;
    }


    @Override
    public CartResponse fillStockInfo(CartResponse cartResponse) {
        if (cartResponse == null || CollUtil.isEmpty(cartResponse.getItems())) {
            return cartResponse;
        }
        String accountFromToken = jwtUtil.getAccountFromToken(UserContext.getContext());
        for (CartItemResponse item : cartResponse.getItems()) {
            InnerKnetProductRequest request = new InnerKnetProductRequest();
            request.setSku(item.getSku());
            request.setStatus(ProductStatus.ON_SALE.getDescription());
            request.setAccount(accountFromToken);
            HttpResult<List<QueryKnetProductResp>> httpResult = apiGoodsServiceProvider.queryKnetProductsNoAuth(request);
            List<QueryKnetProductResp> resultData = httpResult.getData();
            if (CollUtil.isNotEmpty(resultData)) {
                // 按尺码分组库存信息
                Map<String, Integer> sizeStockMap = resultData.stream()
                        .filter(product -> StrUtil.isNotBlank(product.getSpec()))
                        .collect(Collectors.toMap(
                                QueryKnetProductResp::getSpec,
                                product -> 1,
                                Integer::sum
                        ));
                // 更新尺码明细聚合信息中的库存数据
                Optional.ofNullable(item.getSizeDetailSums())
                        .filter(CollUtil::isNotEmpty)
                        .ifPresent(sums -> sums
                                .forEach(sizeDetailSum ->
                                        sizeDetailSum.setStock(sizeStockMap.getOrDefault(sizeDetailSum.getSize(), 0))));
            }
        }
        return cartResponse;
    }

    @Override
    public List<ShopCartEditInfoResponse> getShopCartItemForEdit(ShopCartItemRequest request) {
        String accountFromToken = jwtUtil.getAccountFromToken(UserContext.getContext());
        InnerProductDetailsQueryRequest innerRequest = new InnerProductDetailsQueryRequest(request.getSku(), accountFromToken);
        HttpResult<List<ProductSkuSpecPriceDtoResp>> listHttpResult = apiGoodsServiceProvider.queryProductDetails(innerRequest);
        List<ProductSkuSpecPriceDtoResp> resultData = listHttpResult.getData();
        if (CollUtil.isNotEmpty(resultData)) {
            List<ShopCartEditInfoResponse> responses = resultData.stream()
                    .map(item -> {
                        List<ShopCartSizeDetailResponse> details = item.getPriceInfo().stream()
                                .map(SpecPriceDto::create)
                                .toList();
                        return ShopCartEditInfoResponse.create(item, details);
                    }).toList();
            List<CartQueryResult> cartDetails = iSysShopCartService.queryCartDetails(request.getUserId());
            if (CollUtil.isNotEmpty(cartDetails)) {
                for (ShopCartEditInfoResponse response : responses) {
                    for (CartQueryResult detail : cartDetails) {
                        if (response.getSku().equals(detail.getSku()) && response.getSize().equals(detail.getSize())) {
                            for (ShopCartSizeDetailResponse sizeDetail : response.getDetails()) {
                                // 需要将策略价格转换为原始价格进行匹配
                                // sizeDetail.getUnitPrice() 是策略价格的美分值字符串
                                // detail.getUnitPrice() 是原始价格的美分值字符串
                                if (sizeDetail.getSize().equals(detail.getSize())) {
                                    Long strategyPriceCents = PriceFormatUtil.formatYuanToCents(sizeDetail.getUnitPrice());
                                    Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
                                    String originalPriceStr = String.valueOf(originalPriceCents);
                                    if (originalPriceStr.equals(detail.getUnitPrice())) {
                                        sizeDetail.setCartItemId(detail.getItemId());
                                        sizeDetail.setDetailId(detail.getDetailId());
                                        sizeDetail.setSelected(detail.getDetailSelected());
                                        sizeDetail.setQuantity(detail.getQuantity());
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return responses;
        }
        return Collections.emptyList();
    }

    /**
     * 构建尺码明细聚合信息
     *
     * @param itemDetails 商品项详情列表
     * @return 尺码明细聚合信息列表
     */
    private List<SizeDetailSumResponse> buildSizeDetailSums(List<CartQueryResult> itemDetails) {
        Map<String, List<CartQueryResult>> sizeMap = itemDetails.stream()
                .filter(detail -> detail.getSize() != null)
                .collect(Collectors.groupingBy(CartQueryResult::getSize));
        List<SizeDetailSumResponse> sizeDetailSums = new ArrayList<>();
        // 遍历每个尺码组
        for (Map.Entry<String, List<CartQueryResult>> entry : sizeMap.entrySet()) {
            String size = entry.getKey();
            List<CartQueryResult> sizeDetails = entry.getValue();
            // 计算该尺码的总数量和总金额
            int totalQuantity = sizeDetails.stream().mapToInt(CartQueryResult::getQuantity).sum();
            BigDecimal totalPrice = sizeDetails.stream()
                    .map(detail -> {
                        BigDecimal unitPrice = new BigDecimal(detail.getUnitPrice());
                        BigDecimal quantity = BigDecimal.valueOf(detail.getQuantity());
                        return unitPrice.multiply(quantity);
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算平均价格并格式化为两位小数
            BigDecimal avgPrice = totalQuantity > 0 ? totalPrice.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO;
            SizeDetailSumResponse sizeDetailSum = new SizeDetailSumResponse(
                    sizeDetails.get(0).getItemId(),
                    size,
                    totalQuantity,
                    0,
                    PriceFormatUtil.formatPrice(avgPrice),
                    PriceFormatUtil.formatPrice(totalPrice)
            );
            sizeDetailSums.add(sizeDetailSum);
        }
        return sizeDetailSums;
    }
}
