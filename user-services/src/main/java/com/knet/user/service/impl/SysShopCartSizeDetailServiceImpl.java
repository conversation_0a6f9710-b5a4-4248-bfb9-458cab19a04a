package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.service.PricingStrategyService;
import com.knet.user.mapper.SysShopCartSizeDetailMapper;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.entity.SysShopCartSizeDetail;
import com.knet.user.service.ISysShopCartSizeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【sys_shop_cart_size_detail(购物车尺码明细表)】的数据库操作Service实现
 * @date 2025-05-21 10:14:37
 */
@Slf4j
@Service
public class SysShopCartSizeDetailServiceImpl extends ServiceImpl<SysShopCartSizeDetailMapper, SysShopCartSizeDetail> implements ISysShopCartSizeDetailService {

    @Resource
    private SysShopCartSizeDetailMapper sysShopCartSizeDetailMapper;
    @Resource
    private PricingStrategyService pricingStrategyService;

    /**
     * 获取或购物车尺码明细记录
     * (SIZE,UNIT_PRICE 视为一条记录)
     *
     * @param sizeDetail 添加购物车请求中的尺码明细
     * @param cartItemId 购物车商品项id
     * @return 购物车尺码明细记录
     */
    @Override
    public SysShopCartSizeDetail getSysShopCartSizeDetail(AddToCartRequest.SizeDetailRequest sizeDetail, Long cartItemId) {
        // sizeDetail.getUnitPrice()已经是策略价格的美分字符串，直接转换为原始价格进行查询
        Long strategyPriceCents = Long.parseLong(sizeDetail.getUnitPrice());
        Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);
        log.debug("购物车查询价格转换: 策略价格={}美分, 原始价格={}美分", strategyPriceCents, originalPriceCents);
        LambdaQueryWrapper<SysShopCartSizeDetail> sizeDetailQueryWrapper = new LambdaQueryWrapper<>();
        sizeDetailQueryWrapper
                .eq(SysShopCartSizeDetail::getCartItemId, cartItemId)
                .eq(SysShopCartSizeDetail::getSize, sizeDetail.getSize())
                .eq(SysShopCartSizeDetail::getUnitPrice, originalPriceCents)
                .eq(SysShopCartSizeDetail::getSelected, 1);
        return this.getOne(sizeDetailQueryWrapper);
    }

    /**
     * 创建新的购物车尺码明细
     *
     * @param cartItemId 购物车商品项ID
     * @param sizeDetail 尺码明细请求
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createNewSizeDetail(Long cartItemId, AddToCartRequest.SizeDetailRequest sizeDetail) {
        // sizeDetail.getUnitPrice()已经是策略价格的美分字符串，直接转换为原始价格保存到数据库
        Long strategyPriceCents = Long.parseLong(sizeDetail.getUnitPrice());
        Long originalPriceCents = pricingStrategyService.removePricingStrategy(strategyPriceCents);

        log.info("购物车保存价格转换: 策略价格={}美分, 原始价格={}美分", strategyPriceCents, originalPriceCents);

        SysShopCartSizeDetail newDetail = SysShopCartSizeDetail.builder()
                .cartItemId(cartItemId)
                .size(sizeDetail.getSize())
                .quantity(sizeDetail.getQuantity())
                .unitPrice(originalPriceCents)
                .selected(1)
                .build();
        this.save(newDetail);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeSizeDetail(Long cartItemId, Long detailId) {
        LambdaQueryWrapper<SysShopCartSizeDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysShopCartSizeDetail::getId, detailId)
                .eq(SysShopCartSizeDetail::getCartItemId, cartItemId);
        this.remove(queryWrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateQuantity(Long detailId, Integer quantity) {
        LambdaUpdateWrapper<SysShopCartSizeDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysShopCartSizeDetail::getId, detailId)
                .set(SysShopCartSizeDetail::getQuantity, quantity);
        this.update(null, updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByCartId(Long cartId) {
        sysShopCartSizeDetailMapper.removeByCartId(cartId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeSysShopCartSizeDetail(Long cartItemId) {
        if (cartItemId != null) {
            LambdaQueryWrapper<SysShopCartSizeDetail> sizeDetailQueryWrapper = new LambdaQueryWrapper<>();
            sizeDetailQueryWrapper.eq(SysShopCartSizeDetail::getCartItemId, cartItemId);
            this.remove(sizeDetailQueryWrapper);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeShopCartSizeDetailByItemIdAndSize(Long cartItemId, String size) {
        LambdaQueryWrapper<SysShopCartSizeDetail> sizeDetailQueryWrapper = new LambdaQueryWrapper<>();
        sizeDetailQueryWrapper
                .eq(BeanUtil.isNotEmpty(cartItemId), SysShopCartSizeDetail::getCartItemId, cartItemId)
                .eq(StrUtil.isNotEmpty(size), SysShopCartSizeDetail::getSize, size);
        this.remove(sizeDetailQueryWrapper);
    }
}




