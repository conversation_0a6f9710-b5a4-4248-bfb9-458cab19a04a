package com.knet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.knet.user.mapper.SysUserRolerRelMapper;
import com.knet.user.model.entity.SysUserRolerRel;
import com.knet.user.service.IUserRoleOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description: 用户角色操作服务实现 - 用于解决循环依赖
 */
@Slf4j
@Service
public class UserRoleOperationServiceImpl implements IUserRoleOperationService {

    @Resource
    private SysUserRolerRelMapper sysUserRolerRelMapper;

    @Override
    public long countUsersByRoleId(Long rolerId) {
        log.info("统计角色关联用户数量: rolerId={}", rolerId);
        LambdaQueryWrapper<SysUserRolerRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRolerRel::getRolerId, rolerId);
        return sysUserRolerRelMapper.selectCount(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeUserRolesByRoleId(Long rolerId) {
        log.info("删除角色关联的用户关系: rolerId={}", rolerId);
        LambdaQueryWrapper<SysUserRolerRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRolerRel::getRolerId, rolerId);
        sysUserRolerRelMapper.delete(queryWrapper);
        log.info("删除角色关联的用户关系完成: rolerId={}", rolerId);
    }
}
