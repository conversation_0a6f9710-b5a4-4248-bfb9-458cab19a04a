package com.knet.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.user.mapper.SysRolerPermissionRelMapper;
import com.knet.user.model.entity.SysRolerPermissionRel;
import com.knet.user.service.ISysRolerPermissionRelService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:48
 * @description:
 */
@Service
public class SysRolerPermissionRelServiceImpl extends ServiceImpl<SysRolerPermissionRelMapper, SysRolerPermissionRel> implements ISysRolerPermissionRelService {
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByRolerId(Long rolerId) {
        LambdaQueryWrapper<SysRolerPermissionRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysRolerPermissionRel::getRolerId, rolerId);
        this.remove(queryWrapper);
    }
}
