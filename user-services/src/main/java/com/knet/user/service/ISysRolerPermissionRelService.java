package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.entity.SysRolerPermissionRel;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:46
 * @description: 角色权限关系服务
 */
public interface ISysRolerPermissionRelService extends IService<SysRolerPermissionRel> {
    /**
     * 根据角色id删除角色权限关系
     *
     * @param rolerId 角色id
     */
    void removeByRolerId(Long rolerId);
}
