package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.entity.SysShopCartSizeDetail;

/**
 * <AUTHOR>
 * @description 针对表【sys_shop_cart_size_detail(购物车尺码明细表)】的数据库操作Service
 * @date 2025-05-21 10:14:37
 */
public interface ISysShopCartSizeDetailService extends IService<SysShopCartSizeDetail> {

    /**
     * 获取或购物车尺码明细记录
     *
     * @param sizeDetail 添加购物车请求中的尺码明细
     * @param cartItemId 购物车商品项id
     * @return 购物车尺码明细记录
     */
    SysShopCartSizeDetail getSysShopCartSizeDetail(AddToCartRequest.SizeDetailRequest sizeDetail, Long cartItemId);

    /**
     * 创建新的购物车尺码明细
     *
     * @param cartItemId 购物车商品项ID
     * @param sizeDetail 尺码明细请求
     */
    void createNewSizeDetail(Long cartItemId, AddToCartRequest.SizeDetailRequest sizeDetail);

    /**
     * 删除购物车尺码明细
     *
     * @param cartItemId 购物车商品项ID
     * @param detailId   购物车尺码明细ID
     */
    void removeSizeDetail(Long cartItemId, Long detailId);

    /**
     * 更新购物车尺码明细数量
     *
     * @param detailId 购物车尺码明细ID
     * @param quantity 数量
     */
    void updateQuantity(Long detailId, Integer quantity);

    /**
     * 根据购物车ID删除购物车尺码明细
     *
     * @param cartId 购物车ID
     */
    void removeByCartId(Long cartId);

    /**
     * 根据cartItem id 删除所有尺码明细
     *
     * @param cartItemId 购物车商品项ID
     */
    void removeSysShopCartSizeDetail(Long cartItemId);

    /**
     * 根据cartItem id 和 size 删除尺码明细
     *
     * @param cartItemId 购物车商品项ID
     * @param size       尺码
     */
    void removeShopCartSizeDetailByItemIdAndSize(Long cartItemId, String size);
}
