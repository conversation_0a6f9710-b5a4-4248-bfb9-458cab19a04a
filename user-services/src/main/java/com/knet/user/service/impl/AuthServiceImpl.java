package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.model.dto.req.AuthBaseRequest;
import com.knet.user.model.dto.req.AuthUserRequest;
import com.knet.user.model.entity.SysUser;
import com.knet.user.service.IAuthService;
import com.knet.user.service.ISysUserRolerRelService;
import com.knet.user.service.ISysUserService;
import com.knet.user.system.utils.JwtUtil;
import com.knet.user.system.utils.UserRandomStrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static com.knet.common.constants.UserServicesConstants.*;

/**
 * <AUTHOR>
 * @date 2025/2/13 14:35
 * @description: 用户授权服务实现
 */
@Slf4j
@Service
public class AuthServiceImpl implements IAuthService {

    @Resource
    private ISysUserService userService;
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private UserRandomStrUtil userRandomStrUtil;
    @Resource
    private ISysUserRolerRelService iSysUserRolerRelService;
    @Resource
    private DefaultKaptcha defaultKaptcha;

    @Override
    public boolean auth(AuthBaseRequest request) {
        return true;
    }

    @Override
    public String login(AuthUserRequest request) {
        checkCaptcha(request.getCode());
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(true, SysUser::getAccount, request.getAccount())
                .eq(true, SysUser::getPassword, userRandomStrUtil.getEncryptPassword(request.getPassword()))
                .eq(true, SysUser::getStatus, 1);
        SysUser sysUser = userService.getOne(queryWrapper);
        if (BeanUtil.isNotEmpty(sysUser)) {
            String generateToken = jwtUtil.generateToken(sysUser, iSysUserRolerRelService.getRolerByUserId(sysUser.getId()));
            String redisKey = String.format(KNET_USER_TOKEN_PREFIX, sysUser.getId());
            RedisCacheUtil.setIfAbsent(redisKey, generateToken, KNET_USER_TOKEN_EXPIRED_TIME);
            RedisCacheUtil.expire(redisKey, KNET_USER_TOKEN_EXPIRED_TIME);
            return generateToken;
        }
        return "";
    }

    @Override
    public void logout(String token, String account) {
        if (StrUtil.isBlank(token) && StrUtil.isBlank(account)) {
            log.error("token或用户名为空");
            return;
        }
        jwtUtil.validateToken(token);
        if (!jwtUtil.validateToken(token)) {
            log.error("token无效");
            return;
        }
        String userIdFromToken = jwtUtil.getUserIdFromToken(token);
        SysUser sysUser = userService.getById(userIdFromToken);
        if (BeanUtil.isEmpty(sysUser)) {
            log.error("用户不存在");
            return;
        }
        if (!sysUser.getAccount().equals(account)) {
            log.error("用户信息不匹配");
            return;
        }
        String redisKey = String.format(KNET_USER_TOKEN_PREFIX, sysUser.getId());
        RedisCacheUtil.del(redisKey);
    }

    @Override
    public ResponseEntity<byte[]> getCaptcha(HttpServletRequest req) {
        try {
            String capText = defaultKaptcha.createText();
            String key = String.format(KNET_CAPTCHA_PREFIX, capText);
            boolean set = RedisCacheUtil.set(key, capText, 5 * 60);
            if (!set) {
                throw new RuntimeException("Failed to save captcha to Redis");
            }
            BufferedImage bi = defaultKaptcha.createImage(capText);
            ByteArrayOutputStream bass = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", bass);
            byte[] imageBytes = bass.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_JPEG);
            headers.setCacheControl("no-store, no-cache");
            headers.setContentLength(imageBytes.length);
            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);
        } catch (IOException e) {
            log.error("Failed to generate captcha", e);
            throw new RuntimeException("Failed to generate captcha", e);
        }
    }

    /**
     * 校验图形验证码
     *
     * @param validCode 输入的验证码
     */
    public void checkCaptcha(String validCode) {
        String key = String.format(KNET_CAPTCHA_PREFIX, validCode);
        String code = (String) RedisCacheUtil.get(key);
        if (ObjectUtils.isEmpty(code)) {
            throw new ServiceException("验证码失效");
        }
        if (!StrUtil.equals(code, validCode)) {
            throw new ServiceException("验证码错误，请重试");
        }
        RedisCacheUtil.del(key);
    }
}
