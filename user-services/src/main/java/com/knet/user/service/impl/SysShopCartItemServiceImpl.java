package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.user.mapper.SysShopCartItemMapper;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.entity.SysShopCart;
import com.knet.user.model.entity.SysShopCartItem;
import com.knet.user.service.ISysShopCartItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 针对表【sys_shop_cart_item(购物车商品项表)】的数据库操作Service实现
 * @date 2025-05-21 10:12:45
 */
@Slf4j
@Service
public class SysShopCartItemServiceImpl extends ServiceImpl<SysShopCartItemMapper, SysShopCartItem> implements ISysShopCartItemService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysShopCartItem getOrCreateShopCartItem(AddToCartRequest request, SysShopCart cart) {
        LambdaQueryWrapper<SysShopCartItem> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper
                .eq(SysShopCartItem::getCartId, cart.getId())
                .eq(SysShopCartItem::getSku, request.getSku());
        SysShopCartItem cartItem = this.getOne(itemQueryWrapper);
        // 如果不存在相同商品，则创建新的商品项
        if (BeanUtil.isEmpty(cartItem)) {
            cartItem = SysShopCartItem.builder()
                    .cartId(cart.getId())
                    .sku(request.getSku())
                    .productName(request.getProductName())
                    .imageUrl(request.getImageUrl())
                    .selected(1)
                    .build();
            this.save(cartItem);
        }
        return cartItem;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeByCartId(Long cartId) {
        LambdaQueryWrapper<SysShopCartItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysShopCartItem::getCartId, cartId);
        this.remove(queryWrapper);
    }

    @Override
    public SysShopCartItem getSysShopCartItem(String sku, Long cartId) {
        LambdaQueryWrapper<SysShopCartItem> itemQueryWrapper = new LambdaQueryWrapper<>();
        itemQueryWrapper
                .eq(SysShopCartItem::getSku, sku)
                .eq(SysShopCartItem::getCartId, cartId);
        return this.getOne(itemQueryWrapper);
    }
}




