package com.knet.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.user.model.dto.req.UserQueryRequest;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import com.knet.user.model.entity.SysUser;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description: 用户查询服务接口 - 用于解决循环依赖
 */
public interface IUserQueryService {

    /**
     * 查询用户列表
     *
     * @param request 查询用户请求体
     * @return 返回用户列表
     */
    IPage<UserInfoDtoResp> listUser(UserQueryRequest request);

    /**
     * 根据用户ID查询用户
     *
     * @param id 用户ID
     * @return 用户实体
     */
    SysUser getUserById(Long id);
}
