package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.mapper.SysShopCartMapper;
import com.knet.user.model.dto.resp.CartQueryResult;
import com.knet.user.model.entity.SysShopCart;
import com.knet.user.service.ISysShopCartItemService;
import com.knet.user.service.ISysShopCartService;
import com.knet.user.service.ISysShopCartSizeDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.knet.common.constants.UserServicesConstants.USER_SHOP_CART_CACHE_KEY_PREFIX;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:40
 * @description: 购物车服务实现类
 */
@Slf4j
@Service
public class SysShopCartServiceImpl extends ServiceImpl<SysShopCartMapper, SysShopCart> implements ISysShopCartService {

    @Resource
    private ISysShopCartItemService iSysShopCartItemService;
    @Resource
    private ISysShopCartSizeDetailService iSysShopCartSizeDetailService;

    /**
     * 获取或创建购物车
     *
     * @param userId 用户ID
     * @return 购物车
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysShopCart getOrCreateCart(Long userId) {
        LambdaQueryWrapper<SysShopCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysShopCart::getUserId, userId);
        SysShopCart cart = this.getOne(queryWrapper);
        if (BeanUtil.isEmpty(cart)) {
            cart = new SysShopCart();
            cart.setUserId(userId);
            this.save(cart);
        }
        return cart;
    }

    @Override
    public List<CartQueryResult> queryCartDetails(Long userId) {
        if (BeanUtil.isEmpty(userId)) {
            return Collections.emptyList();
        }
        return baseMapper.queryCartDetails(userId);
    }


    /**
     * 删除购物车
     *
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteCart(Long userId) {
        log.info("删除购物车, userId: {}", userId);
        if (userId == null) {
            log.warn("删除购物车失败，userId为空");
            return;
        }
        try {
            LambdaQueryWrapper<SysShopCart> cartQueryWrapper = new LambdaQueryWrapper<>();
            cartQueryWrapper.eq(SysShopCart::getUserId, userId);
            SysShopCart cart = this.getOne(cartQueryWrapper);
            if (BeanUtil.isEmpty(cart)) {
                log.info("用户没有购物车，无需删除, userId: {}", userId);
                return;
            }
            this.removeById(cart);
            iSysShopCartItemService.removeByCartId(cart.getId());
            iSysShopCartSizeDetailService.removeByCartId(cart.getId());
            cleanShopCartCache(userId);
            log.info("成功删除购物车, userId: {}, cartId: {}", userId, cart.getId());
        } catch (Exception e) {
            log.error("删除购物车失败, userId: {}, 错误信息: {}", userId, e.getMessage(), e);
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public SysShopCart getUserShopCart(Long userId) {
        if (userId == null) {
            log.warn("获取购物车失败，userId为空");
            return null;
        }
        LambdaQueryWrapper<SysShopCart> cartQueryWrapper = new LambdaQueryWrapper<>();
        cartQueryWrapper
                .eq(SysShopCart::getUserId, userId)
                .eq(SysShopCart::getDelFlag, 0)
                .last("limit 1");
        SysShopCart cart = this.getOne(cartQueryWrapper);
        if (cart == null) {
            log.warn("购物车不存在 用户id: {}", userId);
        }
        return cart;
    }

    /**
     * 清除用户购物车相关数据的缓存
     *
     * @param userId 用户ID
     */
    private void cleanShopCartCache(Long userId) {
        String cacheKey = String.format(USER_SHOP_CART_CACHE_KEY_PREFIX, userId);
        RedisCacheUtil.del(cacheKey);
        String itemCachePattern = "knet:b2b:user_shop_cart_item:" + userId + ":*:*";
        RedisCacheUtil.deleteByPattern(itemCachePattern);
        log.info("成功删除购物车及清除缓存, userId: {}", userId);
    }
}
