package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.entity.SysShopCart;
import com.knet.user.model.entity.SysShopCartItem;

/**
 * <AUTHOR>
 * @description 针对表【sys_shop_cart_item(购物车商品项表)】的数据库操作Service
 * @date 2025-05-21 10:12:45
 */
public interface ISysShopCartItemService extends IService<SysShopCartItem> {
    /**
     * 获取或创建购物车商品项
     *
     * @param request 添加商品请求
     * @param cart    购物车
     * @return 购物车商品项
     */
    SysShopCartItem getOrCreateShopCartItem(AddToCartRequest request, SysShopCart cart);

    /**
     * 根据购物车商品项ID删除购物车商品项
     *
     * @param cartId 购物车ID
     */
    void removeByCartId(Long cartId);

    /**
     * 根据购物车商品项ID和商品SKU获取购物车商品项
     *
     * @param sku    sku
     * @param cartId 购物车ID
     * @return 购物车商品项
     */
    SysShopCartItem getSysShopCartItem(String sku, Long cartId);
}
