package com.knet.user.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.knet.user.mapper.SysUserMapper;
import com.knet.user.model.dto.req.UserQueryRequest;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import com.knet.user.model.entity.SysUser;
import com.knet.user.service.IUserQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description: 用户查询服务实现 - 用于解决循环依赖
 */
@Slf4j
@Service
public class UserQueryServiceImpl implements IUserQueryService {

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public IPage<UserInfoDtoResp> listUser(UserQueryRequest request) {
        log.info("查询用户列表: {}", request);
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(request.getAccount())) {
            queryWrapper.likeRight(SysUser::getAccount, request.getAccount());
        }
        Page<SysUser> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SysUser> userPage = sysUserMapper.selectPage(page, queryWrapper);
        return userPage.convert(SysUser::mapToUserInfoDtoResp);
    }

    @Override
    public SysUser getUserById(Long id) {
        log.info("根据ID查询用户: id={}", id);
        return sysUserMapper.selectById(id);
    }
}
