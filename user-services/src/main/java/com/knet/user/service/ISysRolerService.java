package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.RolerSaveRequest;
import com.knet.user.model.entity.SysRoler;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:43
 * @description: 角色接口
 */
public interface ISysRolerService extends IService<SysRoler> {

    /**
     * 查询角色列表(读取缓存)
     *
     * @return
     */
    List<SysRoler> queryListFromCache();

    /**
     * 创建角色
     *
     * @param request 创建角色请求体
     * @return 是否创建成功
     */
    SysRoler createRoler(RolerSaveRequest request);

    /**
     * 删除角色
     *
     * @param id 角色id
     */
    void deleteRoler(Long id);
}
