package com.knet.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import com.knet.user.model.dto.req.CreateUserOperationRecordRequest;
import com.knet.user.model.dto.req.UserRechargeQueryRequest;
import com.knet.user.model.entity.SysUserOperationRecord;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_operation_record(用户操作记录表)】的数据库操作Service
 * @date 2025-05-20 18:35:00
 */
public interface ISysUserOperationRecordService extends IService<SysUserOperationRecord> {

    /**
     * 创建用户操作记录
     *
     * @param request 创建请求
     * @return 操作记录ID
     */
    String createOperationRecord(CreateUserOperationRecordRequest request);

    /**
     * 创建用户操作记录（简化版本）
     *
     * @param userId          用户ID
     * @param operatorId      操作者ID
     * @param operatorType    操作者类型
     * @param operationType   操作类型
     * @param operationResult 操作结果
     * @param operationDesc   操作描述
     * @param businessId      关联业务ID
     * @param businessType    关联业务类型
     * @return 操作记录ID
     */
    String createOperationRecord(Long userId, Long operatorId, String operatorType,
                                 UserOperationType operationType, OperationResult operationResult,
                                 String operationDesc, String businessId, String businessType);

    /**
     * 创建用户操作记录（最简版本）
     *
     * @param userId          用户ID
     * @param operationType   操作类型
     * @param operationResult 操作结果
     * @param operationDesc   操作描述
     * @return 操作记录ID
     */
    String createOperationRecord(Long userId, UserOperationType operationType,
                                 OperationResult operationResult, String operationDesc);

    /**
     * 查询用户操作记录
     *
     * @param request 查询请求
     * @return 操作记录列表
     */
    IPage<SysUserOperationRecord> queryList(UserRechargeQueryRequest request);
}
