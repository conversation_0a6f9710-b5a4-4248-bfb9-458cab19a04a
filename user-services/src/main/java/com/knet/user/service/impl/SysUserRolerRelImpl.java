package com.knet.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.user.mapper.SysUserRolerRelMapper;
import com.knet.user.model.entity.SysRoler;
import com.knet.user.model.entity.SysUserRolerRel;
import com.knet.user.service.ISysRolerService;
import com.knet.user.service.ISysUserRolerRelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:17
 * @description: 用户角色关联实现
 */
@Slf4j
@Service
public class SysUserRolerRelImpl extends ServiceImpl<SysUserRolerRelMapper, SysUserRolerRel> implements ISysUserRolerRelService {
    @Resource
    private ISysRolerService rolerService;

    @Override
    public SysRoler getRolerByUserId(Long userId) {
        LambdaQueryWrapper<SysUserRolerRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(true, SysUserRolerRel::getUserId, userId);
        SysUserRolerRel userRolerRel = this.getOne(queryWrapper);
        if (BeanUtil.isNotEmpty(userRolerRel)) {
            return rolerService.getById(userRolerRel.getRolerId());
        }
        log.error("用户角色关系不存在, userId: {}", userId);
        return new SysRoler();
    }

    @Override
    public long countUserRolerRelById(Long rolerId) {
        LambdaQueryWrapper<SysUserRolerRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRolerRel::getRolerId, rolerId);
        return this.count(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByRolerId(Long rolerId) {
        LambdaQueryWrapper<SysUserRolerRel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserRolerRel::getRolerId, rolerId);
        this.remove(queryWrapper);
    }
}
