package com.knet.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.entity.SysRoler;
import com.knet.user.model.entity.SysUserRolerRel;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:16
 * @description: 用户角色关系
 */
public interface ISysUserRolerRelService extends IService<SysUserRolerRel> {
    /**
     * 根据用户id获取角色
     *
     * @param userId 用户id
     * @return 角色
     */
    SysRoler getRolerByUserId(Long userId);

    /**
     * 根据角色id获取用户数量
     *
     * @param rolerId 角色id
     * @return 用户数量
     */
    long countUserRolerRelById(Long rolerId);

    /**
     * 根据角色id删除用户角色关系
     *
     * @param rolerId 角色id
     */
    void removeByRolerId(Long rolerId);
}
