package com.knet.user.service;

/**
 * <AUTHOR>
 * @date 2025/7/24
 * @description: 用户角色操作服务接口 - 用于解决循环依赖
 */
public interface IUserRoleOperationService {

    /**
     * 根据角色id获取用户数量
     *
     * @param rolerId 角色id
     * @return 用户数量
     */
    long countUsersByRoleId(Long rolerId);

    /**
     * 根据角色id删除用户角色关系
     *
     * @param rolerId 角色id
     */
    void removeUserRolesByRoleId(Long rolerId);
}
