package com.knet.user.model.dto.req;

import com.knet.common.base.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:30
 * @description: UserRechargeQueryRequest
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserRechargeQueryRequest extends BasePageRequest {

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String account;

    @Schema(description = "钱包充值/扣款类型 查询全部不传值", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "WALLET_RECHARGE WALLET_DEDUCT")
    private String type;
}
