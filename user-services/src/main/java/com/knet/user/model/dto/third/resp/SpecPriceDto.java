package com.knet.user.model.dto.third.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.knet.common.base.BaseRequest;
import com.knet.common.config.PriceIntegerSerializer;
import com.knet.user.model.dto.resp.ShopCartSizeDetailResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/4 11:54
 * @description: 尺码价格dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SpecPriceDto extends BaseRequest {
    @Schema(description = "尺码,不展示用作业务匹配")
    private String spec;

    @Schema(description = "价格（单位：美元）", example = "10")
    @JsonSerialize(using = PriceIntegerSerializer.class)
    private String price;

    @Schema(description = "原始价格（单位：美元）", example = "10")
    @JsonSerialize(using = PriceIntegerSerializer.class)
    private String originalPrice;

    @Schema(description = "数量")
    private Integer qty;


    public static ShopCartSizeDetailResponse create(SpecPriceDto info) {
        return ShopCartSizeDetailResponse.builder()
                .size(info.getSpec())
                .unitPrice(info.getPrice())
                .stock(info.getQty())
                .quantity(0)
                .selected(0)
                .build();
    }
}
