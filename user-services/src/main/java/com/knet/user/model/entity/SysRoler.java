package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.RoleStatus;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/12 17:28
 * @description: 角色表
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_roler", description = "角色表")
@TableName("sys_roler")
public class SysRoler extends BaseEntity {
    @Schema(description = "角色code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    /**
     * @see RoleStatus 角色状态
     */
    @Builder.Default
    @Schema(description = "角色状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private RoleStatus status = RoleStatus.ENABLE;
}
