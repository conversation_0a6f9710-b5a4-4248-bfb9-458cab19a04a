package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/5/21 14:30
 * @description: 购物车商品项表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "购物车商品项表")
@TableName("sys_shop_cart_item")
public class SysShopCartItem extends BaseEntity {

    @Schema(description = "购物车ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long cartId;

    @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    @Schema(description = "商品图片URL")
    private String imageUrl;

    @Schema(description = "是否选中：0-未选中，1-已选中", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer selected;
}