package com.knet.user.model.dto.third.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.ProductStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/4/14 10:53
 * @description: 查询knet商品数据接口返回体
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryKnetProductResp extends BaseResponse {

    @Schema(description = "knet_product唯一标识符")
    private String listingId;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date updateTime;

    @Schema(description = "knet唯一标识符")
    private String oneId;

    @Schema(description = "sku")
    private String sku;

    @Schema(description = "尺码")
    private String spec;

    @Schema(description = "价格（单位：美分）", example = "1075")
    private String price;
    /**
     * @see ProductStatus
     */
    @Schema(description = "商品状态 上下架")
    private String status;
}
