package com.knet.user.model.dto.req;

import com.knet.common.base.BasePageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/2/17 09:57
 * @description: 用户地址查询请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserAddressQueryRequest extends BasePageRequest {
    @Schema(description = "userId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Long userId;
}
