package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:10
 * @description: 用户角色关系表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sysUserRolerRel", description = "用户角色关系表")
@TableName("sys_user_roler_rel")
public class SysUserRolerRel extends BaseEntity {
    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;
    @Schema(description = "角色id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long rolerId;
}
