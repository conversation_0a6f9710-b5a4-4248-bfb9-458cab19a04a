package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/5/21 14:40
 * @description: 尺码明细实体
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "购物车尺码明细")
@TableName("sys_shop_cart_size_detail")
public class SysShopCartSizeDetail extends BaseEntity {

    /**
     * 购物车商品项ID
     * (SIZE,UNIT_PRICE 视为一条记录)
     */
    @Schema(description = "购物车商品项ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long cartItemId;

    @Schema(description = "尺码值", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
    private String size;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer quantity;

    @Schema(description = "单价（单位：美分）", example = "1075")
    private Long unitPrice;

    @Builder.Default
    @Schema(description = "是否选中 (0-未选中, 1-已选中)", example = "1")
    private Integer selected = 1;
}
