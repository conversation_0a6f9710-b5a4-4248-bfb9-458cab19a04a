package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.constants.UserServicesConstants;
import com.knet.common.enums.LanguageEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2025/2/14 17:56
 * @description: 用户请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserSaveRequest extends BaseRequest {

    @Schema(description = "id,用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @NotBlank(message = "账号不能为空")
    @Pattern(regexp = UserServicesConstants.USER_ACCOUNT_REGEX, message = "账号不合法")
    @Schema(description = "账号，最大长度20", requiredMode = Schema.RequiredMode.REQUIRED)
    private String account;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "邮箱")
    private String email;

    @NotBlank(message = "用密码不能为空")
    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    /**
     * @see LanguageEnum 语言
     */
    @Schema(description = "语言,默认英语")
    private LanguageEnum language = LanguageEnum.EN_US;

    @Schema(description = "角色id,默认是商家用户")
    private Long roleId = 2L;
}
