package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:49
 * @description: 购物车项-编辑请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "购物车项-编辑请求体")
public class ShopCartItemRequest extends BaseRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @NotBlank(message = "商品SKU不能为空")
    @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

}
