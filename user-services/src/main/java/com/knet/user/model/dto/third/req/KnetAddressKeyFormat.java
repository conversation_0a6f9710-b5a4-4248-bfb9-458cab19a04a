package com.knet.user.model.dto.third.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.knet.common.base.BaseRequest;
import com.knet.user.model.dto.req.UserAddressSaveRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/19 16:18
 * @description: kg 校验地址请求体
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = false)
public class KnetAddressKeyFormat extends BaseRequest {
    @Schema(description = "省份")
    @JsonProperty("PoliticalDivision1")
    private String politicalDivision1;

    @Schema(description = "城市")
    @JsonProperty("PoliticalDivision2")
    private String politicalDivision2;

    @Schema(description = "邮编")
    @JsonProperty("PostcodePrimaryLow")
    private String postcodePrimaryLow;

    @Schema(description = "国家代码")
    @JsonProperty("CountryCode")
    private String countryCode;

    @Schema(description = "街道地址")
    @JsonProperty("AddressLine")
    private List<String> addressLine;

    public static KnetAddressKeyFormat create(UserAddressSaveRequest request) {
        return KnetAddressKeyFormat
                .builder()
                .politicalDivision1(request.getState())
                .politicalDivision2(request.getCity())
                .postcodePrimaryLow(request.getZipCode())
                .countryCode(request.getCountry())
                .addressLine(List.of(request.getAddressLine1(), request.getAddressLine2()))
                .build();
    }
}
