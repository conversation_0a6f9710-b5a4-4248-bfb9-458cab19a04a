package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.constants.UserServicesConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


/**
 * <AUTHOR>
 * @date 2025/2/13 14:14
 * @description: 认证用户请求体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthUserRequest extends BaseRequest {

    @NotBlank(message = "账号不能为空")
    @Pattern(regexp = UserServicesConstants.USER_ACCOUNT_REGEX, message = "账号不合法")
    @Schema(description = "账号，最大长度20", requiredMode = Schema.RequiredMode.REQUIRED)
    private String account;

    @NotBlank(message = "密码不能为空")
    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证码")
    private String code;

    /**
     * 校验用户名是否合法
     *
     * @return boolean
     */
    public boolean validateName() {
        return this.account != null && this.account.matches(UserServicesConstants.USER_ACCOUNT_REGEX);
    }
}
