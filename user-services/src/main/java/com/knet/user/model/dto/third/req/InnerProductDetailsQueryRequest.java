package com.knet.user.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/2/19 14:04
 * @description: 商品查询请求体
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class InnerProductDetailsQueryRequest extends BaseRequest {
    @NotBlank(message = "sku is not blank")
    @Schema(description = "sku", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sku;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private String account;
}
