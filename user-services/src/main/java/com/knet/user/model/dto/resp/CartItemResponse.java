package com.knet.user.model.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:05
 * @description: 购物车商品项响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "购物车商品项响应")
public class CartItemResponse {

    @Schema(description = "购物车商品项ID")
    private Long itemId;

    @Schema(description = "购物车ID")
    private Long cartId;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品SKU")
    private String sku;

    @Schema(description = "商品图片URL")
    private String imageUrl;

    @Schema(description = "尺码明细列表")
    private List<SizeDetailResponse> sizeDetails;

    @Schema(description = "尺码明细聚合列表")
    private List<SizeDetailSumResponse> sizeDetailSums;

    @Schema(description = "商品总数量")
    private Integer totalQuantity;

    @Schema(description = "商品总金额，美元")
    private String totalAmount;

    @Schema(description = "原始商品总金额，美元")
    private String originalTotalAmount;

    public static CartItemResponse createCartItemResponse(Long itemId, Long cartId, String productName,
                                                          String sku, String imageUrl,
                                                          List<SizeDetailResponse> sizeDetails,
                                                          List<SizeDetailSumResponse> sizeDetailSums,
                                                          int totalQuantity, String totalAmount, String originalTotalAmount) {
        return CartItemResponse.builder()
                .itemId(itemId)
                .cartId(cartId)
                .productName(productName)
                .sku(sku)
                .imageUrl(imageUrl)
                .sizeDetails(sizeDetails)
                .sizeDetailSums(sizeDetailSums)
                .totalQuantity(totalQuantity)
                .totalAmount(totalAmount)
                .originalTotalAmount(originalTotalAmount)
                .build();
    }
}
