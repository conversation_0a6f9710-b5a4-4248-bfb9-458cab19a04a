package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/13 09:55
 * @description: 权限表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_permission", description = "权限表")
@TableName("sys_permission")
public class SysPermission extends BaseEntity {

    @Schema(description = "权限名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "权限表达式", requiredMode = Schema.RequiredMode.REQUIRED)
    private String expression;
}
