package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.LanguageEnum;
import com.knet.common.enums.UserStatus;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:02
 * @description: 用户表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_user", description = "用户表")
@TableName("sys_user")
public class SysUser extends BaseEntity {

    @Schema(description = "账号标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uid;

    @Schema(description = "账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String account;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    /**
     * @see LanguageEnum
     */
    @Builder.Default
    @Schema(description = "语言", requiredMode = Schema.RequiredMode.REQUIRED)
    private LanguageEnum language = LanguageEnum.EN_US;

    /**
     * @see UserStatus
     */
    @Builder.Default
    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    UserStatus status = UserStatus.ENABLE;

    public UserInfoDtoResp mapToUserInfoDtoResp() {
        return UserInfoDtoResp.builder()
                .id(this.getId())
                .uid(this.uid)
                .account(this.account)
                .nickName(this.nickName)
                .email(this.email)
                .language(this.language)
                .createTime(this.getCreateTime())
                .updateTime(this.getUpdateTime())
                .build();
    }
}
