package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/2/13 14:47
 * @description: 认证请求实体基础类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthBaseRequest extends BaseRequest {
    @Schema(description = "token", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "token不能为空")
    private String token;
}
