package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/2/13 10:41
 * @description: 角色权限关系表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sysRolerPermissionRel", description = "角色权限关系表")
@TableName("sys_roler_permission_rel")
public class SysRolerPermissionRel extends BaseEntity {
    @Schema(description = "角色id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long rolerId;
    @Schema(description = "权限id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long permissionId;
}
