package com.knet.user.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.OperationResult;
import com.knet.common.enums.UserOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/5/20 18:10
 * @description: 用户操作记录表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(description = "用户操作记录表")
@TableName("sys_user_operation_record")
public class SysUserOperationRecord extends BaseEntity {

    /**
     * 操作记录ID（全局唯一）
     */
    @Schema(description = "操作记录ID（全局唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operationId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;

    /**
     * 操作者ID（谁执行的操作，可能是用户本人或管理员）
     */
    @Schema(description = "操作者ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long operatorId;

    /**
     * 操作者类型（USER-用户，ADMIN-管理员，SYSTEM-系统）
     */
    @Schema(description = "操作者类型", example = "USER", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operatorType;

    /**
     * 操作类型
     * @see UserOperationType
     */
    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private UserOperationType operationType;

    /**
     * 操作结果
     * @see OperationResult
     */
    @Schema(description = "操作结果", requiredMode = Schema.RequiredMode.REQUIRED)
    private OperationResult operationResult;

    /**
     * 操作描述
     */
    @Schema(description = "操作描述", example = "用户充值100.00美元")
    private String operationDesc;

    /**
     * 操作详情（JSON格式存储具体的操作数据）
     */
    @Schema(description = "操作详情", example = "{\"amount\":\"100.00\",\"currency\":\"USD\"}")
    private String operationDetail;

    /**
     * 客户端IP地址
     */
    @Schema(description = "客户端IP地址", example = "*************")
    private String clientIp;

    /**
     * 用户代理（浏览器信息）
     */
    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
    private String userAgent;

    /**
     * 关联业务ID（如订单ID、支付ID等）
     */
    @Schema(description = "关联业务ID", example = "ORD-123456789012345678")
    private String businessId;

    /**
     * 关联业务类型（如ORDER、PAYMENT、WALLET等）
     */
    @Schema(description = "关联业务类型", example = "WALLET")
    private String businessType;

    /**
     * 错误信息（操作失败时记录）
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remarks;
}
