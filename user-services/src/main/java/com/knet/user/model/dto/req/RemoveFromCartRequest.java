package com.knet.user.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/5/21 14:55
 * @description: 从购物车移除商品请求
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "从购物车移除商品请求")
public class RemoveFromCartRequest extends BaseRequest {
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1001")
    private Long userId;

    @Schema(description = "购物车商品项ID", example = "1")
    private Long cartItemId;

    @Schema(description = "尺码值", example = "5")
    private String size;
    
    @Schema(description = "sku", example = "FQ8138 002")
    private String sku;

    @Schema(description = "是否清空整个购物车", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean clearAll = false;
}
