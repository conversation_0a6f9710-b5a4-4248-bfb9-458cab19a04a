package com.knet.user.model.dto.third.req;

import com.knet.common.base.BaseRequest;
import com.knet.common.enums.ProductStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/4/14 10:46
 * @description: 查询knet 商品的请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InnerKnetProductRequest extends BaseRequest {

    @Schema(description = "knet_product唯一标识符")
    private String listingId;

    @Schema(description = "knet唯一标识符")
    private String oneId;

    @Schema(description = "sku")
    private String sku;

    @Schema(description = "尺码")
    private String spec;
    /**
     * @see ProductStatus
     */
    @Schema(description = "商品状态 上下架")
    private String status;
    
    @Schema(description = "账号", requiredMode = Schema.RequiredMode.NOT_REQUIRED, hidden = true)
    private String account;
}
