package com.knet.user.model.dto.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/5 14:32
 * @description: UserRechargeRecordResp
 */
@Data
public class UserRechargeRecordResp {

    @Schema(description = "操作记录ID")
    private String operationId;

    @Schema(description = "userId")
    private Long userId;

    @Schema(description = "用户账号", example = "admin")
    private String account;

    @Schema(description = "充值金额，美元", example = "100.00")
    private String amount;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "createTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
    private Date createTime;
}
