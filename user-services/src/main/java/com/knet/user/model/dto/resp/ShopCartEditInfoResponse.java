package com.knet.user.model.dto.resp;

import com.knet.common.base.BaseResponse;
import com.knet.user.model.dto.third.resp.ProductSkuSpecPriceDtoResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/17 16:45
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = true)
public class ShopCartEditInfoResponse extends BaseResponse {
    @Schema(description = "sku")
    private String sku;

    @Schema(description = "尺码")
    private String size;

    @Schema(description = "商品图片")
    public String img;

    @Schema(description = "商品名称")
    public String productName;

    @Schema(description = "商品库存数量")
    private Integer total;

    @Builder.Default
    @Schema(description = "stock价格（单位：美元）", example = "10.75")
    private String stockPrice = "0.00";

    @Builder.Default
    @Schema(description = "goat价格（单位：美元）", example = "10.75")
    private String goatPrice = "0.00";

    @Schema(description = "价格-购物车聚合信息")
    private List<ShopCartSizeDetailResponse> details;


    public static ShopCartEditInfoResponse create(ProductSkuSpecPriceDtoResp item, List<ShopCartSizeDetailResponse> details) {
        return ShopCartEditInfoResponse
                .builder()
                .sku(item.getSku())
                .size(item.getSpec())
                .img(item.getImg())
                .productName(item.getRemarks())
                .total(item.getTotal())
                .stockPrice(item.getStockPrice())
                .goatPrice(item.getGoatPrice())
                .details(details)
                .build();
    }
}
