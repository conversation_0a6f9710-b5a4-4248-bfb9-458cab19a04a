package com.knet.user.model.dto.resp;

import com.knet.common.utils.PriceFormatUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:10
 * @description: 尺码明细响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "尺码明细响应")
public class SizeDetailResponse {

    @Schema(description = "尺码明细ID")
    private Long detailId;

    @Schema(description = "购物车商品项ID")
    private Long cartItemId;

    @Schema(description = "尺码值", example = "5")
    private String size;

    @Schema(description = "数量", example = "1")
    private Integer quantity;

    @Schema(description = "库存")
    private Integer stock;

    @Schema(description = "单价，美分", example = "9999")
    private String unitPrice;

    @Schema(description = "原始单价，美分", example = "9999")
    private String originalUnitPrice;

    @Schema(description = "小计金额，美元", example = "9999")
    private String subtotal;

    @Schema(description = "原始小计金额，美元", example = "9999")
    private String originalSubtotal;

    @Schema(description = "是否选中 (0-未选中, 1-已选中)", example = "1")
    private Integer selected;

    /**
     * 创建尺码明细响应
     *
     * @param detail 购物车查询结果（已应用价格策略）
     * @param itemId 购物车商品项ID
     * @return 尺码明细响应
     */
    public static SizeDetailResponse createSizeDetailResponse(CartQueryResult detail, Long itemId) {
        // detail.getUnitPrice() 已经是应用了价格策略的美元价格
        double strategySubtotal = Double.parseDouble(detail.getUnitPrice()) * Long.valueOf(detail.getQuantity());

        // 计算原始价格（反推）
        // 注意：这里需要通过价格策略服务来反推原始价格，但为了简化，我们暂时不在这里处理
        // 原始价格的计算应该在服务层完成

        return SizeDetailResponse
                .builder()
                .detailId(detail.getDetailId())
                .cartItemId(itemId)
                .size(detail.getSize())
                .quantity(detail.getQuantity())
                .unitPrice(detail.getUnitPrice())
                .subtotal(PriceFormatUtil.formatPrice(strategySubtotal))
                .selected(detail.getDetailSelected())
                .build();
    }
}
