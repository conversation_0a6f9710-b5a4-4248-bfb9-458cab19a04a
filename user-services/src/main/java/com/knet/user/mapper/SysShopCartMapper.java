package com.knet.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.user.model.dto.resp.CartQueryResult;
import com.knet.user.model.entity.SysShopCart;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/21 10:08
 * @description: SysShopCartMapper
 */
@Mapper
public interface SysShopCartMapper extends BaseMapper<SysShopCart> {

    /**
     * 查询用户购物车详情
     *
     * @param userId 用户ID
     * @return 购物车详情列表
     */
    List<CartQueryResult> queryCartDetails(@Param("userId") Long userId);

    /**
     * 查询用户购物车中的特定商品
     *
     * @param userId 用户ID
     * @param sku    商品SKU
     * @param size   商品尺码
     * @return 购物车商品详情
     */
    List<CartQueryResult> queryCartItem(@Param("userId") Long userId, @Param("sku") String sku, @Param("size") String size);
}
