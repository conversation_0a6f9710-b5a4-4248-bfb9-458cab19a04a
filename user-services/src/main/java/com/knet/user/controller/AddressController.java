package com.knet.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.context.UserContext;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.user.model.dto.req.UserAddressQueryRequest;
import com.knet.user.model.dto.req.UserAddressSaveRequest;
import com.knet.user.model.dto.resp.UserAddressDtoResp;
import com.knet.user.model.entity.SysUserAddress;
import com.knet.user.service.ISysUserAddressService;
import com.knet.user.system.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/7 14:56
 * @description: 地址控制器
 */
@Slf4j
@RestController
@RequestMapping("/address")
@Tag(name = "用户地址控制器", description = "用户地址控制器")
public class AddressController {
    @Resource
    private ISysUserAddressService sysUserAddressService;
    @Resource
    private JwtUtil jwtUtil;

    /**
     * 添加用户地址
     *
     * @param request 用户地址请求体
     * @return 返回创建结果
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "添加用户地址")
    @Operation(summary = "添加用户地址")
    @PostMapping("/create")
    public HttpResult<SysUserAddress> createUserAddress(@Validated @RequestBody UserAddressSaveRequest request) {
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        String cacheKeyPattern = "user-service:addressList:" + userId + ":*";
        RedisCacheUtil.deleteByPattern(cacheKeyPattern);
        return HttpResult.ok(sysUserAddressService.createAddress(request));
    }

    /**
     * 查询用户地址列表
     *
     * @param request r
     * @return r
     */
    @Cacheable(value = "addressList",
            key = "#request.userId + ':' + #request.pageNo + ':' + #request.pageSize",
            unless = "#result == null || #result.data == null || #result.data.records.isEmpty()")
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Operation(description = "查询用户地址列表")
    @PostMapping("/list")
    public HttpResult<IPage<UserAddressDtoResp>> list(@RequestBody UserAddressQueryRequest request) {
        return HttpResult.ok(sysUserAddressService.listAddress(request));
    }

    /**
     * 删除用户地址
     *
     * @param id 地址ID
     * @return 返回删除结果
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "删除用户地址")
    @Operation(summary = "根据ID删除用户地址")
    @DeleteMapping("/{id}")
    public HttpResult<Void> deleteUserAddress(@PathVariable Long id) {
        log.info("删除用户地址请求体:{}", id);
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        String cacheKeyPattern = "user-service:addressList:" + userId + ":*";
        RedisCacheUtil.deleteByPattern(cacheKeyPattern);
        sysUserAddressService.deleteUserAddress(id);
        log.info("删除用户地址成功");
        return HttpResult.ok();
    }

    /**
     * 修改用户地址
     *
     * @param id      地址ID
     * @param request 用户地址请求体
     * @return 返回更新后的地址
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "修改用户地址")
    @Operation(summary = "根据ID修改用户地址信息")
    @PutMapping("/{id}")
    public HttpResult<SysUserAddress> updateUserAddress(
            @PathVariable(required = true) Long id,
            @Validated @RequestBody UserAddressSaveRequest request) {
        log.info("修改用户地址请求, id: {}", id);
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        String cacheKeyPattern = "user-service:addressList:" + userId + ":*";
        RedisCacheUtil.deleteByPattern(cacheKeyPattern);
        SysUserAddress updatedAddress = sysUserAddressService.updateUserAddress(id, request);
        log.info("修改用户地址成功, id: {}", id);
        return HttpResult.ok(updatedAddress);
    }

    /**
     * 校验用户地址
     *
     * @param request 用户地址请求体
     * @return 返回校验结果
     */
    @ModifyHeader(value = "token", handlerType = "USER_TOKEN")
    @Loggable(value = "校验用户地址")
    @Operation(summary = "校验用户地址")
    @PostMapping("/validate")
    public HttpResult<Boolean> validateUserAddress(@RequestBody UserAddressSaveRequest request) {
        return HttpResult.ok(sysUserAddressService.validateAddress(request));
    }
}
