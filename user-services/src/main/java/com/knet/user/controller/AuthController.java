package com.knet.user.controller;

import cn.hutool.core.util.StrUtil;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.RateLimiter;
import com.knet.common.base.HttpResult;
import com.knet.common.constants.SystemConstant;
import com.knet.user.model.dto.req.AuthBaseRequest;
import com.knet.user.model.dto.req.AuthUserRequest;
import com.knet.user.service.IAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2025/2/13 13:51
 * @description: 用户授权相关控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Tag(name = "用户授权相关控制器", description = "用户授权相关控制器")
public class AuthController {

    @Resource
    private IAuthService iAuthService;

    /**
     * 用户登录
     *
     * @param request 登录请求体 {@link AuthUserRequest}
     * @return 返回登录结果
     */
    @RateLimiter(key = "auth:login:", capacity = 60, refillRate = 1)
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public HttpResult<String> login(@Validated @RequestBody AuthUserRequest request) {
        String tokenStr = iAuthService.login(request);
        if (StrUtil.isBlank(tokenStr)) {
            return HttpResult.error("登录失败,账号密码不匹配");
        }
        return HttpResult.ok(tokenStr);
    }

    /**
     * 用户退出
     *
     * @param token   token
     * @param account 用户名
     * @return 返回退出结果
     */
    @Operation(summary = "用户退出")
    @GetMapping("/logout")
    public HttpResult<Void> logout(@RequestHeader(SystemConstant.TOKEN) String token, @RequestParam("account") String account) {
        iAuthService.logout(token, account);
        return HttpResult.ok();
    }

    @Operation(summary = "验证用户授权（废弃）")
    @Deprecated
    @PostMapping("/auth")
    public HttpResult<Boolean> auth(@Validated @RequestBody AuthBaseRequest request) {
        boolean authed = iAuthService.auth(request);
        return HttpResult.ok(authed);
    }

    @Loggable(value = "获取图形验证码", logResult = false)
    @RateLimiter(key = "auth:captcha:", capacity = 30, refillRate = 2)
    @Operation(summary = "获取图形验证码")
    @GetMapping("/captcha")
    public ResponseEntity<byte[]> captcha(HttpServletRequest req) {
        return iAuthService.getCaptcha(req);
    }
}
