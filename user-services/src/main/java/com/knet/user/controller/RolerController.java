package com.knet.user.controller;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.PermissionCheck;
import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.req.RolerSaveRequest;
import com.knet.user.model.entity.SysRoler;
import com.knet.user.service.ISysRolerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/17 16:49
 * @description: 角色控制器
 */
@Slf4j
@RestController
@RequestMapping("/roler")
@Tag(name = "角色相关控制器", description = "角色相关控制器")
public class RolerController {

    @Resource
    private ISysRolerService iSysRolerService;

    /**
     * 查询角色列表
     *
     * @return r
     */
    @Operation(description = "查询角色列表")
    @GetMapping("/list")
    public HttpResult<List<SysRoler>> listRoler() {
        return HttpResult.ok(iSysRolerService.queryListFromCache());
    }

    /**
     * 创建角色
     *
     * @return r
     */
    @Loggable(value = "创建角色")
    @PermissionCheck(role = "admin")
    @Operation(description = "创建角色")
    @PostMapping("/create")
    public HttpResult<SysRoler> createRoler(@Validated @RequestBody RolerSaveRequest request) {
        return HttpResult.ok(iSysRolerService.createRoler(request));
    }

    /**
     * 删除角色
     *
     * @param id 角色id
     * @return 返回删除结果
     */
    @Loggable(value = "删除角色")
    @PermissionCheck(role = "admin")
    @Operation(description = "删除角色")
    @DeleteMapping("/{id}")
    public HttpResult<Void> deleteRoler(@PathVariable Long id) {
        iSysRolerService.deleteRoler(id);
        return HttpResult.ok();
    }
}
