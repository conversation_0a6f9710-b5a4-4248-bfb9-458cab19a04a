package com.knet.user.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.PermissionCheck;
import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.req.UserEditRequest;
import com.knet.user.model.dto.req.UserQueryRequest;
import com.knet.user.model.dto.req.UserRechargeQueryRequest;
import com.knet.user.model.dto.req.UserSaveRequest;
import com.knet.user.model.dto.resp.UserInfoDtoResp;
import com.knet.user.model.dto.resp.UserRechargeRecordResp;
import com.knet.user.model.entity.SysUser;
import com.knet.user.service.ISysUserService;
import com.knet.user.system.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/14 17:53
 * @description: 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/users")
@Tag(name = "用户相关控制器-增删查改")
public class UserController {
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private ISysUserService iUserService;

    /**
     * 创建用户
     *
     * @param request 创建用户请求体
     * @return 返回创建结果
     */
    @Loggable(value = "创建用户")
    @PermissionCheck(role = "admin")
    @Operation(summary = "创建用户")
    @PostMapping("/create")
    public HttpResult<SysUser> createUser(@Validated @RequestBody UserSaveRequest request) {
        return HttpResult.ok(iUserService.createUser(request));
    }

    /**
     * 查询用户列表
     *
     * @param request r
     * @return r
     */
    @Cacheable(value = "userList",
            key = "#request.account + ':' + #request.pageNo + ':' + #request.pageSize",
            unless = "#result == null || #result.data == null || #result.data.records.isEmpty()")
    @Operation(summary = "查询用户列表")
    @PostMapping("/list")
    public HttpResult<IPage<UserInfoDtoResp>> listUser(@RequestBody UserQueryRequest request) {
        return HttpResult.ok(iUserService.listUser(request));
    }

    /**
     * 根据用户ID查询用户
     *
     * @param id id
     * @return dto
     */
    @Operation(summary = "根据用户ID查询用户")
    @GetMapping("/{id}")
    public HttpResult<UserInfoDtoResp> getUserById(@PathVariable(value = "id") Long id) {
        return HttpResult.ok(iUserService.getUserById(id));
    }

    /**
     * 更新用户
     *
     * @param request 更新用户请求体
     * @return 返回更新结果
     */
    @Operation(summary = "更新用户")
    @Loggable(value = "更新用户")
    @PostMapping("/update")
    public HttpResult<Void> updateUser(@Validated @RequestBody UserEditRequest request) {
        log.info("更新用户请求体:{}", request);
        iUserService.updateUser(request);
        log.info("更新用户成功");
        return HttpResult.ok();
    }

    /**
     * 删除用户
     *
     * @param id 用户id
     * @return 返回删除结果
     */
    @Operation(summary = "删除用户")
    @Loggable(value = "删除用户")
    @DistributedLock(key = "'deleteUser:'+#id", expire = 2)
    @PermissionCheck(role = "admin")
    @DeleteMapping("/{id}")
    public HttpResult<Void> deleteUser(@PathVariable Long id) {
        log.info("删除用户请求体:{}", id);
        iUserService.deleteUser(id);
        log.info("删除用户成功");
        return HttpResult.ok();
    }

    /**
     * 根据token获取用户ID
     *
     * @param token 用户令牌
     * @return 返回用户ID
     */
    @Operation(description = "从请求头中获取token并解析出用户ID")
    @GetMapping("/get-userid")
    public HttpResult<Long> getUserIdByToken(@RequestHeader(value = "token", required = true) String token) {
        try {
            String userId = jwtUtil.getUserIdFromToken(token);
            return HttpResult.ok(Long.valueOf(userId));
        } catch (Exception e) {
            log.error("解析token获取用户ID失败: {}", e.getMessage());
            return HttpResult.error("无效的token");
        }
    }

    /**
     * 管理员-查询用户操作记录
     *
     * @param request 查询请求
     * @return 充值/扣款记录列表
     */
    @Operation(summary = "管理员-查询用户操作记录", description = "管理员-查询用户操作记录")
    @PostMapping("/operation-record/list")
    public HttpResult<IPage<UserRechargeRecordResp>> operationRecord(@RequestBody UserRechargeQueryRequest request) {
        return HttpResult.ok(iUserService.operationRecord(request));
    }
}
