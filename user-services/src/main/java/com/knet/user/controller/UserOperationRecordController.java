package com.knet.user.controller;

import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.req.CreateUserOperationRecordRequest;
import com.knet.user.service.ISysUserOperationRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/20 18:45
 * @description: 用户操作记录控制器
 */
@Slf4j
@RestController
@RequestMapping("/user/operation-record")
@Tag(name = "用户操作记录控制器", description = "用户操作记录管理")
public class UserOperationRecordController {

    @Resource
    private ISysUserOperationRecordService sysUserOperationRecordService;

    /**
     * 创建用户操作记录
     *
     * @param request 创建请求
     * @return 操作记录ID
     */
    @Operation(summary = "创建用户操作记录", description = "创建新的用户操作记录")
    @PostMapping("/create")
    public HttpResult<String> create(@Validated @RequestBody CreateUserOperationRecordRequest request) {
        log.info("创建用户操作记录请求: {}", request);
        String operationId = sysUserOperationRecordService.createOperationRecord(request);
        return HttpResult.ok(operationId);
    }
}
