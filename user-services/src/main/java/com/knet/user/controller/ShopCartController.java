package com.knet.user.controller;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.constants.SystemConstant;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.dto.req.RemoveFromCartRequest;
import com.knet.user.model.dto.req.ShopCartItemRequest;
import com.knet.user.model.dto.resp.CartResponse;
import com.knet.user.model.dto.resp.ShopCartEditInfoResponse;
import com.knet.user.service.IShopCartService;
import com.knet.user.system.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:00
 * @description: 用户购物车接口
 */
@Slf4j
@RestController
@RequestMapping("/shop-cart")
@Tag(name = "用户购物车控制器", description = "用户购物车控制器")
public class ShopCartController {
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private IShopCartService shopCartService;

    /**
     * 添加商品到购物车
     *
     * @param request 添加商品请求
     * @return 购物车信息
     */
    @Loggable(value = "添加商品到购物车")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "添加商品到购物车", description = "添加商品到购物车，返回更新后的购物车信息")
    @PostMapping("/add")
    public HttpResult<CartResponse> addToCart(@Validated @RequestBody AddToCartRequest request) {
        log.info("添加商品到购物车请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        CartResponse response = shopCartService.addToCart(request);
        return HttpResult.ok(response);
    }

    /**
     * 从购物车移除商品
     *
     * @param request 移除商品请求
     * @return 购物车信息
     */
    @Loggable(value = "从购物车移除商品")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "从购物车移除商品", description = "从购物车移除商品")
    @PostMapping("/remove")
    public HttpResult<Void> removeFromCart(@Validated @RequestBody RemoveFromCartRequest request) {
        log.info("从购物车移除商品请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        shopCartService.removeFromCart(request);
        return HttpResult.ok();
    }

    /**
     * 获取购物车信息
     *
     * @param userId 用户ID
     * @return 购物车信息
     */
    @Loggable(value = "获取购物车信息")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "获取购物车信息", description = "获取用户购物车信息")
    @GetMapping("/{userId}")
    public HttpResult<CartResponse> getCart(@PathVariable Long userId) {
        log.info("获取购物车信息请求, userId: {}", userId);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(userId)) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        CartResponse response = shopCartService.getCart(userId);
        CartResponse cartResponse = shopCartService.fillStockInfo(response);
        return HttpResult.ok(cartResponse);
    }

    /**
     * 购物车弹窗信息接口-指定SKU
     *
     * @param request 购物车商品项请求
     * @return 购物车商品项信息
     */
    @Loggable(value = "购物车弹窗信息接口-指定SKU")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "购物车弹窗信息接口", description = "根据用户ID、商品SKU获取购物车中弹窗信息接口-适用于购物车编辑/添加信息显示")
    @PostMapping("/cart-item/list")
    public HttpResult<List<ShopCartEditInfoResponse>> getShopCartItemForEdit(@Validated @RequestBody ShopCartItemRequest request) {
        log.info("获取购物车中的特定商品-适用于购物车编辑 请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        List<ShopCartEditInfoResponse> shopCartItemForEdit = shopCartService.getShopCartItemForEdit(request);
        return HttpResult.ok(shopCartItemForEdit);
    }
}
