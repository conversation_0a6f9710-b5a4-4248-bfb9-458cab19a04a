package com.knet.user.openfeign;

import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.third.req.InnerKnetProductRequest;
import com.knet.user.model.dto.third.req.InnerProductDetailsQueryRequest;
import com.knet.user.model.dto.third.resp.ProductSkuSpecPriceDtoResp;
import com.knet.user.model.dto.third.resp.QueryKnetProductResp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/14 11:01
 * @description: 商品服务api
 */
@FeignClient(name = "goods-services", path = "/goodServices")
public interface ApiGoodsServiceProvider {

    /**
     * 查询商品
     *
     * @param request 查询商品请求体
     * @return 返回查询结果
     */
    @Loggable(value = "knet 查询商品")
    @Operation(description = "外部查询商品")
    @PostMapping("/api/no-auth/product/list")
    HttpResult<List<QueryKnetProductResp>> queryKnetProductsNoAuth(@RequestBody InnerKnetProductRequest request);

    /**
     * 商品详情
     *
     * @param request 查询条件
     * @return 商品详情
     */
    @Loggable(value = "knet查询-商品详情")
    @Operation(description = "商品详情")
    @PostMapping("/api/no-auth/product/detail")
    HttpResult<List<ProductSkuSpecPriceDtoResp>> queryProductDetails(@Validated @RequestBody InnerProductDetailsQueryRequest request);
}
