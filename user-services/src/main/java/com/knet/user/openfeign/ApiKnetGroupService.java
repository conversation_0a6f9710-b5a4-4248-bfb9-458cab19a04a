package com.knet.user.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.user.model.dto.third.req.KnetAddressKeyFormat;
import com.knet.user.system.handler.ApiKnetGroupServiceFallbackImpl;
import feign.FeignException;
import feign.RetryableException;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.ConnectException;
import java.net.SocketException;
import java.util.concurrent.TimeoutException;


/**
 * <AUTHOR>
 * @date 2025/2/25 11:02
 * @description: kg 服务提供API
 */
@RefreshScope
@FeignClient(name = "knetGroup-services", url = "${feign.client.config.knetGroupServices.url}", fallback = ApiKnetGroupServiceFallbackImpl.class)
public interface ApiKnetGroupService {
    /**
     * KG 创建物流单
     *
     * @param shipment shipment
     * @return 物流信息
     */
    @Retryable(
            //重试策略，3次，间隔1秒
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000, multiplier = 2),
            include = {
                    ConnectException.class,
                    SocketException.class,
                    FeignException.class,
                    TimeoutException.class,
                    RetryableException.class,
            },
            exclude = {
                    //排除参数异常
                    IllegalArgumentException.class
            }
    )
    @Schema(description = "KG 地址验证服务")
    @PostMapping("/api/system/verify/address")
    HttpResult<Boolean> verifyAddress(@RequestBody KnetAddressKeyFormat shipment);
}
