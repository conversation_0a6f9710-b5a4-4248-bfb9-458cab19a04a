# Nacos配置指南

## 配置说明

需要在Nacos配置中心添加webhook相关配置，以支持订单通知功能。

## 配置步骤

### 1. 登录Nacos管理界面
访问Nacos管理界面：http://your-nacos-server:8848/nacos

### 2. 找到notification-services配置文件
- 命名空间：`5139d7fa-db43-42ae-9697-509bddd0cbd6`
- Group：`DEFAULT_GROUP`
- Data ID：`notification-services-dev.yaml` (开发环境) 或 `notification-services-prd.yaml` (生产环境)

### 3. 添加webhook配置
在配置文件中添加以下内容：

```yaml
# Webhook配置
webhook:
  app-push-url: *********************************************************************************
```

### 4. 发布配置
点击"发布"按钮使配置生效。

## 配置验证

配置添加后，可以通过以下方式验证：

1. 重启notification-services服务
2. 查看服务启动日志，确认配置加载成功
3. 触发订单shipping label分配流程，观察webhook消息发送情况

## 注意事项

1. webhook URL需要根据实际的Slack webhook地址进行配置
2. 配置修改后需要重启服务或等待配置刷新
3. 生产环境和开发环境需要分别配置对应的webhook地址

## 功能说明

- 当订单分配完成shipping label后，系统会自动发送订单通知到配置的webhook地址
- 每个子订单会发送一条独立的通知消息
- 通过Redis防重复机制确保同一个子订单号只发送一次消息
- 消息格式符合Slack webhook要求，包含OrderNo、OrderSubNo和SKU信息
