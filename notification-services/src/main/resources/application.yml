spring:
  profiles:
    active: dev
  config:
    import: optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  mvc:
    servlet:
      path: /notificationServices
#actuator 运维配置信息
management:
  endpoints:
    web:
      exposure:
        include: "health,info"
mybatis-plus:
  mapper-locations: classpath*:/mapper/*Mapper.xml
  type-aliases-package: com.knet.entity