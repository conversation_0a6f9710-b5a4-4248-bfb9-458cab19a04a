package com.knet.notification.mq.consumer;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.notification.service.INotificationService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

import static com.knet.common.constants.SystemConstant.NOTIFICATION_PROCESSED;
import static com.knet.common.constants.SystemConstant.PROCESSED;

/**
 * <AUTHOR>
 * @date 2025/5/20 20:30
 * @description: 用户操作记录消费者
 */
@Slf4j
@Component
public class NotificationMessageConsumer {

    @Resource
    private INotificationService notificationService;

    /**
     * 消费通知消息
     *
     * @param messageBody 消息内容
     * @param routingKey  路由键
     * @param messageId   消息ID
     * @param channel     通道
     * @param deliveryTag 投递标签
     */
    @RabbitListener(
            queues = "notification-queue.notification-services",
            ackMode = "MANUAL"
    )
    public void consumeNotificationMessage(
            @Payload String messageBody,
            @Header("routingKey") String routingKey,
            @Header("messageId") String messageId,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        log.info("接收到通知消息: messageId={}, routingKey={}", messageId, routingKey);
        try {
            if (!RedisCacheUtil.setIfAbsent(NOTIFICATION_PROCESSED.formatted(messageId), PROCESSED, 10)) {
                log.warn("重复消息，已处理过: messageId={}", messageId);
                channel.basicAck(deliveryTag, false);
                return;
            }
            // 根据routingKey进行不同的处理
            if (routingKey.startsWith("notification.")) {
                //处理消息通知
                log.info("处理通知消息: messageId={}, routingKey={}", messageId, routingKey);
                // 根据routingKey的具体类型进行处理
                NotificationMessage notificationMessage = JSON.parseObject(messageBody, NotificationMessage.class);
                notificationService.sendNotification(notificationMessage);
                channel.basicAck(deliveryTag, false);
            } else {
                log.warn("未知的routingKey: {}, messageId: {}", routingKey, messageId);
                channel.basicAck(deliveryTag, false);
            }
        } catch (Exception e) {
            log.error("通知消息处理失败: messageId={},  error={}", messageId, e.getMessage(), e);
            try {
                // 拒绝消息并重新入队
                channel.basicNack(deliveryTag, false, true);
            } catch (IOException ioException) {
                log.error("消息确认失败: messageId={}, error={}", messageId, ioException.getMessage());
            }
        }
    }
}
