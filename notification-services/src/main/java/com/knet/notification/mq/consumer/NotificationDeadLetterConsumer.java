package com.knet.notification.mq.consumer;

import com.knet.common.utils.RedisCacheUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2025/7/15
 * @description: 通知服务死信消费者 - 处理进入死信队列的消息
 */
@Slf4j
@Component
public class NotificationDeadLetterConsumer {
    private static final String DLX_PROCESSED_KEY = "notification:dlx:processed:%s";
    private static final String PROCESSED = "PROCESSED";

    /**
     * 处理通知相关死信消息
     */
    @RabbitListener(queues = "notification-service.dlx.notification.queue", ackMode = "MANUAL")
    public void handleNotificationDeadLetter(
            @Payload String messageBody,
            @Header(value = "x-original-routing-key", required = false) String originalRoutingKey,
            @Header(value = "x-death", required = false) Object deathInfo,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
            Message message) {
        String messageId = extractMessageId(message);
        String dlxMessageId = "NOTIFICATION_DLX_" + messageId;
        try {
            // 幂等性检查
            if (!RedisCacheUtil.setIfAbsent(DLX_PROCESSED_KEY.formatted(dlxMessageId), PROCESSED, 300)) {
                log.warn("通知服务通知死信重复消息: messageId={}", dlxMessageId);
                channel.basicAck(deliveryTag, false);
                return;
            }
            log.error("通知服务通知死信消息处理: messageId={}, originalRoutingKey={}, messageBody={}, deathInfo={}",
                    messageId, originalRoutingKey, messageBody, deathInfo);
            // 记录死信消息
            recordDeadLetterMessage("NOTIFICATION", messageId, originalRoutingKey, messageBody, deathInfo);
            // 确认消息（死信消息通常不需要重新处理）
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.error("处理通知服务通知死信消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
            try {
                // 死信消息处理失败，直接确认（避免无限循环）
                channel.basicAck(deliveryTag, false);
            } catch (IOException ex) {
                log.error("确认死信消息失败: messageId={}, error={}", messageId, ex.getMessage());
            }
        }
    }

    /**
     * 从消息中提取messageId
     */
    private String extractMessageId(Message message) {
        Object messageIdHeader = message.getMessageProperties().getHeaders().get("messageId");
        if (messageIdHeader != null) {
            return messageIdHeader.toString();
        }
        // 如果没有messageId，使用消息的唯一标识
        return message.getMessageProperties().getMessageId() != null ?
                message.getMessageProperties().getMessageId() :
                "UNKNOWN_" + System.currentTimeMillis();
    }

    /**
     * 记录死信消息（可以保存到数据库或发送告警）
     */
    private void recordDeadLetterMessage(String messageType, String messageId, String originalRoutingKey,
                                         String messageBody, Object deathInfo) {
        try {
            // 这里可以实现具体的记录逻辑：
            // 1. 保存到死信消息表
            // 2. 发送告警通知
            // 3. 记录到监控系统
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            String logMessage = String.format(
                    "[NOTIFICATION_DEAD_LETTER_RECORD] Type: %s, MessageId: %s, OriginalRoutingKey: %s, Timestamp: %s, DeathInfo: %s, MessageBody: %s",
                    messageType, messageId, originalRoutingKey, timestamp, deathInfo, messageBody
            );
            // 记录到专门的死信日志文件
            log.error(logMessage);
            // TODO: 可以在这里添加以下功能：
            // 1. 保存到sys_dead_letter_message表
            // 2. 发送钉钉/邮件告警
            // 3. 推送到监控系统
            // 4. 对于通知相关的死信消息，可以考虑重新发送或人工处理

        } catch (Exception e) {
            log.error("记录死信消息失败: messageId={}, error={}", messageId, e.getMessage(), e);
        }
    }
}
