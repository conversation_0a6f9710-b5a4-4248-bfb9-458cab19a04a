package com.knet.notification.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:35
 * @description: 消息发送记录表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_message_delivery", description = "消息发送记录表")
@TableName("sys_message_delivery")
public class SysMessageDelivery extends BaseEntity {

    /**
     * 关联的消息ID
     */
    @Schema(description = "关联的消息ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long messageId;

    /**
     * 发送通道
     *
     * @see MessageChannel
     */
    @Schema(description = "发送通道", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageChannel channel;

    /**
     * 实际发送时间
     */
    @Schema(description = "实际发送时间")
    private LocalDateTime sendTime;

    /**
     * 投递状态
     *
     * @see MessageStatus
     */
    @Schema(description = "投递状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageStatus deliveryStatus;

    /**
     * 错误日志
     */
    @Schema(description = "错误日志")
    private String errorLog;

    /**
     * 创建投递记录的便捷方法
     */
    public static SysMessageDelivery createDelivery(Long messageId, MessageChannel channel) {
        return SysMessageDelivery
                .builder()
                .messageId(messageId)
                .channel(channel)
                .deliveryStatus(MessageStatus.PENDING)
                .build();
    }
}