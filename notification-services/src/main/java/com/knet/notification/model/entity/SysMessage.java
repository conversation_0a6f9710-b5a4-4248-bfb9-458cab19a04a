package com.knet.notification.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;
import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageStatus;
import com.knet.common.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/6/10 10:30
 * @description: 消息主表
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_message", description = "消息主表")
@TableName("sys_message")
public class SysMessage extends BaseEntity {

    /**
     * 发送者唯一标识
     */
    @Schema(description = "发送者唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String senderId;

    /**
     * 接收者唯一标识
     */
    @Schema(description = "接收者唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverId;

    /**
     * 消息内容
     */
    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    /**
     * 消息类型
     *
     * @see MessageType
     */
    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageType msgType;

    /**
     * 消息状态
     *
     * @see MessageStatus
     */
    @Schema(description = "消息状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageStatus status;

    /**
     * 创建消息对象的便捷方法
     */
    public static SysMessage createMessage(String senderId, String receiverId, String content,
                                           MessageType msgType) {
        return SysMessage.builder()
                .senderId(senderId)
                .receiverId(receiverId)
                .content(content)
                .msgType(msgType)
                .status(MessageStatus.PENDING)
                .build();
    }

    public MessageChannel getDefaultChannel() {
        return switch (this.msgType) {
            case TEXT, SYSTEM_NOTICE, PAYMENT_NOTICE -> MessageChannel.EMAIL;
            case ORDER_NOTICE -> MessageChannel.APP_PUSH;
            default -> MessageChannel.SMS;
        };
    }
}