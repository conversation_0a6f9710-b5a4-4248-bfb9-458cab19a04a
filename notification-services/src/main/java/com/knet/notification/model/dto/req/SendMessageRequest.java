package com.knet.notification.model.dto.req;

import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:25
 * @description: 发送消息请求参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "发送消息请求参数")
public class SendMessageRequest {

    /**
     * 发送者ID
     */
    @Schema(description = "发送者ID")
    private String senderId;

    /**
     * 接收者ID
     */
    @NotBlank(message = "接收者ID不能为空")
    @Schema(description = "接收者ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverId;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    @Schema(description = "消息内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String content;

    /**
     * 消息类型
     */
    @NotNull(message = "消息类型不能为空")
    @Schema(description = "消息类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private MessageType msgType;

    /**
     * 发送通道列表，为空时使用默认通道
     */
    @Schema(description = "发送通道列表，为空时使用默认通道")
    private List<MessageChannel> channels;
}