package com.knet.notification.system.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/21 11:57
 * @description: RabbitConfig 通知服务RabbitMQ配置
 **/

@Configuration
public class RabbitConfig {

    /**
     * 通知服务专用死信交换机
     */
    @Bean
    public DirectExchange notificationDlxExchange() {
        return new DirectExchange("notification-service.dlx", true, false);
    }

    /**
     * 通知服务通知死信队列
     */
    @Bean
    public Queue notificationDlxQueue() {
        return QueueBuilder
                .durable("notification-service.dlx.notification.queue")
                .build();
    }

    /**
     * 通知服务通知死信绑定
     */
    @Bean
    public Binding notificationDlxBinding() {
        return BindingBuilder
                .bind(notificationDlxQueue())
                .to(notificationDlxExchange())
                .with("notification.notification.*");
    }

    /**
     * 通知交换机
     */
    @Bean
    public TopicExchange notificationExchange() {
        return new TopicExchange("notification-exchange", true, false);
    }

    /**
     * 通知服务队列
     */
    @Bean
    public Queue notificationQueue() {
        return QueueBuilder
                .durable("notification-queue.notification-services")
                .withArgument("x-dead-letter-exchange", "notification-service.dlx")
                .withArgument("x-dead-letter-routing-key", "notification.notification.*")
                .build();
    }

    /**
     * 通知服务队列绑定
     */
    @Bean
    public Binding notificationBinding() {
        return BindingBuilder
                .bind(notificationQueue())
                .to(notificationExchange())
                .with("notification.*");
    }
}
