package com.knet.notification.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:54
 * @description: 邮件发送邮箱配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "mail")
public class MailProperties {
    /**
     * SMTP服务器地址
     */
    private String host;
    /**
     * 发件人地址
     */
    private String from;
    /**
     * 发件人显示名称
     */
    private String fromName;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
}
