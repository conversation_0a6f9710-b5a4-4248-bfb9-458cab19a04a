package com.knet.notification.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/2/13 13:29
 * @description: 认证配置信息
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "authconfig")
public class AuthConfig {
    /**
     * 密钥
     */
    private String authSecretKey;
    /**
     * 用户密钥
     */
    private String userSecretKey;
}
