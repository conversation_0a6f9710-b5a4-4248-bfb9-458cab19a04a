package com.knet.notification.system.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.notification.system.config.WebhookConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/8/15 10:05
 * @description: Webhook发送工具
 */
@Slf4j
@Component
public class WebhookSender {

    @Resource
    private WebhookConfig webhookConfig;

    /**
     * 发送APP_PUSH消息到Slack webhook
     * 通过Redis确保同一个子单号只能发送一次消息
     *
     * @param orderNo    订单号
     * @param orderSubNo 子订单号
     * @param sku        商品SKU
     * @return 是否发送成功
     */
    public boolean sendAppPushMessage(String orderNo, String orderSubNo, String sku) {
        // 使用Redis确保同一个子单号只能发送一次消息
        String redisKey = String.format("WEBHOOK_SENT:%s", orderSubNo);
        if (!RedisCacheUtil.setIfAbsent(redisKey, "SENT", 3600)) {
            log.info("子订单号{}的webhook消息已发送过，跳过发送", orderSubNo);
            return true;
        }
        try {
            // 构建Slack消息格式
            Map<String, Object> message = new HashMap<>(2);
            Map<String, Object> block = new HashMap<>(2);
            Map<String, Object> text = new HashMap<>(2);
            block.put("type", "section");
            text.put("type", "mrkdwn");
            text.put("text", String.format("Order Notification\n>OrderNo: %s\n>OrderSubNo: %s\n>SKU: %s\n>",
                    orderNo, orderSubNo, sku));
            block.put("text", text);
            message.put("blocks", List.of(block));
            String jsonBody = JSONUtil.toJsonStr(message);
            log.info("发送APP_PUSH消息到webhook: orderNo={}, orderSubNo={}, sku={}", orderNo, orderSubNo, sku);
            String response = HttpUtil.post(webhookConfig.getAppPushUrl(), jsonBody);
            log.info("APP_PUSH消息发送成功: orderNo={}, orderSubNo={}, sku={}, response={}",
                    orderNo, orderSubNo, sku, response);
            return true;
        } catch (Exception e) {
            log.error("发送APP_PUSH消息异常: orderNo={}, orderSubNo={}, sku={}, error={}",
                    orderNo, orderSubNo, sku, e.getMessage(), e);
            RedisCacheUtil.del(redisKey);
            return false;
        }
    }
}
