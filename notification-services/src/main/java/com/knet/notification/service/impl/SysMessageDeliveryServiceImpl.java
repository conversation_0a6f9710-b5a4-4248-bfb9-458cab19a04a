package com.knet.notification.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageStatus;
import com.knet.notification.mapper.SysMessageDeliveryMapper;
import com.knet.notification.model.entity.SysMessageDelivery;
import com.knet.notification.service.ISysMessageDeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:50
 * @description: SysMessageDeliveryServiceImpl 服务实现
 */
@Slf4j
@Service
public class SysMessageDeliveryServiceImpl extends ServiceImpl<SysMessageDeliveryMapper, SysMessageDelivery>
        implements ISysMessageDeliveryService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean updateDeliveryStatus(Long messageId, MessageChannel channel, MessageStatus messageStatus) {
        LambdaUpdateWrapper<SysMessageDelivery> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(SysMessageDelivery::getMessageId, messageId)
                .eq(SysMessageDelivery::getChannel, channel)
                .set(SysMessageDelivery::getDeliveryStatus, messageStatus);
        boolean updated = this.update(null, updateWrapper);
        log.info("更新消息发送记录状态: messageId={}, channel={}, status={}, result={}",
                messageId, channel, messageStatus, updated);
        return updated;
    }
}
