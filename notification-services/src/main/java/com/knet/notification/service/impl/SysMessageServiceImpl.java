package com.knet.notification.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.common.enums.MessageChannel;
import com.knet.notification.mapper.SysMessageMapper;
import com.knet.notification.model.entity.SysMessage;
import com.knet.notification.model.entity.SysMessageDelivery;
import com.knet.notification.service.ISysMessageDeliveryService;
import com.knet.notification.service.ISysMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:50
 * @description: SysMessageServiceImpl 实现
 */
@Slf4j
@Service
public class SysMessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessage> implements ISysMessageService {

    @Resource
    private ISysMessageDeliveryService sysMessageDeliveryService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SysMessage saveMessage(NotificationMessage notificationMessage) {
        // 1. 保存消息主表记录
        SysMessage message = SysMessage.createMessage(
                notificationMessage.getSenderId() == null ? "SYSTEM" : notificationMessage.getSenderId(),
                notificationMessage.getReceiverId(),
                notificationMessage.getContent(),
                notificationMessage.getMsgType()
        );
        boolean saved = this.save(message);
        if (!saved) {
            log.error("保存消息失败: {}", message);
            return null;
        }
        MessageChannel channel = notificationMessage.getDefaultChannel();
        // 2. 创建消息发送记录
        List<SysMessageDelivery> deliveries = new ArrayList<>();
        SysMessageDelivery delivery = SysMessageDelivery.createDelivery(message.getId(), channel);
        deliveries.add(delivery);
        // 3. 保存发送记录
        boolean deliverySaved = sysMessageDeliveryService.saveBatch(deliveries);
        if (!deliverySaved) {
            log.error("保存消息发送记录失败: {}", deliveries);
            return null;
        }
        return message;
    }
}
