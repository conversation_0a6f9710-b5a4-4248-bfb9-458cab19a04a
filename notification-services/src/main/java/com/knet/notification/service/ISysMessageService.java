package com.knet.notification.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.dto.message.NotificationMessage;
import com.knet.notification.model.entity.SysMessage;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:42
 * @description: ISysMessageService服务定义
 */
public interface ISysMessageService extends IService<SysMessage> {

    /**
     * 保存消息
     *
     * @param notificationMessage 通知消息
     * @return 消息
     */
    SysMessage saveMessage(NotificationMessage notificationMessage);
}
