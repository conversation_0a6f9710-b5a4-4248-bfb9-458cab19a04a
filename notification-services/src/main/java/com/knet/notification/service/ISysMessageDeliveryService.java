package com.knet.notification.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.common.enums.MessageChannel;
import com.knet.common.enums.MessageStatus;
import com.knet.notification.model.entity.SysMessageDelivery;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025/6/20 11:42
 * @description: SysMessageDelivery服务定义
 */
public interface ISysMessageDeliveryService extends IService<SysMessageDelivery> {
    @Transactional(rollbackFor = Exception.class)
    boolean updateDeliveryStatus(Long messageId, MessageChannel channel, MessageStatus messageStatus);
}
