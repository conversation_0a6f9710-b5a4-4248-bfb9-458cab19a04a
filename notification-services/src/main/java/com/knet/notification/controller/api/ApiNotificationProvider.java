package com.knet.notification.controller.api;

import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.notification.model.dto.req.SendMessageRequest;
import com.knet.notification.service.IApiNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/20 13:22
 * @description: 通知服务对外提供服务
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = " 通知服务-对外提供接口", description = " 通知服务-对外提供接口")
public class ApiNotificationProvider {
    @Resource
    private IApiNotificationService apiNotificationService;

    @Loggable(value = "发送消息通知")
    @PostMapping("/send/message")
    @Operation(summary = "发送消息通知", description = "供其他服务调用，发送消息通知")
    public HttpResult<Boolean> sendMessage(@Validated @RequestBody SendMessageRequest request) {
        log.info("发送消息通知: {}", request);
        try {
            boolean result = apiNotificationService.sendMessage(request);
            log.info("发送消息通知成功: {}", result);
            return HttpResult.ok(result);
        } catch (Exception e) {
            log.error("发送消息通知失败: {}", e.getMessage(), e);
            return HttpResult.error("发送消息通知失败: " + e.getMessage());
        }
    }
}