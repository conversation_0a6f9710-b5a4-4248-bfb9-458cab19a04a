package com.knet.delayed.service.impl;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.delayed.service.IRedisDelayedQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/6/25 16:47
 * @description:
 */
@Slf4j
@Service
public class RedisDelayedQueueServiceImpl implements IRedisDelayedQueueService {

    private static final String DELAYED_QUEUE_KEY = "delayed_queue:zset";

    /**
     * 添加延迟消息到队列
     *
     * @param message 延迟消息
     */
    @Override
    public void addDelayMessage(DelayedMessage message) {
        String messageJson = JSON.toJSONString(message);
        RedisCacheUtil.addZSet(DELAYED_QUEUE_KEY, messageJson, message.getTriggerTime());
    }
}
