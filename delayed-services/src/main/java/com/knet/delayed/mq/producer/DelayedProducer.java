package com.knet.delayed.mq.producer;

import cn.hutool.core.bean.BeanUtil;
import com.knet.common.dto.message.DelayedMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/6/25 17:30
 * @description: 延迟消息生产者
 */
@Slf4j
@Component
public class DelayedProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送延迟消息到交换机
     *
     * @param messageBody 消息体
     */
    public void sendDelayedMessage(DelayedMessage messageBody) {
        String messageId = messageBody.getId();
        // 构建消息属性
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", messageBody.getTargetRoutingKey());
        properties.setHeader("messageId", messageBody.getId());
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getPayloadJson().getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                messageBody.getTargetExchange(),
                messageBody.getTargetRoutingKey(),
                message,
                correlationData
        );
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info(messageBody.getTargetRoutingKey() + " 消息到达Broker: {}", messageId);
                    } else {
                        log.error(messageBody.getTargetRoutingKey() + " 消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    //消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error(messageBody.getTargetRoutingKey() + "消息发送异常: {}", ex.getMessage());
                }
        );
    }
}
