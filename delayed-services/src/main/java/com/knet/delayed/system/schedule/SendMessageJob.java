package com.knet.delayed.system.schedule;

import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.DelayedMessage;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.delayed.mq.producer.DelayedProducer;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/6/25 17:46
 * @description: 处理到期延迟消息
 */
@Slf4j
@Component
public class SendMessageJob {

    private static final String DELAYED_QUEUE_KEY = "delayed_queue:zset";
    @Resource
    private DelayedProducer delayedProducer;

    /**
     * 定时任务，处理已到期的延迟消息
     */
    @XxlJob("processExpiredMessages")
    public ReturnT<String> processExpiredMessages() {
        try {
            long now = System.currentTimeMillis();
            // 获取所有已到期的消息
            Set<Object> messages = RedisCacheUtil.getZSet(DELAYED_QUEUE_KEY, now);
            if (messages != null && !messages.isEmpty()) {
                log.info("发现 {} 条到期延迟消息", messages.size());
                for (Object obj : messages) {
                    String jsonStr = (String) obj;
                    DelayedMessage msg = JSON.parseObject(jsonStr, DelayedMessage.class);
                    log.debug("处理消息: id={}, triggerTime={}, now={}", msg.getId(), msg.getTriggerTime(), now);
                    // 发送到 RabbitMQ 目标队列
                    delayedProducer.sendDelayedMessage(msg);
                    // 从 ZSet 中移除已处理消息
                    RedisCacheUtil.removeZSet(DELAYED_QUEUE_KEY, obj);
                }
                log.info("成功处理 {} 条延迟消息", messages.size());
                return ReturnT.SUCCESS;
            } else {
                log.debug("当前没有到期的延迟消息");
                return ReturnT.SUCCESS;
            }
        } catch (Exception e) {
            log.error("处理延迟消息失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "处理延迟消息失败: " + e.getMessage());
        }
    }
}
