package com.knet.delayed;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2025/6/26 15:10
 * @description: 延迟服务 主启动类
 */
@EnableRetry
@EnableCaching
@ComponentScan(basePackages = {"com.knet.delayed", "com.knet.common"})
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class DelayedServiceApplication {
    public static void main(String[] args) {
        //禁用Log4j JNDI
        System.setProperty("log4j2.formatMsgNoLookups", "true");
        SpringApplication.run(DelayedServiceApplication.class, args);
        System.out.println(" 🚀 Delayed Service started successfully!");
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}