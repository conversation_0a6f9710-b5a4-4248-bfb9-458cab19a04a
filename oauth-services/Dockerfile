#1.依赖的环境：
FROM openjdk:17-jdk-slim

# 将工作目录设置为 /app
WORKDIR /app

#2.定义作者信息：
LABEL maintainer="<EMAIL>"

#3.将jar包添加到容器（将jar包存入镜像中）：其他项目参考本文件
ADD ./target/oauth-services-1.0-SNAPSHOT.jar oauth-service.jar

#4.指定这个容器对外暴露的端口号
EXPOSE 7003

# 容器启动命令
CMD ["java", "-Djava.awt.headless=true", "--add-opens", "java.base/java.lang=ALL-UNNAMED", "--add-opens", "java.base/java.util=ALL-UNNAMED", "--add-opens", "java.base/java.time=ALL-UNNAMED", "-jar", "oauth-service.jar"]
