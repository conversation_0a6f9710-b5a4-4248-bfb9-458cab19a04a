package com.knet.oauth.controller;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.RateLimiter;
import com.knet.common.base.HttpResult;
import com.knet.oauth.model.dto.req.ThirdGetTokenRequest;
import com.knet.oauth.model.dto.resp.ThirdGetTokenResponse;
import com.knet.oauth.service.ISysOauthClientService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:29
 * @description: 第三方认证中心对外服务接口
 */
@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "第三方认证中心-对外提供接口", description = "第三方认证中心-对外提供接口")
public class ApiOauthServicesProvider {

    @Resource
    private ISysOauthClientService sysOauthClientService;

    @RateLimiter(key = "third:getToken:", capacity = 5, refillRate = 1)
    @Loggable(value = "第三方获取token")
    @Schema(description = "第三方获取token")
    @PostMapping("/gettoken")
    public HttpResult<ThirdGetTokenResponse> getAccessToken(@Validated @RequestBody ThirdGetTokenRequest request) {
        ThirdGetTokenResponse accessToken = sysOauthClientService.getAccessToken(request);
        return HttpResult.ok(accessToken);
    }
}
