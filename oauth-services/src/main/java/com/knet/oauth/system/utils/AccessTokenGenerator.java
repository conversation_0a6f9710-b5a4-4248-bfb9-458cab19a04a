package com.knet.oauth.system.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import com.knet.oauth.system.config.AuthConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:54
 * @description: AccessTokenGenerator
 */
@Component
public class AccessTokenGenerator {
    @Resource
    private AuthConfig authConfig;

    /**
     * 生成access token
     *
     * @param clientId client id
     * @return access token
     */
    public String generate(String clientId) {
        String rawData = clientId + "|" + System.currentTimeMillis() + "|" + RandomUtil.randomString(8);
        return SecureUtil.hmacSha256(authConfig.getTokenSecret()).digestHex(rawData);
    }
}
