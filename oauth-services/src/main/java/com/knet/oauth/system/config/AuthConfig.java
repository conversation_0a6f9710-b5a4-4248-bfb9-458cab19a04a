package com.knet.oauth.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/3/7 15:13
 * @description: 动态配置
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "authconfig")
public class AuthConfig {
    private String tokenSecret;
    private String bodySecret;
}
