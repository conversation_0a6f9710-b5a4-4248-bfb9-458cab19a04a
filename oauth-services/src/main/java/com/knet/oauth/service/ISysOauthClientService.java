package com.knet.oauth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.oauth.model.dto.req.ThirdGetTokenRequest;
import com.knet.oauth.model.dto.resp.ThirdGetTokenResponse;
import com.knet.oauth.model.entity.SysOauthClient;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:25
 * @description: ISysOauthClientService
 */
public interface ISysOauthClientService extends IService<SysOauthClient> {
    /**
     * 获取token
     *
     * @param request 请求体
     * @return ThirdGetTokenResponse
     */
    ThirdGetTokenResponse getAccessToken(ThirdGetTokenRequest request);
}
