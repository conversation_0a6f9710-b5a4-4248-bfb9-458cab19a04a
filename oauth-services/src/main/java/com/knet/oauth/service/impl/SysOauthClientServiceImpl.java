package com.knet.oauth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.RedisCacheUtil;
import com.knet.oauth.mapper.SysOauthClientMapper;
import com.knet.oauth.model.dto.req.ThirdGetTokenRequest;
import com.knet.oauth.model.dto.resp.ThirdGetTokenResponse;
import com.knet.oauth.model.entity.SysOauthClient;
import com.knet.oauth.service.ISysOauthClientService;
import com.knet.oauth.system.utils.AccessTokenGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.knet.common.constants.SystemConstant.THIRD_API_TOKEN;
import static com.knet.common.constants.SystemConstant.THIRD_API_TOKEN_EXPIRE;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:26
 * @description: 认证实现
 */
@Service
public class SysOauthClientServiceImpl extends ServiceImpl<SysOauthClientMapper, SysOauthClient> implements ISysOauthClientService {
    @Resource
    private AccessTokenGenerator accessTokenGenerator;

    @Override
    public ThirdGetTokenResponse getAccessToken(ThirdGetTokenRequest request) {
        LambdaQueryWrapper<SysOauthClient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysOauthClient::getClientId, request.getClientId())
                .eq(SysOauthClient::getClientSecret, request.getClientSecret());
        SysOauthClient sysOauthClient = this.getOne(queryWrapper);
        if (BeanUtil.isEmpty(sysOauthClient)) {
            throw new ServiceException("clientId or clientSecret is error");
        }
        String key = String.format(THIRD_API_TOKEN, request.getClientId());
        if (BeanUtil.isNotEmpty(RedisCacheUtil.get(key))) {
            long expire = RedisCacheUtil.getExpire(key);
            String accessToken = (String) RedisCacheUtil.get(key);
            return new ThirdGetTokenResponse(accessToken, expire, sysOauthClient.getScope());
        }
        String accessToken = accessTokenGenerator.generate(request.getClientId());
        RedisCacheUtil.set(key, accessToken, THIRD_API_TOKEN_EXPIRE);
        long expireTime = THIRD_API_TOKEN_EXPIRE.longValue();
        return new ThirdGetTokenResponse(accessToken, expireTime, sysOauthClient.getScope());
    }
}
