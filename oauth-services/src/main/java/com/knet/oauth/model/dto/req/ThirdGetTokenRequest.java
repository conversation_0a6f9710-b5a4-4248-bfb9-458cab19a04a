package com.knet.oauth.model.dto.req;

import com.knet.common.base.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:38
 * @description: 第三方获取token请求体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdGetTokenRequest extends BaseRequest {

    @NotBlank(message = "clientId is not null")
    @Schema(description = "clientId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientId;

    @NotBlank(message = "clientSecret is not null")
    @Schema(description = "clientSecret", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientSecret;
}
