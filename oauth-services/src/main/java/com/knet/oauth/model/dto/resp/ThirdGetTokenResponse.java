package com.knet.oauth.model.dto.resp;

import com.knet.common.base.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:40
 * @description: ThirdGetToken响应体
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class ThirdGetTokenResponse extends BaseResponse {

    @Schema(description = "accessToken", requiredMode = Schema.RequiredMode.REQUIRED, example = "string")
    private String accessToken;

    @Schema(description = "expireTime （unit is Second）", requiredMode = Schema.RequiredMode.REQUIRED, example = "7200")
    private Long expireTime;

    @Schema(description = "scope", requiredMode = Schema.RequiredMode.REQUIRED, example = "read,write")
    private String scope;
}
