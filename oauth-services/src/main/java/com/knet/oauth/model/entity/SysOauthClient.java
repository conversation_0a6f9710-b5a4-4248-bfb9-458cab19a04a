package com.knet.oauth.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.knet.common.base.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2025/3/7 14:15
 * @description: oauth_client模型
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Schema(name = "sys_oauth_client", description = "sys_oauth_client模型")
@TableName("sys_oauth_client")
public class SysOauthClient extends BaseEntity {
    @Schema(description = "clientId", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientId;

    @Schema(description = "clientSecret", requiredMode = Schema.RequiredMode.REQUIRED)
    private String clientSecret;

    @Schema(description = "companyName", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companyName;

    @Schema(description = "权限范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "read,write")
    private String scope;
}
